/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  TTCAN
**  Filename        :  TTcan_events.c
**  Created on      :  21-lug-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef  _BUILD_CAN_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "typedefs.h"
#include "TTcan.h"
#include "Task.h"
#include "Tasksdefs.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
#if (CAN_CHC_EN == 1u)
static uint8_T CanCRxExc[MCAN_3_RXBUFF_NUM] = 
{
    CAN_CHC_BUF0_RX_EXC,  CAN_CHC_BUF1_RX_EXC, CAN_CHC_BUF2_RX_EXC, CAN_CHC_BUF3_RX_EXC, CAN_CHC_BUF4_RX_EXC, CAN_CHC_BUF5_RX_EXC,
    CAN_CHC_BUF6_RX_EXC,  CAN_CHC_BUF7_RX_EXC, CAN_CHC_BUF8_RX_EXC, CAN_CHC_BUF9_RX_EXC, CAN_CHC_BUF10_RX_EXC,CAN_CHC_BUF11_RX_EXC,
    CAN_CHC_BUF12_RX_EXC, CAN_CHC_BUF13_RX_EXC, CAN_CHC_BUF14_RX_EXC, CAN_CHC_BUF15_RX_EXC, CAN_CHC_BUF16_RX_EXC, CAN_CHC_BUF17_RX_EXC,
    CAN_CHC_BUF18_RX_EXC, CAN_CHC_BUF19_RX_EXC, CAN_CHC_BUF20_RX_EXC, CAN_CHC_BUF21_RX_EXC, CAN_CHC_BUF22_RX_EXC, CAN_CHC_BUF23_RX_EXC,
    CAN_CHC_BUF24_RX_EXC, CAN_CHC_BUF25_RX_EXC, CAN_CHC_BUF26_RX_EXC, CAN_CHC_BUF27_RX_EXC, CAN_CHC_BUF28_RX_EXC, CAN_CHC_BUF29_RX_EXC,
    CAN_CHC_BUF30_RX_EXC, CAN_CHC_BUF31_RX_EXC, CAN_CHC_BUF32_RX_EXC, CAN_CHC_BUF33_RX_EXC, CAN_CHC_BUF34_RX_EXC, CAN_CHC_BUF35_RX_EXC,
    CAN_CHC_BUF36_RX_EXC, CAN_CHC_BUF37_RX_EXC, CAN_CHC_BUF38_RX_EXC, CAN_CHC_BUF39_RX_EXC, CAN_CHC_BUF40_RX_EXC, CAN_CHC_BUF41_RX_EXC,
    CAN_CHC_BUF42_RX_EXC, CAN_CHC_BUF43_RX_EXC, CAN_CHC_BUF44_RX_EXC, CAN_CHC_BUF45_RX_EXC, CAN_CHC_BUF46_RX_EXC, CAN_CHC_BUF46_RX_EXC,
    CAN_CHC_BUF48_RX_EXC, CAN_CHC_BUF49_RX_EXC, CAN_CHC_BUF50_RX_EXC, CAN_CHC_BUF51_RX_EXC, CAN_CHC_BUF52_RX_EXC, CAN_CHC_BUF53_RX_EXC,
    CAN_CHC_BUF54_RX_EXC, CAN_CHC_BUF55_RX_EXC, CAN_CHC_BUF56_RX_EXC, CAN_CHC_BUF57_RX_EXC, CAN_CHC_BUF58_RX_EXC, CAN_CHC_BUF59_RX_EXC,
    CAN_CHC_BUF60_RX_EXC, CAN_CHC_BUF61_RX_EXC, CAN_CHC_BUF62_RX_EXC, CAN_CHC_BUF63_RX_EXC
};
#endif

extern CanUsedMb_T TTCanUsedMb;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : CAN_CHC_L0
**
**   Description:
**    ISR handler for interrupt line 0 on MCAN_3 (M_TTCAN_0).
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CAN_CHC_L0(void) 
{
#if (CAN_CHC_EN == 1u)
    uint8_T rxBuf = 0u;
    uint8_T canLowMbUsed = TTCanUsedMb.rxLowMbUsed;
    uint8_T canHighMbUsed = TTCanUsedMb.rxHighMbUsed;

#if (TTCAN_BOFF_ISR_ENABLE == 1u)
#if TTCAN_BOFF_INT_LINE == CAN_LINE0_INT

    if (M_TTCAN_0.IR.B.BO == TTCAN_BUSOFF_NOT_OCCURRED) 
    { /* Check Bus-Off state */
#endif
#endif

#ifdef MCAN_3_RX_INT_LINE
#if MCAN_3_RX_INT_LINE == CAN_LINE0_INT
#ifdef _BUILD_SAFETYMNGR_INTC_
        /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
        SafetyMngr_INTC_CheckCtx(M_TTCAN_LINE0_ISR_POS); // Interrupt no. 677
#endif // _BUILD_SAFETYMNGR_INTC_


        /* check if Rx buffer interrupt occurred on line 0 */
        if (M_TTCAN_0.IR.B.DRX == TTCAN_RX_ISR_OCCURRED) 
        {
            if (M_TTCAN_0.ILS.B.DRXL == CAN_LINE0_INT) 
            {
                for (rxBuf = 0u; rxBuf < canLowMbUsed; rxBuf++) 
                {
                    if (((M_TTCAN_0.NDAT1.R) & (1UL << rxBuf)) != 0U) 
                    {
                        TTCAN_RxData_ISR(MCAN_ENG_C, rxBuf);
                        M_TTCAN_0.NDAT1.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHC_EN == 1u)
#if (CAN_CHC_TXRX_EXC)
                        if (CanCRxExc[rxBuf] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChCID);
                        }
#endif
#endif
                    }
                }

                for (rxBuf = 0u; rxBuf < canHighMbUsed; rxBuf++) 
                {
                    if (((M_TTCAN_0.NDAT2.R) & (1UL << rxBuf)) != 0U) 
                    {
                        TTCAN_RxData_ISR(MCAN_ENG_C, rxBuf + TTCAN_RXB_NDAT1_NUMBUF);
                        M_TTCAN_0.NDAT2.R = (vuint32_t)(1UL << rxBuf);

#if (CAN_CHC_EN == 1u)
#if (CAN_CHC_TXRX_EXC)
                        if (CanCRxExc[rxBuf+ TTCAN_RXB_NDAT1_NUMBUF] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChCID);
                        }
#endif
#endif
                    }
                }
            }

            /* clear flag */
            M_TTCAN_0.IR.R = CAN_IR_DRX;
        }
#endif
#endif

#if (TTCAN_BOFF_ISR_ENABLE == 1u)
#if TTCAN_BOFF_INT_LINE == CAN_LINE0_INT
    }
    else //Bus-Off state
    {
        TTCAN_BusOffRecovery(MCAN_ENG_C);
    }
#endif
#endif
#endif
}

/******************************************************************************
**   Function    : CAN_CHC_L1
**
**   Description:
**    ISR handler for interrupt line 1 on MCAN_3 (M_TTCAN_0).
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CAN_CHC_L1(void) 
{
#if (CAN_CHC_EN == 1u)
    uint8_T rxBuf = 0u;
    uint8_T canLowMbUsed = TTCanUsedMb.rxLowMbUsed;
    uint8_T canHighMbUsed = TTCanUsedMb.rxHighMbUsed;

#if (TTCAN_BOFF_ISR_ENABLE == 1u)
#if TTCAN_BOFF_INT_LINE == CAN_LINE1_INT

    /* Check Bus-Off state */
    if (M_TTCAN_0.IR.B.BO == TTCAN_BUSOFF_NOT_OCCURRED) 
    { 
#endif
#endif

#ifdef MCAN_3_RX_INT_LINE
#if MCAN_3_RX_INT_LINE == CAN_LINE1_INT

#ifdef _BUILD_SAFETYMNGR_INTC_
        /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
        SafetyMngr_INTC_CheckCtx(M_TTCAN_LINE1_ISR_POS); // Interrupt no. 678
#endif // _BUILD_SAFETYMNGR_INTC_

        /* check if Rx buffer interrupt occurred on line 0 */
        if (M_TTCAN_0.IR.B.DRX == TTCAN_RX_ISR_OCCURRED) 
        {
            if (M_TTCAN_0.ILS.B.DRXL == CAN_LINE1_INT) 
            {
                for (rxBuf = 0u; rxBuf < canLowMbUsed; rxBuf++) 
                {
                    if (((M_TTCAN_0.NDAT1.R) & (1UL << rxBuf)) != 0U) 
                    {
                        TTCAN_RxData_ISR(MCAN_ENG_C, rxBuf);
                        M_TTCAN_0.NDAT1.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHC_EN == 1u)
#if (CAN_CHC_TXRX_EXC)
                        if (CanCRxExc[rxBuf] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChCID);
                        }
#endif
#endif
                    }
                }

                for (rxBuf = 0u; rxBuf < canHighMbUsed; rxBuf++) 
                {
                    if (((M_TTCAN_0.NDAT2.R) & (1UL << rxBuf)) != 0U) 
                    {
                        TTCAN_RxData_ISR(MCAN_ENG_C, rxBuf + TTCAN_RXB_NDAT1_NUMBUF);
                        M_TTCAN_0.NDAT2.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHC_EN == 1u)
#if (CAN_CHC_TXRX_EXC)
                        if (CanCRxExc[rxBuf+ TTCAN_RXB_NDAT1_NUMBUF] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChCID);
                        }
#endif
#endif
                    }
                }
            }


            /* clear flag */
            M_TTCAN_0.IR.R = CAN_IR_DRX;
        }
#endif
#endif 

#if (TTCAN_BOFF_ISR_ENABLE == 1u)
#if TTCAN_BOFF_INT_LINE == CAN_LINE1_INT
    }
    else //Bus-Off state
    {
        TTCAN_BusOffRecovery(MCAN_ENG_C);
    }
#endif
#endif
#endif
}

#if defined (CAN_CHC_EN) 
#if (CAN_CHC_TXRX_EXC)
/******************************************************************************
**   Function    : FuncCAN_ExTxDoneChC
**
**   Description:
**    Exception task for TTCAN transmission
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void FuncCAN_ExTxDoneChC (void)
{
    /*
    This exception will be generated
    at the end of CAN data transmission
    of the TTCAN (MCAN 3)
     */
    if(ExTxDoneChC != 0u)
    {
        ExTxDoneChC();
    }
    TerminateTask();
}

/******************************************************************************
**   Function    : FuncCAN_ExRxDoneChC
**
**   Description:
**    Exception task for TTCAN reception
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void FuncCAN_ExRxDoneChC (void)
{
    /*
    This exception will be generated
    at the end of CAN data reception
    of the TTCAN (MCAN 3)
     */
    if(ExRxDoneChC != 0u)
    {
        ExRxDoneChC();
    }
    TerminateTask();
}
#endif /* #if (CAN_CHC_TXRX_EXC) */
#endif /* #if defined CAN_CHC_EN */

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/


#endif /* _BUILD_CAN_ */

/****************************************************************************
 ****************************************************************************/
