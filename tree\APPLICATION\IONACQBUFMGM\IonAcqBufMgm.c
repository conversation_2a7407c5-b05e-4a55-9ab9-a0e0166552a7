/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonAcqBufMgm.c
 **  File Creation Date: 21-Jan-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonAcqBufMgm
 **  Model Description:  This model performs several functionalities:
   - latch some important signals at EOA task
   - calcuate some signals used to detect ion phases on ion signal buffer
   - manage the copy acquired ion signal into ion signal buffers
   It provides two function call for End Of Acquisition and Power On event.
 **  Model Version:      1.1794
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Fri Jan 21 16:07:03 2022
 **
 **  Last Saved Modification:  RoccaG - Fri Jan 21 16:06:14 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonAcqBufMgm_out.h"
#include "IonAcqBufMgm_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/Task_Supervisior' */
#define IonAcqBufMgm_event_ev_EOA      (1)
#define IonAcqBufMgm_event_ev_PowerOn  (0)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKIONSPKRPM_dim                7U                        /* Referenced by:
                                                                  * '<S17>/Constant1'
                                                                  * '<S17>/Constant10'
                                                                  * '<S17>/Constant11'
                                                                  * '<S17>/Constant17'
                                                                  * '<S17>/Constant2'
                                                                  * '<S17>/Constant3'
                                                                  * '<S17>/Constant4'
                                                                  * '<S17>/Constant8'
                                                                  * '<S17>/Constant9'
                                                                  */

/* BKIONSPKRPM breakpoint dimension. */
#define ID_VER_IONACQBUFMGM_DEF        11790U                    /* Referenced by: '<Root>/Task_Supervisior' */

/* Model Version. */
#define LAST_SAMPLE_ID                 799U                      /* Referenced by: '<S18>/Constant' */

/* Last id into ion buffer (according to zero based notation) */
#define MAX_INT16                      32767                     /* Referenced by:
                                                                  * '<S50>/Constant1'
                                                                  * '<S17>/Constant18'
                                                                  */

/* Max value for int16 type. */
#define MAX_ION_GAIN                   53248U                    /* Referenced by:
                                                                  * '<S44>/Constant5'
                                                                  * '<S45>/Constant4'
                                                                  * '<S47>/Constant4'
                                                                  */

/* Max allowed value for IonGainEOA */
#define MAX_UINT8                      255U                      /* Referenced by:
                                                                  * '<S50>/Constant'
                                                                  * '<S50>/Constant3'
                                                                  */

/* Max value for uint8 type. */
#define MIN_INT16                      -32768                    /* Referenced by: '<S17>/Constant19' */

/* Min value for int16 type. */
#define MIN_IONDT                      8U                        /* Referenced by: '<S36>/Constant2' */

/* Minimum value for IonDTEOA */
#define MIN_VCHARGE                    1                         /* Referenced by:
                                                                  * '<S44>/Constant3'
                                                                  * '<S45>/Constant2'
                                                                  * '<S47>/Constant2'
                                                                  */

/* Min allowed value for VCharge to protect from zero division */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONACQBUFMGM_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinders!
#endif

#if (DIAG_NUMBER != 96)
#error This code was generated with a different number of diagnosis!
#endif

#if (MAX_SAMPLE != 800)
#error This code was generated with a different number of ion samples!
#endif

#if (MAX_ION_SIGNALS != 2)
#error This code was generated with a different number of plot channels!
#endif

#if (N_CIR_MAX != 8)
#error This code was generated with a different number of ion acquisition circuits!
#endif

#if (N_CH_MAX != 4)
#error This code was generated with a different number of ion channels!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint8_T IonAcqBufMgm_ptFault;   /* '<S1>/BufferCopy' */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKIONSPKRPM[8] = { 700U, 1000U, 2000U,
  3000U, 4000U, 5000U, 6000U, 7000U } ;/* Referenced by: '<S17>/Constant' */

/* Rpm breakpoint vector for spike detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T DMAIDXSPKDETOFFSET = 0;/* Referenced by: '<S18>/Constant1' */

/* DMA index Offset, use to offset spike start search index */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENIONGAINCOMP = 3U;/* Referenced by: '<S41>/Constant' */

/* Enable IonGain compensation (0)disabled, (1)VChargeObjNom/Vcharge, (2)VChargeObjNom/VChargeObj, (3)VCHARGEREF)/VChargeObj */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENSPIKEDETECT = 1U;/* Referenced by:
                                                             * '<Root>/Constant'
                                                             * '<S1>/Constant2'
                                                             */

/* Enable Spike detector (=0 OFF; =1 ON; =2 Erase) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T HYSTLOADSPIKEANG = 640U;/* Referenced by: '<S16>/Constant1' */

/* Load hyst for VTSPIKEANGLEHL/LL */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T ION_GAIN[8] = { 410U, 819U, 1212U,
  1622U, 2118U, 2527U, 2920U, 3330U } ;/* Referenced by:
                                        * '<S44>/Constant1'
                                        * '<S45>/Constant1'
                                        * '<S46>/Constant1'
                                        * '<S47>/Constant1'
                                        */

/* Ion Conversion Coefficient (from mV to uA) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SPIKEANGGASPOSTHR = 80U;/* Referenced by: '<S16>/Constant5' */

/* Threshold used to switch to VTSPIKEANGLELL vector in case GasPos goes below the thr */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T SPIKEIDDMAEN = 0U;/* Referenced by: '<S18>/Constant2' */

/* Enable start search of ion spike from DMA index */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBVCHARGEREF[90] = { 25600U, 25600U,
  25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U,
  25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U,
  25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U,
  25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U,
  25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U,
  25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U,
  25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U,
  25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U,
  25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U, 25600U } ;/* Referenced by: '<S47>/Constant6' */

/* ION Charge Reverence */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRLOADSPIKEANG = 4480U;/* Referenced by:
                                                                   * '<S16>/Constant'
                                                                   * '<S16>/Constant3'
                                                                   */

/* Load thr for VTSPIKEANGLEHL/LL */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VIONMAX = 15892U;/* Referenced by: '<S1>/Constant1' */

/* Maximum value of IonBufferV for diagnosis */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VIONMIN = 492U;/* Referenced by: '<S1>/Constant' */

/* Minimum value of IonBufferV for diagnosis */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTAFTERSPIKETIME[8] = { 338U, 338U,
  297U, 255U, 247U, 209U, 190U, 171U } ;/* Referenced by: '<S17>/Constant13' */

/* Time after spike for ion signal interpolation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTBEFORESPIKETIME[8] = { 104U, 104U,
  78U, 51U, 39U, 33U, 30U, 30U } ;     /* Referenced by: '<S17>/Constant12' */

/* Time before spike for ion signal interpolation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTFORCEDIONBUFFER[800] = { 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U } ;/* Referenced by: '<S1>/Constant3' */

/* FORCED buffer values in Volts */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTFORCEDIONGAIN[8] = { 64U, 64U, 64U,
  64U, 64U, 64U, 64U, 64U } ;          /* Referenced by: '<S1>/Constant4' */

/* Forced Ion Gain */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTFORCEIONBUFFER[8] = { 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U } ;                   /* Referenced by: '<S1>/Constant5' */

/* Enable flag to force IonBufferV values */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTMINDIONTHRH[8] = { 841U, 705U, 570U,
  461U, 353U, 298U, 298U, 298U } ;     /* Referenced by: '<S17>/Constant14' */

/* Min value for DionMax to detect spike */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTSPIKEANGDIFF[8] = { 480U, 480U, 544U,
  624U, 720U, 816U, 1120U, 1232U } ;   /* Referenced by: '<S17>/Constant5' */

/* Min angular distance between high and low spike edges */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTSPIKEANGLEHL[8] = { 384U, 512U, 960U,
  1280U, 1728U, 2176U, 2560U, 3520U } ;/* Referenced by: '<S17>/Constant15' */

/* Angle offset for spike detector (Load above thr) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTSPIKEANGLELL[8] = { 12800U, 12800U,
  12800U, 12800U, 12800U, 12800U, 12800U, 12800U } ;/* Referenced by: '<S17>/Constant16' */

/* Angle offset for spike detector (Load below thr) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTSPIKEDIONTHRH[8] = { 15394U, 9038U,
  3650U, 1970U, 1130U, 765U, 637U, 546U } ;/* Referenced by: '<S17>/Constant6' */

/* Max threshold for spike detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTSPIKEDIONTHRL[8] = { -15938, -9357,
  -3779, -2039, -1170, -792, -660, -566 } ;/* Referenced by: '<S17>/Constant7' */

/* Min threshold for spike detection */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T DwellSampCyl[8];               /* '<S52>/Merge12' */

/* Number of samples corresponding to dwell time */
uint8_T IonAbsTdcEOA;                  /* '<S38>/Merge4' */

/* Cylinder for last acquisition - task variable */
uint8_T IonDTEOA;                      /* '<S38>/Merge1' */

/* Ion Acquisition step time - task variable */
uint16_T IonDThetaEOA;                 /* '<S38>/Merge12' */

/* Ion Acquisition step angle - task variable */
uint16_T IonDThetaInvEOA;              /* '<S38>/Merge9' */

/* Inverse for ion Acquisition step angle - task variable */
uint16_T IonGainEOA;                   /* '<S38>/Merge5' */

/* IonGain - task variable */
uint8_T KnockAbsCycles;                /* '<S38>/Merge7' */

/* Absolute engine cycle counter for knock analysis */
uint16_T NSampIonSpike;                /* '<S9>/Merge11' */

/* NSampIonSpike */
uint16_T NSampleMaxEOA;                /* '<S38>/Merge2' */

/* Ion Acquisition Max Sample Counter - task variable */
uint16_T NSampleStartEOA;              /* '<S38>/Merge3' */

/* Ion Acquisition Start Sample Counter - task variable */
uint16_T NrStartIonDMAIdxEOA;          /* '<S38>/Merge6' */

/* Near start ion index */
uint8_T PtFaultChannel[8];             /* '<S9>/Merge2' */

/* Ion channels punctual fault. */
uint16_T StartSpark;                   /* '<S52>/Merge1' */

/* StartSpark sample index */
uint16_T StopOffsetSearch;             /* '<S52>/Merge3' */

/* Stop offset search sample index */
uint16_T VtStartSpark[8];              /* '<S52>/Merge2' */

/* Sample index corresponding to the Spark start */
uint8_T VtStopOffsetSearch[8];         /* '<S52>/Merge4' */

/* Sample index corresponding to the Stop offset search */

/*Memory section for Output interface using special allocation*/
#include "out_map_sec_init.h"

/* Definition for custom storage class: ELD_OUT_INTERFACE_MAP */
uint32_T IonBuffer[800];               /* '<S9>/Merge1' */

/* Ion current Buffer */
uint16_T IonBufferV[800];              /* '<S9>/Merge12' */

/* Ion Current Voltage Buffer */
#include "out_map_sec_end.h"

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T AfterSpikeInt;/* '<S9>/Merge6' */

/* Index after spike */
STATIC_TEST_POINT int32_T AngDiff;     /* '<S9>/Merge13' */

/* Ion angular difference */
STATIC_TEST_POINT uint16_T BeforeSpikeInt;/* '<S9>/Merge4' */

/* Index before spike */
STATIC_TEST_POINT int32_T DeltaIonAng; /* '<S9>/Merge10' */

/* Delta angular ion */
STATIC_TEST_POINT int32_T DeltaIonMax; /* '<S9>/Merge5' */

/* Max delta ion */
STATIC_TEST_POINT int32_T DeltaIonMaxAng;/* '<S9>/Merge9' */

/* Max delta angular ion */
STATIC_TEST_POINT uint16_T DeltaIonMaxId;/* '<S9>/Merge14' */

/* Max delta ion index */
STATIC_TEST_POINT int32_T DeltaIonMin; /* '<S9>/Merge8' */

/* Min delta ion */
STATIC_TEST_POINT uint16_T DeltaIonMinId;/* '<S9>/Merge15' */

/* Min delta ion index */
STATIC_TEST_POINT uint16_T DwellSamp;  /* '<S52>/Merge11' */

/* Number of samples corresponding to dwell time */
STATIC_TEST_POINT uint8_T FirstKnockAbsTdc;/* '<S38>/Merge8' */

/* First cylinder detected for knock cycle counter */
STATIC_TEST_POINT uint8_T FlgSpikeFound;/* '<S9>/Merge3' */

/* Spike found in ion buffer */
STATIC_TEST_POINT uint32_T IdVer_IonAcqBufMgm;/* '<Root>/Task_Supervisior' */

/* Model Version */
STATIC_TEST_POINT boolean_T SpikeAngleSel;/* '<S9>/Merge16' */

/* Selected table for start index of spike detection algorithm: 0 Low table, 1 High table */
STATIC_TEST_POINT uint16_T StartSpkId; /* '<S9>/Merge7' */

/* StartSpkId */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void IonAc_chartstep_c3_IonAcqBufMgm(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/
#pragma ghs section text=".vletext_c0"
/* Function for Chart: '<Root>/Task_Supervisior' */
static void IonAc_chartstep_c3_IonAcqBufMgm(const int32_T *sfEvent)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_LookUp_IR_U16_f;
  uint16_T rtb_LookUp_IR_U16_p;
  uint16_T rtb_LookUp_IR_U16_k;
  uint16_T rtb_LookUp_IR_U16_d;
  uint16_T rtb_LookUp_IR_U16_m;
  uint16_T rtb_LookUp_IR_U16_g;
  uint16_T rtb_Look2D_U16_U16_U16;
  int16_T rtb_LookUp_IR_S16;
  uint8_T rtb_DiagMgm_SetDiagState;
  uint16_T ionVoltage;
  uint8_T rtb_IonAbsTdcEOA;
  int8_T rtAction;
  uint8_T rtb_FirstKnockAbsTdc_p;
  uint8_T rtb_KnockAbsCycles_o;
  uint8_T rtb_CCaller_o1_mi;
  uint8_T rtb_CCaller_o2_b;
  uint16_T rtb_NSampleMaxEOA;
  uint32_T rtb_Product_j;
  int16_T rtb_IndexVector2_mh;
  uint16_T rtb_IndexVector2_a;
  uint16_T rtb_Conversion1_f;
  int32_T Add;
  int32_T Divide;
  int32_T Add2_p;
  int32_T Add2;
  int32_T u0;

  /* Chart: '<Root>/Task_Supervisior'
   *
   * Block description for '<Root>/Task_Supervisior':
   *  This chart schedules IonAcqBufMgm functionalities according to PowerOn
   *  and EOA tasks.
   */
  /* Chart: '<Root>/Task_Supervisior' incorporates:
   *  Constant: '<Root>/Constant'
   *  Constant: '<S1>/Constant2'
   *  DataTypeConversion: '<S17>/DataTypeConversion'
   *  DataTypeConversion: '<S31>/Conversion'
   *  DataTypeConversion: '<S32>/Conversion'
   *  Inport: '<Root>/VtStPlasObjDBuff'
   *
   * Block description for '<Root>/Task_Supervisior':
   *  This chart schedules IonAcqBufMgm functionalities according to PowerOn
   *  and EOA tasks.
   */
  /* During: Task_Supervisior */
  /* This chart schedules IonAcqBufMgm functionalities according to PowerOn and EOA tasks. No requirement links needed. */
  /* Entry Internal: Task_Supervisior */
  /* Transition: '<S4>:44' */
  if ((*sfEvent) == ((int32_T)IonAcqBufMgm_event_ev_PowerOn)) {
    /* Outputs for Function Call SubSystem: '<S2>/LatchInit'
     *
     * Block description for '<S2>/LatchInit':
     *  This block provides initialization for latched signals at task EOA.
     */
    /* SignalConversion generated from: '<S36>/IonDThetaEOA' incorporates:
     *  Constant: '<S36>/Constant8'
     */
    /* Transition: '<S4>:6' */
    /* Transition: '<S4>:10' */
    /* Event: '<S4>:21' */
    IonDThetaEOA = 0U;

    /* SignalConversion generated from: '<S36>/IonDThetaInvEOA' incorporates:
     *  Constant: '<S36>/Constant1'
     */
    IonDThetaInvEOA = 0U;

    /* SignalConversion generated from: '<S36>/IonDTEOA' incorporates:
     *  Constant: '<S36>/Constant2'
     */
    IonDTEOA = ((uint8_T)MIN_IONDT);

    /* SignalConversion generated from: '<S36>/NSampleMaxEOA' incorporates:
     *  Constant: '<S36>/Constant3'
     */
    NSampleMaxEOA = 0U;

    /* SignalConversion generated from: '<S36>/NSampleStartEOA' incorporates:
     *  Constant: '<S36>/Constant4'
     */
    NSampleStartEOA = 0U;

    /* SignalConversion generated from: '<S36>/IonAbsTdcEOA' incorporates:
     *  Constant: '<S36>/Constant5'
     */
    IonAbsTdcEOA = 0U;

    /* SignalConversion generated from: '<S36>/IonGainEOA' incorporates:
     *  Constant: '<S36>/Constant7'
     */
    IonGainEOA = 4096U;

    /* SignalConversion generated from: '<S36>/NrStartIonDMAIdxEOA' incorporates:
     *  Constant: '<S36>/Constant6'
     */
    NrStartIonDMAIdxEOA = 0U;

    /* SignalConversion generated from: '<S36>/KnockAbsCycles' incorporates:
     *  Constant: '<S36>/Constant10'
     */
    KnockAbsCycles = 0U;

    /* SignalConversion generated from: '<S36>/FirstKnockAbsTdc' incorporates:
     *  Constant: '<S36>/Constant9'
     */
    FirstKnockAbsTdc = 0U;

    /* End of Outputs for SubSystem: '<S2>/LatchInit' */

    /* Outputs for Function Call SubSystem: '<S3>/PhaseParamsInit'
     *
     * Block description for '<S3>/PhaseParamsInit':
     *  This block provides the initialization values for the signals used to
     *  detect ion phases.
     */
    /* SignalConversion generated from: '<S51>/DwellSampCyl' */
    /* Event: '<S4>:39' */
    memset((&(DwellSampCyl[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S51>/VtStartSpark' */
    memset((&(VtStartSpark[0])), 0, (sizeof(uint16_T)) << 3U);

    /* SignalConversion generated from: '<S51>/VtStopOffsetSearch' */
    memset((&(VtStopOffsetSearch[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S51>/DwellSamp' incorporates:
     *  Constant: '<S51>/Constant'
     */
    DwellSamp = 0U;

    /* SignalConversion generated from: '<S51>/StartSpark' incorporates:
     *  Constant: '<S51>/Constant2'
     */
    StartSpark = 0U;

    /* SignalConversion generated from: '<S51>/StopOffsetSearch' incorporates:
     *  Constant: '<S51>/Constant4'
     */
    StopOffsetSearch = 0U;

    /* End of Outputs for SubSystem: '<S3>/PhaseParamsInit' */

    /* Outputs for Function Call SubSystem: '<S1>/BufferInit'
     *
     * Block description for '<S1>/BufferInit':
     *  This block provides initialization of ion signal buffers and test
     *  point signals used for buffer construction.
     */
    /* SignalConversion generated from: '<S6>/IonBufferV' */
    /* Event: '<S4>:22' */
    memset((&(IonBufferV[0])), 0, 800U * (sizeof(uint16_T)));

    /* SignalConversion generated from: '<S6>/IonBuffer' */
    memset((&(IonBuffer[0])), 0, 800U * (sizeof(uint32_T)));

    /* SignalConversion generated from: '<S6>/PtFaultChannel' */
    memset((&(PtFaultChannel[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S6>/NSampIonSpike' incorporates:
     *  Constant: '<S6>/Constant'
     */
    NSampIonSpike = 0U;

    /* SignalConversion generated from: '<S6>/FlgSpikeFound' incorporates:
     *  Constant: '<S6>/Constant4'
     */
    FlgSpikeFound = 0U;

    /* SignalConversion generated from: '<S6>/BeforeSpikeInt' incorporates:
     *  Constant: '<S6>/Constant5'
     */
    BeforeSpikeInt = 0U;

    /* SignalConversion generated from: '<S6>/AfterSpikeInt' incorporates:
     *  Constant: '<S6>/Constant6'
     */
    AfterSpikeInt = 0U;

    /* SignalConversion generated from: '<S6>/StartSpkId' incorporates:
     *  Constant: '<S6>/Constant7'
     */
    StartSpkId = 0U;

    /* SignalConversion generated from: '<S6>/DeltaIonMax' incorporates:
     *  Constant: '<S6>/Constant8'
     */
    DeltaIonMax = 0;

    /* SignalConversion generated from: '<S6>/DeltaIonMin' incorporates:
     *  Constant: '<S6>/Constant9'
     */
    DeltaIonMin = 0;

    /* SignalConversion generated from: '<S6>/DeltaIonMaxAng' incorporates:
     *  Constant: '<S6>/Constant10'
     */
    DeltaIonMaxAng = 0;

    /* SignalConversion generated from: '<S6>/DeltaIonAng' incorporates:
     *  Constant: '<S6>/Constant11'
     */
    DeltaIonAng = 0;

    /* SignalConversion generated from: '<S6>/DeltaIonMaxId' incorporates:
     *  Constant: '<S6>/Constant12'
     */
    DeltaIonMaxId = 0U;

    /* SignalConversion generated from: '<S6>/DeltaIonMinId' incorporates:
     *  Constant: '<S6>/Constant13'
     */
    DeltaIonMinId = 0U;

    /* SignalConversion generated from: '<S6>/AngDiff' incorporates:
     *  Constant: '<S6>/Constant14'
     */
    AngDiff = 0;

    /* SignalConversion generated from: '<S6>/SpikeAngleSel' incorporates:
     *  Constant: '<S6>/Constant15'
     */
    SpikeAngleSel = false;

    /* End of Outputs for SubSystem: '<S1>/BufferInit' */
    IdVer_IonAcqBufMgm = ID_VER_IONACQBUFMGM_DEF;
  } else {
    /* Outputs for Function Call SubSystem: '<S2>/SignalLatch'
     *
     * Block description for '<S2>/SignalLatch':
     *  This subsystem performs signals latching at EOA event. The selected signal to be freezed at EOA are:
     *  - acquisition sampling time.
     *  - acquisition sampling angle.
     *  - acquisition channel.
     *  - number of acquired samples for ion signal.
     *  - conversion factor from mV to uA for ion signal.
     *  - current cylinder.
     */
    /* MultiPortSwitch: '<S42>/IndexVector4' incorporates:
     *  Inport: '<Root>/AbsIonTdc'
     *  Inport: '<Root>/IonChannelEOA'
     */
    /* Transition: '<S4>:7' */
    /*  ev_EOA  */
    /* Event: '<S4>:25' */
    rtb_IonAbsTdcEOA = AbsIonTdc[(IonChannelEOA)];

    /* Chart: '<S39>/CounterMgm' incorporates:
     *  Inport: '<Root>/AbsIonTdc'
     *  Inport: '<Root>/CntAbsTdc'
     *  Inport: '<Root>/IonChannelEOA'
     *  MultiPortSwitch: '<S42>/IndexVector4'
     *  SignalConversion generated from: '<S37>/FirstKnockAbsTdc_old'
     *  SignalConversion generated from: '<S37>/KnockAbsCycles_old'
     */
    /* Gateway: Latch_Subsystem/SignalLatch/Counters/CounterMgm */
    /* During: Latch_Subsystem/SignalLatch/Counters/CounterMgm */
    /* This block calculates a counter of tdc events and the id of the first cylinder evaluated. */
    /* Entry Internal: Latch_Subsystem/SignalLatch/Counters/CounterMgm */
    /* Transition: '<S43>:10' */
    /*  Assign old value to each output to improve code generation  */
    rtb_FirstKnockAbsTdc_p = FirstKnockAbsTdc;
    rtb_KnockAbsCycles_o = KnockAbsCycles;

    /*  Knock cycle counter management  */
    if (((CntAbsTdc >= 1U) && (((int32_T)AbsIonTdc[(IonChannelEOA)]) == 0)) &&
        (((int32_T)FirstKnockAbsTdc) == 0)) {
      /* Transition: '<S43>:21' */
      /* Transition: '<S43>:23' */
      rtb_FirstKnockAbsTdc_p = 1U;
    } else {
      /* Transition: '<S43>:25' */
      if ((((int32_T)AbsIonTdc[(IonChannelEOA)]) == 0) && (((int32_T)
            FirstKnockAbsTdc) == 1)) {
        /* Transition: '<S43>:27' */
        /* Transition: '<S43>:29' */
        /*  Cyclic counter  */
        if (((int32_T)KnockAbsCycles) == 255) {
          /* Transition: '<S43>:34' */
          /* Transition: '<S43>:35' */
          rtb_KnockAbsCycles_o = 0U;

          /* Transition: '<S43>:39' */
        } else {
          /* Transition: '<S43>:36' */
          rtb_KnockAbsCycles_o = (uint8_T)((int32_T)(((int32_T)KnockAbsCycles) +
            1));
        }

        /* Transition: '<S43>:41' */
      } else {
        /* Transition: '<S43>:31' */
      }
    }

    /* End of Chart: '<S39>/CounterMgm' */

    /* MultiPortSwitch: '<S42>/IndexVector2' incorporates:
     *  Inport: '<Root>/IonChannelEOA'
     *  Inport: '<Root>/NSampleMaxPrg'
     */
    rtb_NSampleMaxEOA = NSampleMaxPrg[(IonChannelEOA)];

    /* Sum: '<S40>/Add' incorporates:
     *  Inport: '<Root>/AbsIonTdc'
     *  Inport: '<Root>/IonChannelEOA'
     *  Inport: '<Root>/NSampleMaxPrg'
     *  Inport: '<Root>/VtNrStartIonDMAIdx'
     *  MultiPortSwitch: '<S40>/IndexVector5'
     *  MultiPortSwitch: '<S42>/IndexVector2'
     *  MultiPortSwitch: '<S42>/IndexVector4'
     */
    Add = ((int32_T)NSampleMaxPrg[(IonChannelEOA)]) - ((int32_T)
      VtNrStartIonDMAIdx[(AbsIonTdc[(IonChannelEOA)])]);

    /* MinMax: '<S40>/MinMax' */
    if (Add > 0) {
      /* DataTypeConversion: '<S40>/Conversion' */
      NrStartIonDMAIdxEOA = (uint16_T)Add;
    } else {
      /* DataTypeConversion: '<S40>/Conversion' */
      NrStartIonDMAIdxEOA = 0U;
    }

    /* End of MinMax: '<S40>/MinMax' */

    /* SwitchCase: '<S41>/Switch Case' incorporates:
     *  Constant: '<S41>/Constant'
     */
    switch (ENIONGAINCOMP) {
     case 1:
      rtAction = 0;
      break;

     case 2:
      rtAction = 1;
      break;

     case 3:
      rtAction = 2;
      break;

     default:
      rtAction = 3;
      break;
    }

    switch (rtAction) {
     case 0:
      /* Outputs for IfAction SubSystem: '<S41>/SwitchCaseActionSubsystem' incorporates:
       *  ActionPort: '<S44>/Action Port'
       *
       * Block description for '<S41>/SwitchCaseActionSubsystem':
       *  Conversion factor from mV to uA is calculated with a compensation
       *  obtained as the ratio between the nominal and the real value of
       *  capacitor charge.
       */
      /* Product: '<S44>/Product' incorporates:
       *  Constant: '<S44>/Constant1'
       *  Inport: '<Root>/AbsIonTdc'
       *  Inport: '<Root>/IonChannelEOA'
       *  Inport: '<Root>/IonSelect'
       *  Inport: '<Root>/VChargeObjNom'
       *  MultiPortSwitch: '<S42>/IndexVector4'
       *  MultiPortSwitch: '<S44>/IndexVector1'
       *  MultiPortSwitch: '<S44>/IndexVector4'
       */
      rtb_Product_j = ((uint32_T)ION_GAIN[(IonSelect[(AbsIonTdc[(IonChannelEOA)])])])
        * ((uint32_T)VChargeObjNom);

      /* CCaller: '<S44>/C Caller' incorporates:
       *  Inport: '<Root>/AbsIonTdc'
       *  Inport: '<Root>/IonChannelEOA'
       *  MultiPortSwitch: '<S42>/IndexVector4'
       */
      IonAcq_GetCirIdx(AbsIonTdc[(IonChannelEOA)], &rtb_CCaller_o1_mi,
                       &rtb_CCaller_o2_b);

      /* MultiPortSwitch: '<S44>/IndexVector2' incorporates:
       *  Inport: '<Root>/VCharge'
       */
      rtb_IndexVector2_mh = VCharge[(rtb_CCaller_o2_b)];

      /* MinMax: '<S44>/MinMax' incorporates:
       *  Constant: '<S44>/Constant3'
       */
      if (rtb_IndexVector2_mh <= ((int16_T)MIN_VCHARGE)) {
        rtb_IndexVector2_mh = ((int16_T)MIN_VCHARGE);
      }

      /* End of MinMax: '<S44>/MinMax' */

      /* Product: '<S44>/Divide1' */
      rtb_Product_j /= (uint32_T)rtb_IndexVector2_mh;

      /* MinMax: '<S44>/MinMax1' incorporates:
       *  Constant: '<S44>/Constant5'
       */
      if (rtb_Product_j < MAX_ION_GAIN) {
        /* DataTypeConversion: '<S44>/Conversion' */
        IonGainEOA = (uint16_T)rtb_Product_j;
      } else {
        /* DataTypeConversion: '<S44>/Conversion' */
        IonGainEOA = (uint16_T)MAX_ION_GAIN;
      }

      /* End of MinMax: '<S44>/MinMax1' */
      /* End of Outputs for SubSystem: '<S41>/SwitchCaseActionSubsystem' */
      break;

     case 1:
      /* Outputs for IfAction SubSystem: '<S41>/SwitchCaseActionSubsystem1' incorporates:
       *  ActionPort: '<S45>/Action Port'
       *
       * Block description for '<S41>/SwitchCaseActionSubsystem1':
       *  Conversion factor from mV to uA is calculated with a compensation
       *  obtained as the ratio between the nominal and the target value of
       *  capacitor charge.
       */
      /* Product: '<S45>/Product' incorporates:
       *  Constant: '<S45>/Constant1'
       *  Inport: '<Root>/AbsIonTdc'
       *  Inport: '<Root>/IonChannelEOA'
       *  Inport: '<Root>/IonSelect'
       *  Inport: '<Root>/VChargeObjNom'
       *  MultiPortSwitch: '<S42>/IndexVector4'
       *  MultiPortSwitch: '<S45>/IndexVector1'
       *  MultiPortSwitch: '<S45>/IndexVector4'
       */
      rtb_Product_j = ((uint32_T)ION_GAIN[(IonSelect[(AbsIonTdc[(IonChannelEOA)])])])
        * ((uint32_T)VChargeObjNom);

      /* CCaller: '<S45>/C Caller' incorporates:
       *  Inport: '<Root>/AbsIonTdc'
       *  Inport: '<Root>/IonChannelEOA'
       *  MultiPortSwitch: '<S42>/IndexVector4'
       */
      IonAcq_GetCirIdx(AbsIonTdc[(IonChannelEOA)], &rtb_CCaller_o1_mi,
                       &rtb_CCaller_o2_b);

      /* MultiPortSwitch: '<S45>/IndexVector2' incorporates:
       *  Inport: '<Root>/VChargeObj'
       */
      rtb_IndexVector2_a = VChargeObj[(rtb_CCaller_o2_b)];

      /* DataTypeConversion: '<S45>/Conversion1' incorporates:
       *  Constant: '<S45>/Constant2'
       */
      rtb_Conversion1_f = (uint16_T)((int16_T)MIN_VCHARGE);

      /* MinMax: '<S45>/MinMax' */
      if (rtb_IndexVector2_a > rtb_Conversion1_f) {
        rtb_Conversion1_f = rtb_IndexVector2_a;
      }

      /* End of MinMax: '<S45>/MinMax' */

      /* Product: '<S45>/Divide1' */
      rtb_Product_j /= (uint32_T)rtb_Conversion1_f;

      /* MinMax: '<S45>/MinMax1' incorporates:
       *  Constant: '<S45>/Constant4'
       */
      if (rtb_Product_j < MAX_ION_GAIN) {
        /* DataTypeConversion: '<S45>/Conversion' */
        IonGainEOA = (uint16_T)rtb_Product_j;
      } else {
        /* DataTypeConversion: '<S45>/Conversion' */
        IonGainEOA = (uint16_T)MAX_ION_GAIN;
      }

      /* End of MinMax: '<S45>/MinMax1' */
      /* End of Outputs for SubSystem: '<S41>/SwitchCaseActionSubsystem1' */
      break;

     case 2:
      /* Outputs for IfAction SubSystem: '<S41>/SwitchCaseActionSubsystem4' incorporates:
       *  ActionPort: '<S47>/Action Port'
       *
       * Block description for '<S41>/SwitchCaseActionSubsystem4':
       *  Conversion factor from mV to uA is calculated with a compensation
       *  obtained as the ratio between the reference and the target value of
       *  capacitor charge.
       */
      /* S-Function (Look2D_U16_U16_U16): '<S48>/Look2D_U16_U16_U16' incorporates:
       *  Constant: '<S47>/BKLOADVCRGION'
       *  Constant: '<S47>/BKLOADVCRGION_dim1'
       *  Constant: '<S47>/BKRPMVCRGION'
       *  Constant: '<S47>/BKRPMVCRGION_dim1'
       *  Constant: '<S47>/Constant6'
       */
      Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBVCHARGEREF[0], Rpm,
                         &BKRPMVCRGION[0], BKRPMVCRGION_dim, Load,
                         &BKLOADVCRGION[0], BKLOADVCRGION_dim);

      /* Product: '<S47>/Product' incorporates:
       *  Constant: '<S47>/Constant1'
       *  Inport: '<Root>/AbsIonTdc'
       *  Inport: '<Root>/IonChannelEOA'
       *  Inport: '<Root>/IonSelect'
       *  MultiPortSwitch: '<S42>/IndexVector4'
       *  MultiPortSwitch: '<S47>/IndexVector1'
       *  MultiPortSwitch: '<S47>/IndexVector4'
       */
      rtb_Product_j = ((uint32_T)ION_GAIN[(IonSelect[(AbsIonTdc[(IonChannelEOA)])])])
        * ((uint32_T)rtb_Look2D_U16_U16_U16);

      /* CCaller: '<S47>/C Caller' incorporates:
       *  Inport: '<Root>/AbsIonTdc'
       *  Inport: '<Root>/IonChannelEOA'
       *  MultiPortSwitch: '<S42>/IndexVector4'
       */
      IonAcq_GetCirIdx(AbsIonTdc[(IonChannelEOA)], &rtb_CCaller_o1_mi,
                       &rtb_CCaller_o2_b);

      /* MultiPortSwitch: '<S47>/IndexVector2' incorporates:
       *  Inport: '<Root>/VChargeObj'
       */
      rtb_IndexVector2_a = VChargeObj[(rtb_CCaller_o2_b)];

      /* DataTypeConversion: '<S47>/Conversion1' incorporates:
       *  Constant: '<S47>/Constant2'
       */
      rtb_Conversion1_f = (uint16_T)((int16_T)MIN_VCHARGE);

      /* MinMax: '<S47>/MinMax' */
      if (rtb_IndexVector2_a > rtb_Conversion1_f) {
        rtb_Conversion1_f = rtb_IndexVector2_a;
      }

      /* End of MinMax: '<S47>/MinMax' */

      /* Product: '<S47>/Divide1' */
      rtb_Product_j /= (uint32_T)rtb_Conversion1_f;

      /* MinMax: '<S47>/MinMax1' incorporates:
       *  Constant: '<S47>/Constant4'
       */
      if (rtb_Product_j < MAX_ION_GAIN) {
        /* DataTypeConversion: '<S47>/Conversion' */
        IonGainEOA = (uint16_T)rtb_Product_j;
      } else {
        /* DataTypeConversion: '<S47>/Conversion' */
        IonGainEOA = (uint16_T)MAX_ION_GAIN;
      }

      /* End of MinMax: '<S47>/MinMax1' */
      /* End of Outputs for SubSystem: '<S41>/SwitchCaseActionSubsystem4' */
      break;

     default:
      /* Outputs for IfAction SubSystem: '<S41>/SwitchCaseActionSubsystem3' incorporates:
       *  ActionPort: '<S46>/Action Port'
       *
       * Block description for '<S41>/SwitchCaseActionSubsystem3':
       *  Conversion factor from mV to uA is calculated without compensation on
       *  the charge of capacitor used for ion signal acquisition.
       */
      /* MultiPortSwitch: '<S46>/IndexVector1' incorporates:
       *  Constant: '<S46>/Constant1'
       *  Inport: '<Root>/AbsIonTdc'
       *  Inport: '<Root>/IonChannelEOA'
       *  Inport: '<Root>/IonSelect'
       *  MultiPortSwitch: '<S42>/IndexVector4'
       *  MultiPortSwitch: '<S46>/IndexVector4'
       */
      IonGainEOA = ION_GAIN[(IonSelect[(AbsIonTdc[(IonChannelEOA)])])];

      /* End of Outputs for SubSystem: '<S41>/SwitchCaseActionSubsystem3' */
      break;
    }

    /* End of SwitchCase: '<S41>/Switch Case' */

    /* MultiPortSwitch: '<S42>/IndexVector' incorporates:
     *  Inport: '<Root>/IonChannelEOA'
     *  Inport: '<Root>/IonDThetaPrg'
     */
    IonDThetaEOA = IonDThetaPrg[(IonChannelEOA)];

    /* MultiPortSwitch: '<S42>/IndexVector1' incorporates:
     *  Inport: '<Root>/IonChannelEOA'
     *  Inport: '<Root>/IonDTPrg'
     */
    IonDTEOA = IonDTPrg[(IonChannelEOA)];

    /* MultiPortSwitch: '<S42>/IndexVector3' incorporates:
     *  Inport: '<Root>/IonChannelEOA'
     *  Inport: '<Root>/NSampleStartPrg'
     */
    NSampleStartEOA = NSampleStartPrg[(IonChannelEOA)];

    /* MultiPortSwitch: '<S42>/IndexVector5' incorporates:
     *  Inport: '<Root>/IonChannelEOA'
     *  Inport: '<Root>/IonDThetaInvPrg'
     */
    IonDThetaInvEOA = IonDThetaInvPrg[(IonChannelEOA)];

    /* SignalConversion generated from: '<S37>/FirstKnockAbsTdc' */
    FirstKnockAbsTdc = rtb_FirstKnockAbsTdc_p;

    /* SignalConversion generated from: '<S37>/IonAbsTdcEOA' */
    IonAbsTdcEOA = rtb_IonAbsTdcEOA;

    /* SignalConversion generated from: '<S37>/KnockAbsCycles' */
    KnockAbsCycles = rtb_KnockAbsCycles_o;

    /* SignalConversion generated from: '<S37>/NSampleMaxEOA' */
    NSampleMaxEOA = rtb_NSampleMaxEOA;

    /* End of Outputs for SubSystem: '<S2>/SignalLatch' */

    /* Chart: '<S1>/BufferCopy'
     *
     * Block description for '<S1>/BufferCopy':
     *  This stateflow implements the copy of ion signal from acquistion channel to ion signal buffer (IonBufferV [mV]).
     *  Moreover it calculates also the conversion from mV to uA in order to produce a second ion signal buffer (IonBuffer [uA]).
     *  Finally a diagnosis check is performed on acquired ion signal to detect "short to ground" or "short to voltage".
     *

     */
    /* Chart: '<S1>/BufferCopy' incorporates:
     *  Constant: '<S1>/Constant'
     *  Constant: '<S1>/Constant1'
     *  Constant: '<S1>/Constant3'
     *  Constant: '<S1>/Constant4'
     *  Constant: '<S1>/Constant5'
     *  DataTypeConversion: '<S10>/Conversion1'
     *  Inport: '<Root>/EffNMSpark'
     *  Inport: '<Root>/IonBufferRaw'
     *  Product: '<S10>/Product'
     *
     * Block description for '<S1>/BufferCopy':
     *  This stateflow implements the copy of ion signal from acquistion channel to ion signal buffer (IonBufferV [mV]).
     *  Moreover it calculates also the conversion from mV to uA in order to produce a second ion signal buffer (IonBuffer [uA]).
     *  Finally a diagnosis check is performed on acquired ion signal to detect "short to ground" or "short to voltage".
     *

     */
    /* Event: '<S4>:26' */
    /* Gateway: BufferMgm_Subsystem/BufferCopy */
    /* Event: '<S5>:1' */
    /* During: BufferMgm_Subsystem/BufferCopy */
    /* This stateflow implements the copy of ion signal from acquistion channel to ion signal buffer (IonBufferV [mV]).
       Moreover it calculates also the conversion from mV to uA in order to produce a second ion signal buffer (IonBuffer [uA]).
       Finally a diagnosis check is performed on acquired ion signal to detect "short to ground" or "short to voltage".

     */
    /* Entry Internal: BufferMgm_Subsystem/BufferCopy */
    /* Transition: '<S5>:3' */
    /* call_BufferCopy */
    /*  Force IonBufferV assignment by means of calibrations  */
    if (((int32_T)VTFORCEIONBUFFER[(IonAbsTdcEOA)]) != 0) {
      /* Transition: '<S5>:60' */
      /* Transition: '<S5>:62' */
      /* Graphical Function 'ForceIonBuffer': '<S5>:154' */
      /* No requirement has been added to this function because it is used only as code instrumentation for testing purpose.
         Its aim is to force a ion signal. */
      /* Transition: '<S5>:176' */
      rtb_NSampleMaxEOA = 0U;
      rtb_IndexVector2_a = 0U;
      rtb_Conversion1_f = 0U;
      rtb_Product_j = ((((uint32_T)IonGainEOA) * 625U) >> ((uint32_T)7));
      while (rtb_NSampleMaxEOA <= NSampleMaxEOA) {
        /* Transition: '<S5>:177' */
        /* Transition: '<S5>:180' */
        /*  Force IonBufferV and saturate to 5000 mV  */
        ionVoltage = (uint16_T)(((uint32_T)VTFORCEDIONBUFFER[(rtb_NSampleMaxEOA)])
          * ((uint32_T)VTFORCEDIONGAIN[(IonAbsTdcEOA)]));
        if (((int32_T)ionVoltage) < 16384) {
          IonBufferV[(rtb_NSampleMaxEOA)] = ionVoltage;
        } else {
          IonBufferV[(rtb_NSampleMaxEOA)] = 16384U;
        }

        /*  Diagnosis check  */
        if (IonBufferV[(rtb_NSampleMaxEOA)] < VIONMIN) {
          /* Transition: '<S5>:181' */
          /* Transition: '<S5>:183' */
          rtb_IndexVector2_a = (uint16_T)((int32_T)(((int32_T)rtb_IndexVector2_a)
            + 1));

          /* Transition: '<S5>:188' */
          /* Transition: '<S5>:187' */
        } else {
          /* Transition: '<S5>:182' */
          if (IonBufferV[(rtb_NSampleMaxEOA)] >= VIONMAX) {
            /* Transition: '<S5>:184' */
            /* Transition: '<S5>:186' */
            rtb_Conversion1_f = (uint16_T)((int32_T)(((int32_T)rtb_Conversion1_f)
              + 1));

            /* Transition: '<S5>:187' */
          } else {
            /* Transition: '<S5>:185' */
          }
        }

        /* Outputs for Function Call SubSystem: '<S5>/Convert_mV2uA'
         *
         * Block description for '<S5>/Convert_mV2uA':
         *  This block converts the ion signal buffer from uV to uA
         */
        /* Transition: '<S5>:189' */
        /*  Conversion in uA for IonBuffer  */
        /* Simulink Function 'Convert_mV2uA': '<S5>:219' */
        IonBuffer[(rtb_NSampleMaxEOA)] = ((((uint32_T)IonBufferV
          [(rtb_NSampleMaxEOA)]) * rtb_Product_j) >> ((uint32_T)12));

        /* End of Outputs for SubSystem: '<S5>/Convert_mV2uA' */
        /* Transition: '<S5>:178' */
        rtb_NSampleMaxEOA = (uint16_T)((int32_T)(((int32_T)rtb_NSampleMaxEOA) +
          1));
      }

      /* Transition: '<S5>:179' */
      if (rtb_IndexVector2_a >= NSampleMaxEOA) {
        /* Transition: '<S5>:194' */
        /* Transition: '<S5>:196' */
        rtb_IonAbsTdcEOA = CIRCUIT_SHORT_TO_GND;

        /* Transition: '<S5>:201' */
        /* Transition: '<S5>:200' */
      } else {
        /* Transition: '<S5>:195' */
        if (rtb_Conversion1_f >= NSampleMaxEOA) {
          /* Transition: '<S5>:197' */
          /* Transition: '<S5>:199' */
          rtb_IonAbsTdcEOA = CIRCUIT_SHORT_TO_VCC;

          /* Transition: '<S5>:200' */
        } else {
          /* Transition: '<S5>:198' */
          rtb_IonAbsTdcEOA = NO_PT_FAULT;
        }
      }

      /* Transition: '<S5>:202' */
      /* Transition: '<S5>:227' */
    } else {
      /* Transition: '<S5>:28' */
      /* Graphical Function 'CopyIonBuffer': '<S5>:46' */
      /* This block copies ion signal from raw buffer to IonBuffer and IonBufferV. */
      /* Transition: '<S5>:48' */
      rtb_NSampleMaxEOA = 0U;
      rtb_IndexVector2_a = 0U;
      rtb_Conversion1_f = 0U;
      rtb_Product_j = ((((uint32_T)IonGainEOA) * 625U) >> ((uint32_T)7));
      while (rtb_NSampleMaxEOA <= NSampleMaxEOA) {
        /* Transition: '<S5>:67' */
        /* Transition: '<S5>:69' */
        /*  Copy source to IonBufferV */
        IonBufferV[(rtb_NSampleMaxEOA)] = (uint16_T)(IonBufferRaw
          [(rtb_NSampleMaxEOA)] << ((uint32_T)2));

        /*  Diagnosis check  */
        if (IonBufferV[(rtb_NSampleMaxEOA)] < VIONMIN) {
          /* Transition: '<S5>:72' */
          /* Transition: '<S5>:78' */
          rtb_IndexVector2_a = (uint16_T)((int32_T)(((int32_T)rtb_IndexVector2_a)
            + 1));

          /* Transition: '<S5>:85' */
          /* Transition: '<S5>:88' */
        } else {
          /* Transition: '<S5>:80' */
          if (IonBufferV[(rtb_NSampleMaxEOA)] >= VIONMAX) {
            /* Transition: '<S5>:82' */
            /* Transition: '<S5>:84' */
            rtb_Conversion1_f = (uint16_T)((int32_T)(((int32_T)rtb_Conversion1_f)
              + 1));

            /* Transition: '<S5>:88' */
          } else {
            /* Transition: '<S5>:87' */
          }
        }

        /* Outputs for Function Call SubSystem: '<S5>/Convert_mV2uA'
         *
         * Block description for '<S5>/Convert_mV2uA':
         *  This block converts the ion signal buffer from uV to uA
         */
        /* Transition: '<S5>:90' */
        /*  Conversion in uA for IonBuffer  */
        /* Simulink Function 'Convert_mV2uA': '<S5>:219' */
        IonBuffer[(rtb_NSampleMaxEOA)] = ((((uint32_T)IonBufferV
          [(rtb_NSampleMaxEOA)]) * rtb_Product_j) >> ((uint32_T)12));

        /* End of Outputs for SubSystem: '<S5>/Convert_mV2uA' */
        /* Transition: '<S5>:103' */
        rtb_NSampleMaxEOA = (uint16_T)((int32_T)(((int32_T)rtb_NSampleMaxEOA) +
          1));
      }

      /* Transition: '<S5>:105' */
      if (rtb_IndexVector2_a >= NSampleMaxEOA) {
        /* Transition: '<S5>:109' */
        /* Transition: '<S5>:111' */
        rtb_IonAbsTdcEOA = CIRCUIT_SHORT_TO_GND;

        /* Transition: '<S5>:120' */
        /* Transition: '<S5>:121' */
      } else {
        /* Transition: '<S5>:113' */
        if (rtb_Conversion1_f >= NSampleMaxEOA) {
          /* Transition: '<S5>:115' */
          /* Transition: '<S5>:117' */
          rtb_IonAbsTdcEOA = CIRCUIT_SHORT_TO_VCC;

          /* Transition: '<S5>:121' */
        } else {
          /* Transition: '<S5>:119' */
          rtb_IonAbsTdcEOA = NO_PT_FAULT;
        }
      }

      /* Transition: '<S5>:123' */
    }

    /* Transition: '<S5>:135' */
    PtFaultChannel[(IonAbsTdcEOA)] = rtb_IonAbsTdcEOA;
#if 0
    if (((int32_T)EffNMSpark[(IonAbsTdcEOA)]) != 0) {
      /* Transition: '<S5>:138' */
      /* Transition: '<S5>:143' */
      /* Simulink Function 'Diagnosis': '<S5>:207' */
      IonAcqBufMgm_ptFault = rtb_IonAbsTdcEOA;

      /* Outputs for Function Call SubSystem: '<S5>/Diagnosis'
       *
       * Block description for '<S5>/Diagnosis':
       *  The aim of this block is to signal diagnosis on ion signal to diagmgm
       *  component.
       */
      /* Sum: '<S11>/Add' incorporates:
       *  Constant: '<S11>/Constant1'
       *  Inport: '<Root>/IonChannelEOA'
       */
      rtb_IonAbsTdcEOA = (uint8_T)(((uint32_T)IonChannelEOA) + ((uint32_T)
        DIAG_ION_CH_A));

      /* S-Function (DiagMgm_SetDiagState): '<S12>/DiagMgm_SetDiagState' */
      DiagMgm_SetDiagState( rtb_IonAbsTdcEOA, IonAcqBufMgm_ptFault,
                           &rtb_DiagMgm_SetDiagState);

      /* End of Outputs for SubSystem: '<S5>/Diagnosis' */
      /* Transition: '<S5>:144' */
    } else {
      /* Transition: '<S5>:141' */
    }
#endif

    /* End of Chart: '<S1>/BufferCopy' */
    /* Transition: '<S5>:146' */
    if (((int32_T)START_ION_SPARK_ON) == 1) {
      /* Outputs for Function Call SubSystem: '<S3>/PhaseParamsEvaluation'
       *
       * Block description for '<S3>/PhaseParamsEvaluation':
       *  The block calcuates some signals used to detect coil charge phase and
       *  spark start. Moreover it calculates the end index for the summation
       *  used to evaluated the offset to be subtracted from ion integral.
       */
      /* Product: '<S50>/Divide' incorporates:
       *  Inport: '<Root>/EffDwellTime'
       *  MultiPortSwitch: '<S50>/IndexVector'
       */
      /* Transition: '<S4>:32' */
      /* Transition: '<S4>:34' */
      /* Event: '<S4>:40' */
      DwellSamp = (uint16_T)(EffDwellTime[(IonAbsTdcEOA)] / ((uint32_T)IonDTEOA));

      /* MinMax: '<S50>/MinMax' incorporates:
       *  Constant: '<S50>/Constant'
       */
      if (DwellSamp < ((uint16_T)((uint8_T)MAX_UINT8))) {
        /* Assignment: '<S50>/Assignment' incorporates:
         *  DataTypeConversion: '<S50>/Conversion'
         */
        DwellSampCyl[(IonAbsTdcEOA)] = (uint8_T)DwellSamp;
      } else {
        /* Assignment: '<S50>/Assignment' */
        DwellSampCyl[(IonAbsTdcEOA)] = ((uint8_T)MAX_UINT8);
      }

      /* End of MinMax: '<S50>/MinMax' */

      /* DataTypeConversion: '<S50>/Conversion4' incorporates:
       *  Constant: '<S50>/Constant1'
       */
      rtb_Product_j = (uint32_T)((int16_T)MAX_INT16);

      /* MinMax: '<S50>/MinMax1' incorporates:
       *  Inport: '<Root>/EffDwellTrigTime'
       *  MultiPortSwitch: '<S50>/IndexVector1'
       */
      if (EffDwellTrigTime[(IonAbsTdcEOA)] < rtb_Product_j) {
        rtb_Product_j = EffDwellTrigTime[(IonAbsTdcEOA)];
      }

      /* End of MinMax: '<S50>/MinMax1' */

      /* MinMax: '<S50>/MinMax2' incorporates:
       *  Inport: '<Root>/VtDelayBkEnEOA'
       *  MultiPortSwitch: '<S50>/IndexVector2'
       *  Product: '<S50>/Divide1'
       *  Sum: '<S50>/Add'
       */
      rtb_IndexVector2_mh = (int16_T)((((int32_T)rtb_Product_j) - ((int32_T)
        VtDelayBkEnEOA[(IonAbsTdcEOA)])) / ((int32_T)IonDTEOA));
      if (rtb_IndexVector2_mh <= 0) {
        rtb_IndexVector2_mh = 0;
      }

      /* End of MinMax: '<S50>/MinMax2' */

      /* MinMax: '<S50>/MinMax3' incorporates:
       *  DataTypeConversion: '<S50>/Conversion1'
       */
      if (DwellSamp < ((uint16_T)rtb_IndexVector2_mh)) {
        StartSpark = DwellSamp;
      } else {
        StartSpark = (uint16_T)rtb_IndexVector2_mh;
      }

      /* End of MinMax: '<S50>/MinMax3' */

      /* Assignment: '<S50>/Assignment1' */
      VtStartSpark[(IonAbsTdcEOA)] = StartSpark;

      /* Product: '<S50>/Divide2' incorporates:
       *  Inport: '<Root>/VtDeltaBkEnEOA'
       *  MultiPortSwitch: '<S50>/IndexVector3'
       */
      StopOffsetSearch = (uint16_T)(((uint32_T)VtDeltaBkEnEOA[(IonAbsTdcEOA)]) /
        ((uint32_T)IonDTEOA));

      /* MinMax: '<S50>/MinMax4' incorporates:
       *  Constant: '<S50>/Constant3'
       */
      if (StopOffsetSearch < ((uint16_T)((uint8_T)MAX_UINT8))) {
        /* Assignment: '<S50>/Assignment3' incorporates:
         *  DataTypeConversion: '<S50>/Conversion3'
         */
        VtStopOffsetSearch[(IonAbsTdcEOA)] = (uint8_T)StopOffsetSearch;
      } else {
        /* Assignment: '<S50>/Assignment3' */
        VtStopOffsetSearch[(IonAbsTdcEOA)] = ((uint8_T)MAX_UINT8);
      }

      /* End of MinMax: '<S50>/MinMax4' */
      /* End of Outputs for SubSystem: '<S3>/PhaseParamsEvaluation' */
      /* Transition: '<S4>:37' */
    } else {
      /* Transition: '<S4>:36' */
    }

    /* Transition: '<S4>:38' */
    if ((((int32_T)ENSPIKEDETECT) > 0) && (VtStPlasObjDBuff[(IonAbsTdcEOA)] ==
         ION_ST_SEL)) {
      /* Chart: '<S1>/SpikeDetection'
       *
       * Block description for '<S1>/SpikeDetection':
       *  This stateflow perfoms noise spike detection on ion signal buffer.
       *  To detect a spike is necessary to find a big increase in angular gradient of ion signal followed by a large decrease of angular gradient within a limited number of samples (expressed as acquisition angle).
       *  Then spike may also be deleted if erasing functionality is enabled by means of ENSPIKEDETECT calibration.
       */
      /* Chart: '<S1>/SpikeDetection' incorporates:
       *  SubSystem: '<S1>/SpikeParameters'
       *
       * Block description for '<S1>/SpikeDetection':
       *  This stateflow perfoms noise spike detection on ion signal buffer.
       *  To detect a spike is necessary to find a big increase in angular gradient of ion signal followed by a large decrease of angular gradient within a limited number of samples (expressed as acquisition angle).
       *  Then spike may also be deleted if erasing functionality is enabled by means of ENSPIKEDETECT calibration.
       *
       * Block description for '<S1>/SpikeParameters':
       *  The aim of this block is to calculate the parameters necessary to
       *  detect the presence of a noise spike on ion signal buffer.
       */
      /* S-Function (PreLookUpIdSearch_U16): '<S27>/PreLookUpIdSearch_U16' incorporates:
       *  Constant: '<S17>/Constant'
       *  Constant: '<S17>/Constant1'
       */
      /* Transition: '<S4>:14' */
      /* Transition: '<S4>:16' */
      /* Event: '<S4>:28' */
      /* Gateway: BufferMgm_Subsystem/SpikeDetection */
      /* Event: '<S7>:1' */
      /* During: BufferMgm_Subsystem/SpikeDetection */
      /* This stateflow perfoms noise spike detection on ion signal buffer.
         To detect a spike is necessary to find a big increase in angular gradient of ion signal followed by a large decrease of angular gradient within a limited number of samples (expressed as acquisition angle).
         Then spike may also be deleted if erasing functionality is enabled by means of ENSPIKEDETECT calibration. */
      /* Entry Internal: BufferMgm_Subsystem/SpikeDetection */
      /* Transition: '<S7>:65' */
      /* call_SpikeDetection */
      /* Transition: '<S7>:68' */
      /*  Calculate spike parameters, like detection thresholds  */
      /* Event: '<S7>:4' */
      PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                            &rtb_PreLookUpIdSearch_U16_o2, Rpm, &BKIONSPKRPM[0],
                            ((uint8_T)BKIONSPKRPM_dim));

      /* S-Function (LookUp_IR_U16): '<S21>/LookUp_IR_U16' incorporates:
       *  Constant: '<S17>/Constant2'
       *  Constant: '<S17>/Constant5'
       */
      LookUp_IR_U16( &rtb_LookUp_IR_U16_k, &VTSPIKEANGDIFF[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKIONSPKRPM_dim));

      /* S-Function (LookUp_IR_U16): '<S20>/LookUp_IR_U16' incorporates:
       *  Constant: '<S17>/Constant3'
       *  Constant: '<S17>/Constant6'
       */
      LookUp_IR_U16( &rtb_LookUp_IR_U16_d, &VTSPIKEDIONTHRH[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKIONSPKRPM_dim));

      /* S-Function (LookUp_IR_S16): '<S19>/LookUp_IR_S16' incorporates:
       *  Constant: '<S17>/Constant4'
       *  Constant: '<S17>/Constant7'
       */
      LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTSPIKEDIONTHRL[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKIONSPKRPM_dim));

      /* S-Function (LookUp_IR_U16): '<S24>/LookUp_IR_U16' incorporates:
       *  Constant: '<S17>/Constant11'
       *  Constant: '<S17>/Constant14'
       */
      LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTMINDIONTHRH[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKIONSPKRPM_dim));

      /* Switch: '<S17>/Switch1' incorporates:
       *  Constant: '<S17>/Constant18'
       *  Constant: '<S17>/Constant19'
       *  DataTypeConversion: '<S17>/Conversion1'
       *  Product: '<S17>/Product'
       *  RelationalOperator: '<S17>/RelationalOperator'
       */
      if (rtb_LookUp_IR_U16 > ((uint16_T)((int16_T)MAX_INT16))) {
        rtb_IndexVector2_mh = ((int16_T)MIN_INT16);
      } else {
        rtb_IndexVector2_mh = (int16_T)((int32_T)(-((int32_T)rtb_LookUp_IR_U16)));
      }

      /* End of Switch: '<S17>/Switch1' */

      /* S-Function (LookUp_IR_U16): '<S22>/LookUp_IR_U16' incorporates:
       *  Constant: '<S17>/Constant10'
       *  Constant: '<S17>/Constant12'
       */
      LookUp_IR_U16( &rtb_LookUp_IR_U16_m, &VTBEFORESPIKETIME[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKIONSPKRPM_dim));

      /* S-Function (LookUp_IR_U16): '<S23>/LookUp_IR_U16' incorporates:
       *  Constant: '<S17>/Constant13'
       *  Constant: '<S17>/Constant9'
       */
      LookUp_IR_U16( &rtb_LookUp_IR_U16_g, &VTAFTERSPIKETIME[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKIONSPKRPM_dim));

      /* Switch: '<S16>/Switch' incorporates:
       *  Constant: '<S16>/Constant'
       *  Constant: '<S16>/Constant1'
       *  Constant: '<S16>/Constant2'
       *  Constant: '<S16>/Constant3'
       *  Constant: '<S16>/Constant4'
       *  Constant: '<S16>/Constant5'
       *  Inport: '<Root>/GasPos'
       *  Inport: '<Root>/Load'
       *  Inport: '<Root>/VDGasPosCAN'
       *  Logic: '<S16>/LogicalOperator'
       *  Logic: '<S16>/LogicalOperator1'
       *  RelationalOperator: '<S16>/RelationalOperator'
       *  RelationalOperator: '<S16>/RelationalOperator1'
       *  RelationalOperator: '<S16>/RelationalOperator2'
       *  RelationalOperator: '<S16>/RelationalOperator3'
       *  SignalConversion generated from: '<S8>/SpikeAngleSel_old'
       *  Sum: '<S16>/Add'
       */
      if (((int32_T)Load) > ((int32_T)((uint32_T)(((uint32_T)THRLOADSPIKEANG) +
             ((uint32_T)HYSTLOADSPIKEANG))))) {
        SpikeAngleSel = true;
      } else {
        SpikeAngleSel = (((Load > THRLOADSPIKEANG) && ((((int32_T)VDGasPosCAN)
          != 0) || (GasPos >= SPIKEANGGASPOSTHR))) && (SpikeAngleSel));
      }

      /* End of Switch: '<S16>/Switch' */

      /* Switch: '<S18>/Switch' incorporates:
       *  Constant: '<S18>/Constant2'
       *  DataTypeConversion: '<S18>/Conversion3'
       *  Product: '<S18>/Product'
       *  Sum: '<S18>/Add'
       *  Switch: '<S17>/Switch2'
       */
      if (((int32_T)SPIKEIDDMAEN) != 0) {
        /* Sum: '<S18>/Add1' incorporates:
         *  Constant: '<S18>/Constant1'
         */
        Add = ((int32_T)NrStartIonDMAIdxEOA) + ((int32_T)DMAIDXSPKDETOFFSET);

        /* MinMax: '<S18>/MinMax1' incorporates:
         *  DataTypeConversion: '<S18>/Conversion2'
         */
        if (Add > 0) {
          rtb_Product_j = (uint32_T)Add;
        } else {
          rtb_Product_j = 0U;
        }

        /* End of MinMax: '<S18>/MinMax1' */
      } else {
        if (SpikeAngleSel) {
          /* S-Function (LookUp_IR_U16): '<S26>/LookUp_IR_U16' incorporates:
           *  Constant: '<S17>/Constant15'
           *  Constant: '<S17>/Constant17'
           *  Switch: '<S17>/Switch2'
           */
          LookUp_IR_U16( &rtb_LookUp_IR_U16_f, &VTSPIKEANGLEHL[0],
                        rtb_PreLookUpIdSearch_U16_o1,
                        rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKIONSPKRPM_dim));

          /* Switch: '<S17>/Switch2' */
          rtb_NSampleMaxEOA = rtb_LookUp_IR_U16_f;
        } else {
          /* S-Function (LookUp_IR_U16): '<S25>/LookUp_IR_U16' incorporates:
           *  Constant: '<S17>/Constant16'
           *  Constant: '<S17>/Constant8'
           *  Switch: '<S17>/Switch2'
           */
          LookUp_IR_U16( &rtb_LookUp_IR_U16_p, &VTSPIKEANGLELL[0],
                        rtb_PreLookUpIdSearch_U16_o1,
                        rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKIONSPKRPM_dim));

          /* Switch: '<S17>/Switch2' */
          rtb_NSampleMaxEOA = rtb_LookUp_IR_U16_p;
        }

        rtb_Product_j = ((((uint32_T)rtb_NSampleMaxEOA) * ((uint32_T)
          IonDThetaInvEOA)) >> ((uint32_T)16)) + ((uint32_T)StartSpark);
      }

      /* End of Switch: '<S18>/Switch' */
      /* Transition: '<S7>:24' */
      /*  Initialize outputs noise detection */
      DeltaIonMax = 0;
      DeltaIonMaxAng = 0;
      DeltaIonMaxId = 0U;
      DeltaIonMin = 0;
      DeltaIonMinId = 0U;
      DeltaIonAng = 0;
      FlgSpikeFound = 0U;
      NSampIonSpike = 0U;

      /*  Initialize outputs noise deletion */
      AngDiff = 0;

      /* Chart: '<S1>/SpikeDetection'
       *
       * Block description for '<S1>/SpikeDetection':
       *  This stateflow perfoms noise spike detection on ion signal buffer.
       *  To detect a spike is necessary to find a big increase in angular gradient of ion signal followed by a large decrease of angular gradient within a limited number of samples (expressed as acquisition angle).
       *  Then spike may also be deleted if erasing functionality is enabled by means of ENSPIKEDETECT calibration.
       */
      /* Chart: '<S1>/SpikeDetection' incorporates:
       *  SubSystem: '<S1>/SpikeParameters'
       *
       * Block description for '<S1>/SpikeDetection':
       *  This stateflow perfoms noise spike detection on ion signal buffer.
       *  To detect a spike is necessary to find a big increase in angular gradient of ion signal followed by a large decrease of angular gradient within a limited number of samples (expressed as acquisition angle).
       *  Then spike may also be deleted if erasing functionality is enabled by means of ENSPIKEDETECT calibration.
       *
       * Block description for '<S1>/SpikeParameters':
       *  The aim of this block is to calculate the parameters necessary to
       *  detect the presence of a noise spike on ion signal buffer.
       */
      /*  Normalize spike parameters  */
      BeforeSpikeInt = (uint16_T)(((uint32_T)rtb_LookUp_IR_U16_m) / ((uint32_T)
        IonDTEOA));
      AfterSpikeInt = (uint16_T)(((uint32_T)rtb_LookUp_IR_U16_g) / ((uint32_T)
        IonDTEOA));

      /* MinMax: '<S18>/MinMax' incorporates:
       *  Constant: '<S18>/Constant'
       *  DataTypeConversion: '<S18>/Conversion'
       *  DataTypeConversion: '<S18>/Conversion1'
       *  DataTypeConversion: '<S31>/Conversion'
       *  DataTypeConversion: '<S32>/Conversion'
       */
      /*  max used to protect against zero division  */
      if (rtb_Product_j < ((uint32_T)((uint16_T)LAST_SAMPLE_ID))) {
        StartSpkId = (uint16_T)rtb_Product_j;
      } else {
        StartSpkId = ((uint16_T)LAST_SAMPLE_ID);
      }

      /* End of MinMax: '<S18>/MinMax' */
      /*  Initialize local variable  */
      if (((int32_T)StartSpkId) > 1) {
        rtb_NSampleMaxEOA = StartSpkId;
      } else {
        rtb_NSampleMaxEOA = 1U;
      }

      /* Transition: '<S7>:58' */
      /*  Noise search  */
      while (rtb_NSampleMaxEOA < NSampleMaxEOA) {
        /* Transition: '<S7>:28' */
        /* Transition: '<S7>:31' */
        /* Simulink Function 'calculateGradient': '<S7>:193' */
        Add = ((int32_T)IonBuffer[(rtb_NSampleMaxEOA)]) - ((int32_T)IonBuffer
          [((int32_T)rtb_NSampleMaxEOA) - 1]);
        DeltaIonAng = (Add * 1024) / ((int32_T)IonDThetaEOA);

        /*  Look for first spike front (rising edge)  */
        if ((DeltaIonAng > ((int32_T)rtb_LookUp_IR_U16_d)) && (Add > ((int32_T)
              rtb_LookUp_IR_U16))) {
          /* Transition: '<S7>:34' */
          /* Transition: '<S7>:36' */
          DeltaIonMax = Add;
          DeltaIonMaxAng = DeltaIonAng;
          DeltaIonMaxId = rtb_NSampleMaxEOA;

          /* Transition: '<S7>:47' */
          /* Transition: '<S7>:50' */
        } else {
          /* Transition: '<S7>:42' */
          /*  Look for second spike front (falling edge)  */
          if (((((int32_T)DeltaIonMaxId) != 0) && (DeltaIonAng < ((int32_T)
                 rtb_LookUp_IR_S16))) && (Add < ((int32_T)rtb_IndexVector2_mh)))
          {
            /* Transition: '<S7>:44' */
            /* Transition: '<S7>:46' */
            DeltaIonMin = Add;
            DeltaIonMinId = rtb_NSampleMaxEOA;

            /*  break loop, both spike fronts
               have been detected */
            rtb_NSampleMaxEOA = NSampleMaxEOA;

            /* Transition: '<S7>:50' */
          } else {
            /* Transition: '<S7>:49' */
          }
        }

        /* Transition: '<S7>:53' */
        rtb_NSampleMaxEOA = (uint16_T)((int32_T)(((int32_T)rtb_NSampleMaxEOA) +
          1));
      }

      /* Transition: '<S7>:60' */
      /*  Spike detected condition  */
      if (((int32_T)DeltaIonMinId) != 0) {
        /* Transition: '<S7>:73' */
        /* Transition: '<S7>:75' */
        AngDiff = (DeltaIonMinId - DeltaIonMaxId) * IonDThetaEOA;

        /* Outputs for Function Call SubSystem: '<S1>/SpikeParameters'
         *
         * Block description for '<S1>/SpikeParameters':
         *  The aim of this block is to calculate the parameters necessary to
         *  detect the presence of a noise spike on ion signal buffer.
         */
        /*  Spike is acknowledged only if rising and falling edge are close  */
        if (AngDiff < ((int32_T)rtb_LookUp_IR_U16_k)) {
          /* Transition: '<S7>:83' */
          /* Transition: '<S7>:85' */
          NSampIonSpike = DeltaIonMaxId;
          FlgSpikeFound = 1U;
          Add = ((int32_T)DeltaIonMaxId) - ((int32_T)BeforeSpikeInt);
          if (Add > 0) {
            rtb_NSampleMaxEOA = (uint16_T)Add;
          } else {
            Add = 0;
            rtb_NSampleMaxEOA = 0U;
          }

          u0 = ((int32_T)DeltaIonMinId) + ((int32_T)AfterSpikeInt);
          if (u0 >= ((int32_T)NSampleMaxEOA)) {
            u0 = (int32_T)NSampleMaxEOA;
          }

          /*  Enabling for spike deletion strategy  */
          if (((int32_T)ENSPIKEDETECT) == 2) {
            /* Outputs for Function Call SubSystem: '<S7>/EraseSpike.voltageFittingParam'
             *
             * Block description for '<S7>/EraseSpike.voltageFittingParam':
             *  This block calculates the coefficient for linear interpolation on
             *  IonBufferV in case of spike erasion.
             */
            /* Outputs for Function Call SubSystem: '<S7>/EraseSpike.currentFittingParam'
             *
             * Block description for '<S7>/EraseSpike.currentFittingParam':
             *  This block calculates the coefficient for linear interpolation on
             *  IonBuffer in case of spike erasion.
             */
            /* Sum: '<S13>/Add1' incorporates:
             *  Sum: '<S14>/Add1'
             */
            /* Transition: '<S7>:99' */
            /* Transition: '<S7>:112' */
            /* Transition: '<S7>:138' */
            /*  Linear fitting parameters for IonBuffer reconstruction
               mc = (IonBuffer[id_x2] - IonBuffer[id_x1])/(id_x2 -id_x1)
               qc = IonBuffer[id_x1] - (mc*id_x1) */
            /* Simulink Function 'currentFittingParam': '<S7>:202' */
            Add2 = ((int32_T)((uint16_T)u0)) - ((int32_T)((uint16_T)Add));

            /* End of Outputs for SubSystem: '<S7>/EraseSpike.voltageFittingParam' */

            /* Product: '<S13>/Divide' incorporates:
             *  DataTypeConversion: '<S13>/Conversion'
             *  DataTypeConversion: '<S13>/Conversion1'
             *  Sum: '<S13>/Add'
             *  Sum: '<S13>/Add1'
             */
            Divide = (((int32_T)IonBuffer[(uint16_T)u0]) - ((int32_T)IonBuffer
                       [(rtb_NSampleMaxEOA)])) / Add2;

            /* Sum: '<S13>/Add2' incorporates:
             *  DataTypeConversion: '<S13>/Conversion1'
             *  Product: '<S13>/Product'
             */
            Add2_p = ((int32_T)IonBuffer[(rtb_NSampleMaxEOA)]) - (Divide *
              ((int32_T)((uint16_T)Add)));

            /* End of Outputs for SubSystem: '<S7>/EraseSpike.currentFittingParam' */

            /* Outputs for Function Call SubSystem: '<S7>/EraseSpike.voltageFittingParam'
             *
             * Block description for '<S7>/EraseSpike.voltageFittingParam':
             *  This block calculates the coefficient for linear interpolation on
             *  IonBufferV in case of spike erasion.
             */
            /* Product: '<S14>/Divide' incorporates:
             *  DataTypeConversion: '<S14>/Conversion'
             *  Sum: '<S14>/Add'
             */
            /*  Linear fitting parameters for IonBufferV reconstruction
               mv = (IonBufferV[id_x2] - IonBufferV[id_x1])/(id_x2 -id_x1)
               qv = IonBufferV[id_x1] - (mv*id_x1) */
            /* Simulink Function 'voltageFittingParam': '<S7>:215' */
            rtb_IndexVector2_mh = (int16_T)((((int32_T)IonBufferV[(uint16_T)u0])
              - ((int32_T)((int16_T)IonBufferV[(rtb_NSampleMaxEOA)]))) / Add2);

            /* Sum: '<S14>/Add2' incorporates:
             *  DataTypeConversion: '<S14>/Conversion'
             *  Product: '<S14>/Product'
             */
            Add2 = ((int32_T)((int16_T)IonBufferV[(rtb_NSampleMaxEOA)])) -
              (((int32_T)rtb_IndexVector2_mh) * ((int32_T)((uint16_T)Add)));

            /* End of Outputs for SubSystem: '<S7>/EraseSpike.voltageFittingParam' */
            /*  Start for loop  */
            for (rtb_NSampleMaxEOA = (uint16_T)Add; rtb_NSampleMaxEOA <
                 ((uint16_T)u0); rtb_NSampleMaxEOA = (uint16_T)((int32_T)
                  (((int32_T)rtb_NSampleMaxEOA) + 1))) {
              /* Transition: '<S7>:142' */
              /* Transition: '<S7>:145' */
              /*  IonBuffer reconstruction  */
              IonBuffer[(rtb_NSampleMaxEOA)] = (uint32_T)((int32_T)((Divide *
                ((int32_T)rtb_NSampleMaxEOA)) + Add2_p));

              /* Transition: '<S7>:174' */
              /*  IonBufferV reconstruction  */
              IonBufferV[(rtb_NSampleMaxEOA)] = (uint16_T)((int32_T)((((int32_T)
                rtb_IndexVector2_mh) * ((int32_T)rtb_NSampleMaxEOA)) + Add2));

              /* Transition: '<S7>:183' */
            }

            /* Transition: '<S7>:184' */
            /* Transition: '<S7>:129' */
            /* Transition: '<S7>:132' */
          } else {
            /* Transition: '<S7>:109' */
          }

          /* Transition: '<S7>:110' */
        } else {
          /* Transition: '<S7>:89' */
        }

        /* End of Outputs for SubSystem: '<S1>/SpikeParameters' */
        /* Transition: '<S7>:90' */
      } else {
        /* Transition: '<S7>:77' */
      }

      /* Transition: '<S7>:135' */
    } else {
      /* Transition: '<S4>:18' */
    }
  }

  /* End of Chart: '<Root>/Task_Supervisior' */
}

/* System initialize for function-call system: '<Root>/Task_Supervisior' */
void IonAcqBuf_Task_Supervisior_Init(void)
{
  /* SystemInitialize for Chart: '<S1>/BufferCopy'
   *
   * Block description for '<S1>/BufferCopy':
   *  This stateflow implements the copy of ion signal from acquistion channel to ion signal buffer (IonBufferV [mV]).
   *  Moreover it calculates also the conversion from mV to uA in order to produce a second ion signal buffer (IonBuffer [uA]).
   *  Finally a diagnosis check is performed on acquired ion signal to detect "short to ground" or "short to voltage".
   *

   */
  /* SystemInitialize for Chart: '<S1>/BufferCopy'
   *
   * Block description for '<S1>/BufferCopy':
   *  This stateflow implements the copy of ion signal from acquistion channel to ion signal buffer (IonBufferV [mV]).
   *  Moreover it calculates also the conversion from mV to uA in order to produce a second ion signal buffer (IonBuffer [uA]).
   *  Finally a diagnosis check is performed on acquired ion signal to detect "short to ground" or "short to voltage".
   *

   */
  memset((&(IonBufferV[0])), 0, 800U * (sizeof(uint16_T)));
  memset((&(IonBuffer[0])), 0, 800U * (sizeof(uint32_T)));
  memset((&(PtFaultChannel[0])), 0, (sizeof(uint8_T)) << 3U);

  /* SystemInitialize for Chart: '<S1>/SpikeDetection'
   *
   * Block description for '<S1>/SpikeDetection':
   *  This stateflow perfoms noise spike detection on ion signal buffer.
   *  To detect a spike is necessary to find a big increase in angular gradient of ion signal followed by a large decrease of angular gradient within a limited number of samples (expressed as acquisition angle).
   *  Then spike may also be deleted if erasing functionality is enabled by means of ENSPIKEDETECT calibration.
   */
  /* SystemInitialize for Chart: '<S1>/SpikeDetection'
   *
   * Block description for '<S1>/SpikeDetection':
   *  This stateflow perfoms noise spike detection on ion signal buffer.
   *  To detect a spike is necessary to find a big increase in angular gradient of ion signal followed by a large decrease of angular gradient within a limited number of samples (expressed as acquisition angle).
   *  Then spike may also be deleted if erasing functionality is enabled by means of ENSPIKEDETECT calibration.
   */
  memset((&(IonBufferV[0])), 0, 800U * (sizeof(uint16_T)));
  memset((&(IonBuffer[0])), 0, 800U * (sizeof(uint32_T)));
  NSampIonSpike = 0U;
  FlgSpikeFound = 0U;
  BeforeSpikeInt = 0U;
  AfterSpikeInt = 0U;
  StartSpkId = 0U;
  DeltaIonMax = 0;
  DeltaIonMin = 0;
  DeltaIonMaxAng = 0;
  DeltaIonAng = 0;
  DeltaIonMaxId = 0U;
  DeltaIonMinId = 0U;
  AngDiff = 0;
  SpikeAngleSel = false;
}

/*
 * Output and update for function-call system: '<Root>/Task_Supervisior'
 * Block description for: '<Root>/Task_Supervisior'
 *   This chart schedules IonAcqBufMgm functionalities according to PowerOn and
 *   EOA tasks.
 */
void IonAcqBufMgm_Task_Supervisior(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[2];
  int32_T i;

  /* Chart: '<Root>/Task_Supervisior' incorporates:
   *  TriggerPort: '<S4>/input events'
   *
   * Block description for '<Root>/Task_Supervisior':
   *  This chart schedules IonAcqBufMgm functionalities according to PowerOn
   *  and EOA tasks.
   */
  for (i = 0; i < 2; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: Task_Supervisior */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S4>:2' */
    i = (int32_T)IonAcqBufMgm_event_ev_PowerOn;
    IonAc_chartstep_c3_IonAcqBufMgm(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S4>:4' */
    i = (int32_T)IonAcqBufMgm_event_ev_EOA;
    IonAc_chartstep_c3_IonAcqBufMgm(&i);
  }
}
#pragma ghs section text=default

/* Model step function */
void IonAcqBufMgm_EOA(void)
{
  /* Chart: '<Root>/Task_Supervisior'
   *
   * Block description for '<Root>/Task_Supervisior':
   *  This chart schedules IonAcqBufMgm functionalities according to PowerOn
   *  and EOA tasks.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufMgm_EOA' */

  /* Chart: '<Root>/Task_Supervisior'
   *
   * Block description for '<Root>/Task_Supervisior':
   *  This chart schedules IonAcqBufMgm functionalities according to PowerOn
   *  and EOA tasks.
   */
  IonAcqBufMgm_Task_Supervisior(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufMgm_EOA' */
}

/* Model step function */
void IonAcqBufMgm_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufMgm_PowerOn' incorporates:
   *  Chart: '<Root>/Task_Supervisior'
   *
   * Block description for '<Root>/Task_Supervisior':
   *  This chart schedules IonAcqBufMgm functionalities according to PowerOn
   *  and EOA tasks.
   */

  /* Chart: '<Root>/Task_Supervisior'
   *
   * Block description for '<Root>/Task_Supervisior':
   *  This chart schedules IonAcqBufMgm functionalities according to PowerOn
   *  and EOA tasks.
   */
  IonAcqBufMgm_Task_Supervisior(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufMgm_PowerOn' */
}

/* Model initialize function */
void IonAcqBufMgm_initialize(void)
{
  /* SystemInitialize for Chart: '<Root>/Task_Supervisior'
   *
   * Block description for '<Root>/Task_Supervisior':
   *  This chart schedules IonAcqBufMgm functionalities according to PowerOn
   *  and EOA tasks.
   */

  /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufMgm_EOA' */

  /* SystemInitialize for Chart: '<Root>/Task_Supervisior'
   *
   * Block description for '<Root>/Task_Supervisior':
   *  This chart schedules IonAcqBufMgm functionalities according to PowerOn
   *  and EOA tasks.
   */
  IonAcqBuf_Task_Supervisior_Init();

  /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufMgm_EOA' */

  /* SystemInitialize for Merge: '<S9>/Merge1' */
  memset((&(IonBuffer[0])), 0, 800U * (sizeof(uint32_T)));

  /* SystemInitialize for Merge: '<S9>/Merge12' */
  memset((&(IonBufferV[0])), 0, 800U * (sizeof(uint16_T)));

  /* SystemInitialize for Merge: '<S9>/Merge16' */
  SpikeAngleSel = false;

  /* SystemInitialize for Merge: '<S9>/Merge11' */
  NSampIonSpike = 0U;

  /* SystemInitialize for Merge: '<S9>/Merge2' */
  memset((&(PtFaultChannel[0])), 0, (sizeof(uint8_T)) << 3U);

  /* SystemInitialize for Merge: '<S9>/Merge3' */
  FlgSpikeFound = 0U;

  /* SystemInitialize for Merge: '<S9>/Merge4' */
  BeforeSpikeInt = 0U;

  /* SystemInitialize for Merge: '<S9>/Merge6' */
  AfterSpikeInt = 0U;

  /* SystemInitialize for Merge: '<S9>/Merge7' */
  StartSpkId = 0U;

  /* SystemInitialize for Merge: '<S9>/Merge5' */
  DeltaIonMax = 0;

  /* SystemInitialize for Merge: '<S9>/Merge8' */
  DeltaIonMin = 0;

  /* SystemInitialize for Merge: '<S9>/Merge9' */
  DeltaIonMaxAng = 0;

  /* SystemInitialize for Merge: '<S9>/Merge10' */
  DeltaIonAng = 0;

  /* SystemInitialize for Merge: '<S9>/Merge14' */
  DeltaIonMaxId = 0U;

  /* SystemInitialize for Merge: '<S9>/Merge15' */
  DeltaIonMinId = 0U;

  /* SystemInitialize for Merge: '<S9>/Merge13' */
  AngDiff = 0;
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T IonDThetaEOA;
uint8_T IonDTEOA;
uint16_T NSampleMaxEOA;
uint16_T NSampleStartEOA;
uint8_T IonAbsTdcEOA;
uint16_T IonGainEOA;
uint16_T NrStartIonDMAIdxEOA;
uint8_T KnockAbsCycles;
uint16_T NSampIonSpike;
uint16_T IonBufferV[MAX_SAMPLE];
uint32_T IonBuffer[MAX_SAMPLE];
uint8_T PtFaultChannel[N_CYL_MAX];
uint8_T DwellSampCyl[N_CYL_MAX];
uint16_T VtStartSpark[N_CYL_MAX];
uint8_T VtStopOffsetSearch[N_CYL_MAX];
void IonAcqBufMgm_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<MAX_SAMPLE;idx++) {
    IonBufferV[idx] = 0u;
    IonBuffer[idx] = 0u;
  }

  for (idx=0;idx<N_CYL_MAX;idx++) {
    PtFaultChannel[N_CYL_MAX] = 0u;
    DwellSampCyl[N_CYL_MAX] = 0u;
    VtStartSpark[N_CYL_MAX] = 0u;
    VtStopOffsetSearch[N_CYL_MAX] = 0u;
  }

  IonDThetaEOA = 0u;
  IonDTEOA = 8u;
  NSampleMaxEOA = 0u;
  NSampleStartEOA = 0u;
  IonAbsTdcEOA = 0u;
  IonGainEOA = 4096u;
  NrStartIonDMAIdxEOA = 0u;
  KnockAbsCycles = 0u;
  NSampIonSpike = 0u;
}

void IonAcqBufMgm_PowerOn(void)
{
  IonAcqBufMgm_Stub();
}

void IonAcqBufMgm_EOA(void)
{
  IonAcqBufMgm_Stub();
}

#endif                                 /* _BUILD_IONACQBUFMGM_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * signal_blocks                                                              *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
