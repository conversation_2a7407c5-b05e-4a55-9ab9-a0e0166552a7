/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           RonDetectMgm.c
 **  File Creation Date: 30-Mar-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         RonDetectMgm
 **  Model Description:  The aim of this software component is to schedule ron detection strategy (that can be fast or slow)
 **  Model Version:      1.1174
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Wed Mar 30 10:54:38 2022
 **
 **  Last Saved Modification:  RoccaG - Wed Mar 30 10:39:41 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "RonDetectMgm_out.h"
#include "RonDetectMgm_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_RONDETECTMGM_DEF        11174U                    /* Referenced by: '<S2>/Constant1' */

/* Model Version. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_RONDETECTMGM_

/* Add a check for each important define */
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint32_T tmpOdomRonStartEE;     /* '<S1>/RonDetectMgm' */

/* Odometer value when Ron detection starts */
static uint32_T tmpSecRonStartEE;      /* '<S1>/RonDetectMgm' */

/* Seconds run-time value when Ron detection starts */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T CNTMAXEOANOTEST = 4294967293U;/* Referenced by: '<S1>/RonDetectMgm' */

/* EOA tasks with no test execution to reset counters */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENKCOHINCRON1 = 0U;/* Referenced by: '<S1>/RonDetectMgm' */

/* Enable level 1 action in case of knocking coherence */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENKCOHINCRON2 = 0U;/* Referenced by: '<S1>/RonDetectMgm' */

/* Enable level 2 action in case of knocking coherence */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENKCOHINCRON3 = 0U;/* Referenced by: '<S1>/RonDetectMgm' */

/* Enable level 3 action in case of knocking coherence */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T FORCERONDETECT = 0U;/* Referenced by: '<S1>/RonDetectMgm' */

/* RonDetect strategy force flag (=1 force; =2 stop/disable) */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint32_T DKnockIntArg;                 /* '<S1>/RonDetectMgm' */
uint8_T FlgRonStoredFuel;
/* KnockInt used as input argument for software component RonDetectCnt */
//enum_StRonDetect StRonDetect;          /* '<S3>/Merge50' */

/* RonDetect main state */
enum_StRonDetect StRonDetectArg;       /* '<S1>/RonDetectMgm' */

/* RonDetect main state used as input argument for software component RonDetectCnt */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T CntEOANoTest;/* '<S3>/Merge14' */

/* EOA cycles without detection */
STATIC_TEST_POINT uint32_T DKnockInt[8];/* '<S3>/Merge42' */

/* KnockInt used for counter management */
STATIC_TEST_POINT uint32_T IdVer_RonDetectMgm;/* '<S2>/Constant1' */

/* Model Version */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void RonDetectMgm_100ms(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectMgm_100ms' incorporates:
   *  SubSystem: '<Root>/T100ms'
   *
   * Block description for '<Root>/T100ms':
   *  This block is the 100ms task for software component RonDetectMgm
   *
   * Block requirements for '<Root>/T100ms':
   *  1. EISB_FCA6CYL_SW_REQ_2706: Software shall provide a reset functionality for the flag used to ... (ECU_SW_Requirements#12169)
   */
  /* SignalConversion: '<S4>/SignalCopy' incorporates:
   *  Inport: '<Root>/FlgRonStoredFuel'
   */
  FlgRonStoredEE = FlgRonStoredFuel;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectMgm_100ms' */
}

/* Model step function */
void RonDetectMgm_EOA(void)
{
  uint8_T FlgRonStoredEE_o;
  uint32_T CntEOANoTest_g;
  enum_StRonDetect StRonDetect_i;
  uint32_T DKnockInt_i[8];
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectMgm_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   *
   * Block description for '<Root>/EOA':
   *  This block is the EOA task for software component RonDetectMgm
   */
  /* SignalConversion generated from: '<S1>/FlgRonStoredEE_old' */
  FlgRonStoredEE_o = FlgRonStoredEE;

  /* Chart: '<S1>/RonDetectMgm' incorporates:
   *  Inport: '<Root>/EnRonDetect'
   *  Inport: '<Root>/EnRonDetectEE'
   *  Inport: '<Root>/FlgCntKnockCohInc'
   *  Inport: '<Root>/FlgKCohInc'
   *  Inport: '<Root>/FlgKCohIncLev1'
   *  Inport: '<Root>/FlgRonDetect'
   *  Inport: '<Root>/FlgSteadyStateRon'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/IonKnockEnabled'
   *  Inport: '<Root>/KnockInt'
   *  Inport: '<Root>/KnockState'
   *  Inport: '<Root>/ResetEOACnt'
   *  Inport: '<Root>/SecondsWorkTime'
   *  Inport: '<Root>/StRonCCheck'
   *  Inport: '<Root>/TempEnableRon'
   *  Inport: '<Root>/ThrIntKnock'
   *  Inport: '<Root>/TotOdometerCAN'
   *  SignalConversion generated from: '<S1>/CntEOANoTest_old'
   *  SignalConversion generated from: '<S1>/DKnockInt_old'
   *  SignalConversion generated from: '<S1>/StRonDetect_old'
   */
  /* Gateway: EOA/RonDetectMgm */
  /* During: EOA/RonDetectMgm */
  /* This statechart schedules ron detection (triggering fast or slow modality) according to engine state (stability of engine load and engine speed) and to knock detection.

     The INIT box is used to optimize code generation (always initializing model outputs with their old values).

     STATECHART box is the real statechart.

     Simulink functions (RonDetectEst_BackgroundInit,RonDetectEst_MonitoringInit, RonDetectEst_ResetSecStartRun, RonDetectEst_Recovery) are used to implements function calls towards software component RonDetectEst, that implements the fast and slow ron detection strategies.


     The other functions (graphical functions) instead, are used to collect common functionality used in the statechart.This statechart schedules ron detection (triggering fast or slow modality) according to engine state (stability of engine load and engine speed) and to knock detection.

     The INIT box is used to optimize code generation (always initializing model outputs with their old values).

     MAIN box is the real statechart.

     Simulink functions (RonDetectEst_BackgroundInit,RonDetectEst_MonitoringInit,RonDetectEst_Calc, RonDetectEst_ResetSecStartRun, RonDetectEst_Recovery) are used to implements function calls towards software component RonDetectEst, that implements the fast and slow ron detection strategies.


     The other functions (graphical functions) instead, are used to collect common functionality used in the statechart. */
  /* Entry Internal: EOA/RonDetectMgm */
  /* Transition: '<S11>:124' */
  /* Transition: '<S11>:126' */
  /* Transition: '<S11>:136' */
  /* Assign to each output its old value to optimize code generation  */
  StRonDetect_i = StRonDetect;
  CntEOANoTest_g = CntEOANoTest;
  for (i = 0; i < 8; i++) {
    DKnockInt_i[i] = DKnockInt[(i)];
  }

  /* Transition: '<S11>:137' */
  if ((((int32_T)EnRonDetect) == 0) || (((int32_T)EnRonDetectEE) == 0)) {
    /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_MonitoringInit'
     *
     * Block description for '<S1>/RonDetectEst_MonitoringInit':
     *  Function call towards software component RonDetectEst
     */
    /* S-Function (RonDetectEst_MonitoringInit): '<S8>/RonDetectEst_MonitoringInit' */
    /* Transition: '<S11>:1740' */
    /* Transition: '<S11>:1742'
     * Requirements for Transition: '<S11>:1742':
     *  1. EISB_FCA6CYL_SW_REQ_2707: Software shall activate the flag used to indicate the correct memo... (ECU_SW_Requirements#12170)
     */
    /* Event: '<S11>:2043' */
    RonDetectEst_MonitoringInit();

    /* End of Outputs for SubSystem: '<S1>/RonDetectEst_MonitoringInit' */

    /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
     *
     * Block description for '<S1>/RonDetectCnt_Reset':
     *  This block implements function call towards software component
     *  RonDetectCnt, in order to reset knock events counters.
     */
    /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
    /* Event: '<S11>:1421' */
    RonDetectCnt_Reset();

    /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
    CntEOANoTest_g = 0U;
    FlgRonStoredEE_o = 1U;

    /* Transition: '<S11>:1743' */
    /* Transition: '<S11>:1744' */
    /* Transition: '<S11>:1749' */
    StRonDetect_i = RD_INIT;

    /* Transition: '<S11>:1754' */
    /* Transition: '<S11>:1759' */
    /* Transition: '<S11>:1766' */
    /*  RESET  */
    /* Transition: '<S11>:1494' */
    /* Transition: '<S11>:1504' */
    /* Transition: '<S11>:1526' */
    /* Transition: '<S11>:1537' */
    /* Transition: '<S11>:1546' */
    /* Transition: '<S11>:1547' */
  } else {
    /* Transition: '<S11>:1741' */
    if ((((int32_T)ENKCOHINCRON3) != 0) && (((int32_T)FlgKCohInc) != 0)) {
      /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_MonitoringInit'
       *
       * Block description for '<S1>/RonDetectEst_MonitoringInit':
       *  Function call towards software component RonDetectEst
       */
      /* S-Function (RonDetectEst_MonitoringInit): '<S8>/RonDetectEst_MonitoringInit' */
      /* Transition: '<S11>:1745' */
      /* Transition: '<S11>:1746'
       * Requirements for Transition: '<S11>:1746':
       *  1. EISB_FCA6CYL_SW_REQ_2708: Software shall reset the flag used to indicate the correct memoriz... (ECU_SW_Requirements#12171)
       */
      /* Event: '<S11>:2043' */
      RonDetectEst_MonitoringInit();

      /* End of Outputs for SubSystem: '<S1>/RonDetectEst_MonitoringInit' */

      /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
       *
       * Block description for '<S1>/RonDetectCnt_Reset':
       *  This block implements function call towards software component
       *  RonDetectCnt, in order to reset knock events counters.
       */
      /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
      /* Event: '<S11>:1421' */
      RonDetectCnt_Reset();

      /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
      CntEOANoTest_g = 0U;

      /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_BackgroundInit'
       *
       * Block description for '<S1>/RonDetectEst_BackgroundInit':
       *  Function call towards software component RonDetectEst
       */
      /* S-Function (RonDetectEst_BackgroundInit): '<S7>/RonDetectEst_BackgroundInit' */
      /* Event: '<S11>:2044' */
      RonDetectEst_BackgroundInit();

      /* End of Outputs for SubSystem: '<S1>/RonDetectEst_BackgroundInit' */

      /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_Recovery'
       *
       * Block description for '<S1>/RonDetectEst_Recovery':
       *  Function call towards software component RonDetectEst
       */
      /* S-Function (RonDetectEst_Recovery): '<S9>/RonDetectEst_Recovery' */
      /* Event: '<S11>:2046' */
      RonDetectEst_Recovery();

      /* End of Outputs for SubSystem: '<S1>/RonDetectEst_Recovery' */
      FlgRonStoredEE_o = 0U;

      /* Transition: '<S11>:1748' */
      /* Transition: '<S11>:1749' */
      StRonDetect_i = RD_INIT;

      /* Transition: '<S11>:1754' */
      /* Transition: '<S11>:1759' */
      /* Transition: '<S11>:1766' */
      /*  RESET  */
      /* Transition: '<S11>:1494' */
      /* Transition: '<S11>:1504' */
      /* Transition: '<S11>:1526' */
      /* Transition: '<S11>:1537' */
      /* Transition: '<S11>:1546' */
      /* Transition: '<S11>:1547' */
    } else {
      /* Transition: '<S11>:1747' */
      switch (FORCERONDETECT) {
       case 2:
        /* Transition: '<S11>:1750' */
        /* Transition: '<S11>:1752'
         * Requirements for Transition: '<S11>:1752':
         *  1. EISB_FCA6CYL_SW_REQ_2709: Software shall activate the flag used to indicate the correct memo... (ECU_SW_Requirements#12172)
         */
        FlgRonStoredEE_o = 1U;

        /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
         *
         * Block description for '<S1>/RonDetectCnt_Reset':
         *  This block implements function call towards software component
         *  RonDetectCnt, in order to reset knock events counters.
         */
        /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
        /* Event: '<S11>:1421' */
        RonDetectCnt_Reset();

        /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
        CntEOANoTest_g = 0U;

        /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_BackgroundInit'
         *
         * Block description for '<S1>/RonDetectEst_BackgroundInit':
         *  Function call towards software component RonDetectEst
         */
        /* S-Function (RonDetectEst_BackgroundInit): '<S7>/RonDetectEst_BackgroundInit' */
        /* Event: '<S11>:2044' */
        RonDetectEst_BackgroundInit();

        /* End of Outputs for SubSystem: '<S1>/RonDetectEst_BackgroundInit' */
        StRonDetect_i = RD_TEST_STOP;

        /* Transition: '<S11>:1753' */
        /* Transition: '<S11>:1754' */
        /* Transition: '<S11>:1759' */
        /* Transition: '<S11>:1766' */
        /*  RESET  */
        /* Transition: '<S11>:1494' */
        /* Transition: '<S11>:1504' */
        /* Transition: '<S11>:1526' */
        /* Transition: '<S11>:1537' */
        /* Transition: '<S11>:1546' */
        /* Transition: '<S11>:1547' */
        break;

       case 1:
        /* Transition: '<S11>:1751' */
        /* Transition: '<S11>:1755' */
        /* Transition: '<S11>:1756'
         * Requirements for Transition: '<S11>:1756':
         *  1. EISB_FCA6CYL_SW_REQ_2710: Software shall reset the flag used to indicate the correct memoriz... (ECU_SW_Requirements#12173)
         */
        FlgRonStoredEE_o = 0U;

        /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_MonitoringInit'
         *
         * Block description for '<S1>/RonDetectEst_MonitoringInit':
         *  Function call towards software component RonDetectEst
         */
        /* S-Function (RonDetectEst_MonitoringInit): '<S8>/RonDetectEst_MonitoringInit' */
        /* Event: '<S11>:2043' */
        RonDetectEst_MonitoringInit();

        /* End of Outputs for SubSystem: '<S1>/RonDetectEst_MonitoringInit' */

        /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
         *
         * Block description for '<S1>/RonDetectCnt_Reset':
         *  This block implements function call towards software component
         *  RonDetectCnt, in order to reset knock events counters.
         */
        /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
        /* Event: '<S11>:1421' */
        RonDetectCnt_Reset();

        /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
        CntEOANoTest_g = 0U;
        tmpOdomRonStartEE = TotOdometerCAN;
        tmpSecRonStartEE = SecondsWorkTime;

        /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_ResetSecStartRun'
         *
         * Block description for '<S1>/RonDetectEst_ResetSecStartRun':
         *  Function call towards software component RonDetectEst
         */
        /* S-Function (RonDetectEst_ResetSecStartRun): '<S10>/RonDetectEst_ResetSecStartRun' incorporates:
         *  Inport: '<Root>/SecondsWorkTime'
         *  Inport: '<Root>/TotOdometerCAN'
         */
        /* Event: '<S11>:2045' */
        RonDetectEst_ResetSecStartRun();

        /* End of Outputs for SubSystem: '<S1>/RonDetectEst_ResetSecStartRun' */
        StRonDetect_i = RD_WAIT_STAB;

        /* Transition: '<S11>:1758' */
        /* Transition: '<S11>:1759' */
        /* Transition: '<S11>:1766' */
        /*  RESET  */
        /* Transition: '<S11>:1494' */
        /* Transition: '<S11>:1504' */
        /* Transition: '<S11>:1526' */
        /* Transition: '<S11>:1537' */
        /* Transition: '<S11>:1546' */
        /* Transition: '<S11>:1547' */
        break;

       default:
        /* Transition: '<S11>:1757' */
        if ((((uint32_T)StRonCCheck) == RD_CC_STOREDIN) || (((uint32_T)
              StRonCCheck) == RD_CC_FOUNDIN)) {
          /* Transition: '<S11>:1760' */
          /* Transition: '<S11>:1763'
           * Requirements for Transition: '<S11>:1763':
           *  1. EISB_FCA6CYL_SW_REQ_2711: Software shall set state variable StRonDetect equal to RD_INHERIT ... (ECU_SW_Requirements#12174)
           */
          OdomRonStartEE = 0U;
          SecRonStartEE = 0U;
          OdomRonStopEE = 0U;
          SecRonStopEE = 0U;
          StRonDetect_i = RD_INHERIT;

          /* Transition: '<S11>:1764' */
          /* Transition: '<S11>:1766' */
          /*  RESET  */
          /* Transition: '<S11>:1494' */
          /* Transition: '<S11>:1504' */
          /* Transition: '<S11>:1526' */
          /* Transition: '<S11>:1537' */
          /* Transition: '<S11>:1546' */
          /* Transition: '<S11>:1547' */
        } else {
          /* Transition: '<S11>:1762' */
          /*  ENABLE  */
          switch (StRonDetect) {
           case RD_INIT:
            /* Transition: '<S11>:1476' */
            /* Transition: '<S11>:1478' */
            if (((int32_T)FlgSteadyStateRon) != 0) {
              /* Transition: '<S11>:1479' */
              /* Transition: '<S11>:1482' */
              if (KnockInt[(IonAbsTdcEOA)] > ThrIntKnock) {
                /* Transition: '<S11>:1576' */
                /* Transition: '<S11>:1578' */
                DKnockInt_i[IonAbsTdcEOA] = KnockInt[(IonAbsTdcEOA)] -
                  ThrIntKnock;

                /* Transition: '<S11>:1579' */
              } else {
                /* Transition: '<S11>:1577' */
                DKnockInt_i[IonAbsTdcEOA] = 0U;
              }

              /* Transition: '<S11>:1580' */
              if (((int32_T)TempEnableRon) == 1) {
                /* Transition: '<S11>:1581' */
                /* Transition: '<S11>:1583' */
                if (((((int32_T)ENKCOHINCRON2) == 0) || (((int32_T)
                       FlgCntKnockCohInc[(IonAbsTdcEOA)]) == 0)) && ((((int32_T)
                       ENKCOHINCRON1) == 0) || (((int32_T)FlgKCohIncLev1
                       [(IonAbsTdcEOA)]) == 0))) {
                  /* Transition: '<S11>:1584' */
                  /* Transition: '<S11>:1586' */
                  /* Transition: '<S11>:1588' */
                  /* Transition: '<S11>:1591' */
                  if (((int32_T)IonKnockEnabled) != 0) {
                    /* Transition: '<S11>:1594' */
                    /* Transition: '<S11>:1596' */
                    if (((uint32_T)KnockState[(IonAbsTdcEOA)]) == NO_KNOCK) {
                      /* Transition: '<S11>:1598' */
                      CntEOANoTest_g = 0U;
                    } else {
                      /* Transition: '<S11>:1597' */
                      /* Transition: '<S11>:1599' */
                      /* Transition: '<S11>:1601' */
                    }

                    /* Transition: '<S11>:1603'
                     * Requirements for Transition: '<S11>:1603':
                     *  1. EISB_FCA6CYL_SW_REQ_2713: Every time that state variable StRonDetect is equal to RD_INIT and... (ECU_SW_Requirements#12176)
                     */
                    /*  START MONITORING  */
                    DKnockIntArg = DKnockInt_i[IonAbsTdcEOA];
                    StRonDetectArg = StRonDetect;

                    /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Monitoring'
                     *
                     * Block description for '<S1>/RonDetectCnt_Monitoring':
                     *  This block implements function call towards software component
                     *  RonDetectCnt, in order to aggregates knock events and activates ron
                     *  detection strategy.
                     */
                    /* S-Function (RonDetectCnt_Monitoring): '<S5>/RonDetectCnt_Monitoring' incorporates:
                     *  Inport: '<Root>/KnockState'
                     */
                    /* Event: '<S11>:1422' */
                    RonDetectCnt_Monitoring();

                    /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Monitoring' */
                    if (((int32_T)ResetEOACnt) != 0) {
                      /* Transition: '<S11>:1779' */
                      /* Transition: '<S11>:1782' */
                      CntEOANoTest_g = 0U;

                      /* Transition: '<S11>:1783' */
                    } else {
                      /* Transition: '<S11>:1780' */
                    }

                    /* Transition: '<S11>:1604' */
                  } else {
                    /* Transition: '<S11>:1595' */
                    CntEOANoTest_g = CntEOANoTest + 1U;

                    /* Transition: '<S11>:1600' */
                    /* Transition: '<S11>:1602' */
                    /*  STOP MONITORING  */
                  }
                } else {
                  /* Transition: '<S11>:1585' */
                  /* Transition: '<S11>:1587' */
                  /* Transition: '<S11>:1589' */
                  /* Transition: '<S11>:1593' */
                  /* Transition: '<S11>:1602' */
                  /*  STOP MONITORING  */
                  /* Transition: '<S11>:1590' */
                  /* Transition: '<S11>:1592' */
                }
              } else {
                /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
                 *
                 * Block description for '<S1>/RonDetectCnt_Reset':
                 *  This block implements function call towards software component
                 *  RonDetectCnt, in order to reset knock events counters.
                 */
                /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
                /* Transition: '<S11>:1582' */
                /* Event: '<S11>:1421' */
                RonDetectCnt_Reset();

                /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
                /* Transition: '<S11>:1589' */
                /* Transition: '<S11>:1593' */
                /* Transition: '<S11>:1602' */
                /*  STOP MONITORING  */
              }

              /* Transition: '<S11>:1605' */
            } else {
              /* Transition: '<S11>:1480' */
              /* Transition: '<S11>:1484' */
            }

            /* Transition: '<S11>:1485' */
            if (((int32_T)FlgRonStoredEE_o) == 0) {
              /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_MonitoringInit'
               *
               * Block description for '<S1>/RonDetectEst_MonitoringInit':
               *  Function call towards software component RonDetectEst
               */
              /* S-Function (RonDetectEst_MonitoringInit): '<S8>/RonDetectEst_MonitoringInit' */
              /* Transition: '<S11>:1486' */
              /* Transition: '<S11>:1487'
               * Requirements for Transition: '<S11>:1487':
               *  1. EISB_FCA6CYL_SW_REQ_2712: Every time that state variable StRonDetect is equal to RD_INIT, th... (ECU_SW_Requirements#12175)
               */
              /* Event: '<S11>:2043' */
              RonDetectEst_MonitoringInit();

              /* End of Outputs for SubSystem: '<S1>/RonDetectEst_MonitoringInit' */

              /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
               *
               * Block description for '<S1>/RonDetectCnt_Reset':
               *  This block implements function call towards software component
               *  RonDetectCnt, in order to reset knock events counters.
               */
              /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
              /* Event: '<S11>:1421' */
              RonDetectCnt_Reset();

              /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
              CntEOANoTest_g = 0U;
              tmpOdomRonStartEE = TotOdometerCAN;
              tmpSecRonStartEE = SecondsWorkTime;

              /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_ResetSecStartRun'
               *
               * Block description for '<S1>/RonDetectEst_ResetSecStartRun':
               *  Function call towards software component RonDetectEst
               */
              /* S-Function (RonDetectEst_ResetSecStartRun): '<S10>/RonDetectEst_ResetSecStartRun' incorporates:
               *  Inport: '<Root>/SecondsWorkTime'
               *  Inport: '<Root>/TotOdometerCAN'
               */
              /* Event: '<S11>:2045' */
              RonDetectEst_ResetSecStartRun();

              /* End of Outputs for SubSystem: '<S1>/RonDetectEst_ResetSecStartRun' */
              StRonDetect_i = RD_WAIT_STAB;
            } else {
              /* Transition: '<S11>:1488' */
              /* Transition: '<S11>:1492' */
            }

            /* Transition: '<S11>:1493' */
            /* Transition: '<S11>:1494' */
            /* Transition: '<S11>:1504' */
            /* Transition: '<S11>:1526' */
            /* Transition: '<S11>:1537' */
            /* Transition: '<S11>:1546' */
            /* Transition: '<S11>:1547' */
            break;

           case RD_WAIT_STAB:
            /* Transition: '<S11>:1477' */
            /* Transition: '<S11>:1489' */
            /* Transition: '<S11>:1491' */
            if (((int32_T)FlgSteadyStateRon) != 0) {
              /* Transition: '<S11>:1495' */
              /* Transition: '<S11>:1497'
               * Requirements for Transition: '<S11>:1497':
               *  1. EISB_FCA6CYL_SW_REQ_2714: Every time that state variable StRonDetect is equal to RD_WAIT_STA... (ECU_SW_Requirements#12177)
               */
              StRonDetect_i = RD_STAB;
            } else {
              /* Transition: '<S11>:1496'
               * Requirements for Transition: '<S11>:1496':
               *  1. EISB_FCA6CYL_SW_REQ_2715: Every time that state variable StRonDetect is equal to RD_WAIT_STA... (ECU_SW_Requirements#12178)
               */
              CntEOANoTest_g = CntEOANoTest + 1U;
              if ((CntEOANoTest + 1U) > CNTMAXEOANOTEST) {
                /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
                 *
                 * Block description for '<S1>/RonDetectCnt_Reset':
                 *  This block implements function call towards software component
                 *  RonDetectCnt, in order to reset knock events counters.
                 */
                /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
                /* Transition: '<S11>:1498' */
                /* Transition: '<S11>:1499' */
                /* Event: '<S11>:1421' */
                RonDetectCnt_Reset();

                /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
                CntEOANoTest_g = 0U;
              } else {
                /* Transition: '<S11>:1500' */
                /* Transition: '<S11>:1501' */
              }

              /* Transition: '<S11>:1502' */
            }

            /* Transition: '<S11>:1503' */
            /* Transition: '<S11>:1504' */
            /* Transition: '<S11>:1526' */
            /* Transition: '<S11>:1537' */
            /* Transition: '<S11>:1546' */
            /* Transition: '<S11>:1547' */
            break;

           case RD_STAB:
            /* Transition: '<S11>:1490' */
            /* Transition: '<S11>:1505' */
            /* Transition: '<S11>:1507' */
            if (((int32_T)FlgSteadyStateRon) == 0) {
              /* Transition: '<S11>:1508' */
              /* Transition: '<S11>:1511'
               * Requirements for Transition: '<S11>:1511':
               *  1. EISB_FCA6CYL_SW_REQ_2716: Every time that state variable StRonDetect is equal to RD_STAB, th... (ECU_SW_Requirements#12179)
               */
              StRonDetect_i = RD_WAIT_STAB;
            } else {
              /* Transition: '<S11>:1510' */
              if (KnockInt[(IonAbsTdcEOA)] > ThrIntKnock) {
                /* Transition: '<S11>:1942' */
                /* Transition: '<S11>:1944' */
                DKnockInt_i[IonAbsTdcEOA] = KnockInt[(IonAbsTdcEOA)] -
                  ThrIntKnock;

                /* Transition: '<S11>:1945' */
              } else {
                /* Transition: '<S11>:1943' */
                DKnockInt_i[IonAbsTdcEOA] = 0U;
              }

              /* Transition: '<S11>:1946' */
              if (((int32_T)TempEnableRon) == 1) {
                /* Transition: '<S11>:1947' */
                /* Transition: '<S11>:1949' */
                if (((((int32_T)ENKCOHINCRON2) == 0) || (((int32_T)
                       FlgCntKnockCohInc[(IonAbsTdcEOA)]) == 0)) && ((((int32_T)
                       ENKCOHINCRON1) == 0) || (((int32_T)FlgKCohIncLev1
                       [(IonAbsTdcEOA)]) == 0))) {
                  /* Transition: '<S11>:1950' */
                  /* Transition: '<S11>:1952' */
                  /* Transition: '<S11>:1954' */
                  /* Transition: '<S11>:1957' */
                  if (((int32_T)IonKnockEnabled) != 0) {
                    /* Transition: '<S11>:1959' */
                    /* Transition: '<S11>:1962' */
                    if (((uint32_T)KnockState[(IonAbsTdcEOA)]) == NO_KNOCK) {
                      /* Transition: '<S11>:1964' */
                      CntEOANoTest_g = 0U;
                    } else {
                      /* Transition: '<S11>:1963' */
                      /* Transition: '<S11>:1965' */
                      /* Transition: '<S11>:1967' */
                    }

                    /* Transition: '<S11>:1968'
                     * Requirements for Transition: '<S11>:1968':
                     *  1. EISB_FCA6CYL_SW_REQ_2717: Every time that state variable StRonDetect is equal to RD_STAB  th... (ECU_SW_Requirements#12180)
                     */
                    /*  START MONITORING  */
                    DKnockIntArg = DKnockInt_i[IonAbsTdcEOA];
                    StRonDetectArg = StRonDetect;

                    /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Monitoring'
                     *
                     * Block description for '<S1>/RonDetectCnt_Monitoring':
                     *  This block implements function call towards software component
                     *  RonDetectCnt, in order to aggregates knock events and activates ron
                     *  detection strategy.
                     */
                    /* S-Function (RonDetectCnt_Monitoring): '<S5>/RonDetectCnt_Monitoring' incorporates:
                     *  Inport: '<Root>/KnockState'
                     */
                    /* Event: '<S11>:1422' */
                    RonDetectCnt_Monitoring();

                    /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Monitoring' */
                    if (((int32_T)ResetEOACnt) != 0) {
                      /* Transition: '<S11>:1970' */
                      /* Transition: '<S11>:1972' */
                      CntEOANoTest_g = 0U;

                      /* Transition: '<S11>:1974' */
                    } else {
                      /* Transition: '<S11>:1971' */
                    }

                    /* Transition: '<S11>:1973' */
                  } else {
                    /* Transition: '<S11>:1961'
                     * Requirements for Transition: '<S11>:1961':
                     *  1. EISB_FCA6CYL_SW_REQ_2718: Every time that state variable StRonDetect is equal to RD_STAB, th... (ECU_SW_Requirements#12181)
                     */
                    CntEOANoTest_g = CntEOANoTest + 1U;

                    /* Transition: '<S11>:1966' */
                    /* Transition: '<S11>:1969' */
                    /*  STOP MONITORING  */
                  }
                } else {
                  /* Transition: '<S11>:1951' */
                  /* Transition: '<S11>:1953' */
                  /* Transition: '<S11>:1955' */
                  /* Transition: '<S11>:1960' */
                  /* Transition: '<S11>:1969' */
                  /*  STOP MONITORING  */
                  /* Transition: '<S11>:1956' */
                  /* Transition: '<S11>:1958' */
                }
              } else {
                /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
                 *
                 * Block description for '<S1>/RonDetectCnt_Reset':
                 *  This block implements function call towards software component
                 *  RonDetectCnt, in order to reset knock events counters.
                 */
                /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
                /* Transition: '<S11>:1948'
                 * Requirements for Transition: '<S11>:1948':
                 *  1. EISB_FCA6CYL_SW_REQ_2719: Every time that state variable StRonDetect is equal to RD_STAB, th... (ECU_SW_Requirements#12182)
                 */
                /* Event: '<S11>:1421' */
                RonDetectCnt_Reset();

                /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
                /* Transition: '<S11>:1955' */
                /* Transition: '<S11>:1960' */
                /* Transition: '<S11>:1969' */
                /*  STOP MONITORING  */
              }

              /* Transition: '<S11>:1976' */
              if (((int32_T)FlgRonDetect) != 0) {
                /* Transition: '<S11>:1513' */
                /* Transition: '<S11>:1515'
                 * Requirements for Transition: '<S11>:1515':
                 *  1. EISB_FCA6CYL_SW_REQ_2720: Every time that state variable StRonDetect is equal to RD_STAB, th... (ECU_SW_Requirements#12183)
                 */
                FlgRonStoredEE_o = 1U;
                OdomRonStartEE = tmpOdomRonStartEE;
                SecRonStartEE = tmpSecRonStartEE;
                OdomRonStopEE = TotOdometerCAN;
                SecRonStopEE = SecondsWorkTime;

                /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_BackgroundInit'
                 *
                 * Block description for '<S1>/RonDetectEst_BackgroundInit':
                 *  Function call towards software component RonDetectEst
                 */
                /* S-Function (RonDetectEst_BackgroundInit): '<S7>/RonDetectEst_BackgroundInit' incorporates:
                 *  Inport: '<Root>/SecondsWorkTime'
                 *  Inport: '<Root>/TotOdometerCAN'
                 */
                /* Event: '<S11>:2044' */
                RonDetectEst_BackgroundInit();

                /* End of Outputs for SubSystem: '<S1>/RonDetectEst_BackgroundInit' */
                StRonDetect_i = RD_TEST_STOP;
              } else {
                /* Transition: '<S11>:1514' */
                if (CntEOANoTest_g > CNTMAXEOANOTEST) {
                  /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
                   *
                   * Block description for '<S1>/RonDetectCnt_Reset':
                   *  This block implements function call towards software component
                   *  RonDetectCnt, in order to reset knock events counters.
                   */
                  /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
                  /* Transition: '<S11>:1519' */
                  /* Transition: '<S11>:1521' */
                  /* Event: '<S11>:1421' */
                  RonDetectCnt_Reset();

                  /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
                  CntEOANoTest_g = 0U;
                } else {
                  /* Transition: '<S11>:1520' */
                  /* Transition: '<S11>:1525' */
                }

                /* Transition: '<S11>:1524' */
              }

              /* Transition: '<S11>:1522' */
            }

            /* Transition: '<S11>:1523' */
            /* Transition: '<S11>:1526' */
            /* Transition: '<S11>:1537' */
            /* Transition: '<S11>:1546' */
            /* Transition: '<S11>:1547' */
            break;

           case RD_TEST_STOP:
            /* Transition: '<S11>:1506' */
            /* Transition: '<S11>:1527' */
            /* Transition: '<S11>:1529' */
            if (((int32_T)FlgSteadyStateRon) != 0) {
              /* Transition: '<S11>:1530' */
              /* Transition: '<S11>:1533' */
              if (KnockInt[(IonAbsTdcEOA)] > ThrIntKnock) {
                /* Transition: '<S11>:2006' */
                /* Transition: '<S11>:2008' */
                DKnockInt_i[IonAbsTdcEOA] = KnockInt[(IonAbsTdcEOA)] -
                  ThrIntKnock;

                /* Transition: '<S11>:2009' */
              } else {
                /* Transition: '<S11>:2007' */
                DKnockInt_i[IonAbsTdcEOA] = 0U;
              }

              /* Transition: '<S11>:2010' */
              if (((int32_T)TempEnableRon) == 1) {
                /* Transition: '<S11>:2011' */
                /* Transition: '<S11>:2013' */
                if (((((int32_T)ENKCOHINCRON2) == 0) || (((int32_T)
                       FlgCntKnockCohInc[(IonAbsTdcEOA)]) == 0)) && ((((int32_T)
                       ENKCOHINCRON1) == 0) || (((int32_T)FlgKCohIncLev1
                       [(IonAbsTdcEOA)]) == 0))) {
                  /* Transition: '<S11>:2014' */
                  /* Transition: '<S11>:2016' */
                  /* Transition: '<S11>:2018' */
                  /* Transition: '<S11>:2021' */
                  if (((int32_T)IonKnockEnabled) != 0) {
                    /* Transition: '<S11>:2023' */
                    /* Transition: '<S11>:2026' */
                    if (((uint32_T)KnockState[(IonAbsTdcEOA)]) == NO_KNOCK) {
                      /* Transition: '<S11>:2028' */
                      CntEOANoTest_g = 0U;
                    } else {
                      /* Transition: '<S11>:2027' */
                      /* Transition: '<S11>:2029' */
                      /* Transition: '<S11>:2031' */
                    }

                    /* Transition: '<S11>:2032'
                     * Requirements for Transition: '<S11>:2032':
                     *  1. EISB_FCA6CYL_SW_REQ_2721: Every time that state variable StRonDetect is equal to RD_TEST_STO... (ECU_SW_Requirements#12184)
                     */
                    /*  START MONITORING  */
                    DKnockIntArg = DKnockInt_i[IonAbsTdcEOA];
                    StRonDetectArg = StRonDetect;

                    /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Monitoring'
                     *
                     * Block description for '<S1>/RonDetectCnt_Monitoring':
                     *  This block implements function call towards software component
                     *  RonDetectCnt, in order to aggregates knock events and activates ron
                     *  detection strategy.
                     */
                    /* S-Function (RonDetectCnt_Monitoring): '<S5>/RonDetectCnt_Monitoring' incorporates:
                     *  Inport: '<Root>/KnockState'
                     */
                    /* Event: '<S11>:1422' */
                    RonDetectCnt_Monitoring();

                    /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Monitoring' */
                    if (((int32_T)ResetEOACnt) != 0) {
                      /* Transition: '<S11>:2034' */
                      /* Transition: '<S11>:2036' */
                      CntEOANoTest_g = 0U;

                      /* Transition: '<S11>:2038' */
                    } else {
                      /* Transition: '<S11>:2035' */
                    }

                    /* Transition: '<S11>:2037' */
                  } else {
                    /* Transition: '<S11>:2025' */
                    CntEOANoTest_g = CntEOANoTest + 1U;

                    /* Transition: '<S11>:2030' */
                    /* Transition: '<S11>:2033' */
                    /*  STOP MONITORING  */
                  }
                } else {
                  /* Transition: '<S11>:2015' */
                  /* Transition: '<S11>:2017' */
                  /* Transition: '<S11>:2019' */
                  /* Transition: '<S11>:2024' */
                  /* Transition: '<S11>:2033' */
                  /*  STOP MONITORING  */
                  /* Transition: '<S11>:2020' */
                  /* Transition: '<S11>:2022' */
                }
              } else {
                /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
                 *
                 * Block description for '<S1>/RonDetectCnt_Reset':
                 *  This block implements function call towards software component
                 *  RonDetectCnt, in order to reset knock events counters.
                 */
                /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
                /* Transition: '<S11>:2012' */
                /* Event: '<S11>:1421' */
                RonDetectCnt_Reset();

                /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
                /* Transition: '<S11>:2019' */
                /* Transition: '<S11>:2024' */
                /* Transition: '<S11>:2033' */
                /*  STOP MONITORING  */
              }

              /* Transition: '<S11>:2040' */
            } else {
              /* Transition: '<S11>:1532' */
              /* Transition: '<S11>:1535' */
            }

            /* Transition: '<S11>:1536' */
            /* Transition: '<S11>:1537' */
            /* Transition: '<S11>:1546' */
            /* Transition: '<S11>:1547' */
            break;

           case RD_INHERIT:
            /* Transition: '<S11>:1528' */
            /* Transition: '<S11>:1538' */
            /* Transition: '<S11>:1543'
             * Requirements for Transition: '<S11>:1543':
             *  1. EISB_FCA6CYL_SW_REQ_2722: Every time that state variable StRonDetect is equal to RD_INHERIT,... (ECU_SW_Requirements#12185)
             */
            /* [StRonCCheck != enum_StRonCCheck.RD_CC_STOREDIN && StRonCCheck != enum_StRonCCheck.RD_CC_FOUNDIN] */
            StRonDetect_i = RD_INIT;

            /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_BackgroundInit'
             *
             * Block description for '<S1>/RonDetectEst_BackgroundInit':
             *  Function call towards software component RonDetectEst
             */
            /* S-Function (RonDetectEst_BackgroundInit): '<S7>/RonDetectEst_BackgroundInit' */
            /* Event: '<S11>:2044' */
            RonDetectEst_BackgroundInit();

            /* End of Outputs for SubSystem: '<S1>/RonDetectEst_BackgroundInit' */

            /* Outputs for Function Call SubSystem: '<S1>/RonDetectEst_MonitoringInit'
             *
             * Block description for '<S1>/RonDetectEst_MonitoringInit':
             *  Function call towards software component RonDetectEst
             */
            /* S-Function (RonDetectEst_MonitoringInit): '<S8>/RonDetectEst_MonitoringInit' */
            /* Event: '<S11>:2043' */
            RonDetectEst_MonitoringInit();

            /* End of Outputs for SubSystem: '<S1>/RonDetectEst_MonitoringInit' */

            /* Outputs for Function Call SubSystem: '<S1>/RonDetectCnt_Reset'
             *
             * Block description for '<S1>/RonDetectCnt_Reset':
             *  This block implements function call towards software component
             *  RonDetectCnt, in order to reset knock events counters.
             */
            /* S-Function (RonDetectCnt_Reset): '<S6>/RonDetectCnt_Reset' */
            /* Event: '<S11>:1421' */
            RonDetectCnt_Reset();

            /* End of Outputs for SubSystem: '<S1>/RonDetectCnt_Reset' */
            CntEOANoTest_g = 0U;

            /* Transition: '<S11>:1545' */
            /* Transition: '<S11>:1546' */
            /* Transition: '<S11>:1547' */
            break;

           default:
            /* Transition: '<S11>:1539' */
            /*  DEFAULT:
               DO NOTHING  */
            break;
          }
        }
        break;
      }
    }
  }

  /* End of Chart: '<S1>/RonDetectMgm' */

  /* SignalConversion generated from: '<S1>/CntEOANoTest' */
  /* Transition: '<S11>:1549' */
  /*  End MAIN statechart  */
  CntEOANoTest = CntEOANoTest_g;

  /* SignalConversion generated from: '<S1>/DKnockInt' */
  for (i = 0; i < 8; i++) {
    DKnockInt[(i)] = DKnockInt_i[i];
  }

  /* End of SignalConversion generated from: '<S1>/DKnockInt' */

  /* SignalConversion generated from: '<S1>/FlgRonStoredEE' */
  FlgRonStoredEE = FlgRonStoredEE_o;

  /* SignalConversion generated from: '<S1>/StRonDetect' */
  StRonDetect = StRonDetect_i;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectMgm_EOA' */
}

/* Model step function */
void RonDetectMgm_PowerOn(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectMgm_PowerOn' incorporates:
   *  SubSystem: '<Root>/PowerOn'
   *
   * Block description for '<Root>/PowerOn':
   *  This block is the Power On task for software component RonDetectMgm
   *
   * Block requirements for '<Root>/PowerOn':
   *  1. EISB_FCA6CYL_SW_REQ_2704: Software shall initialize, at power-on event, the state variable S... (ECU_SW_Requirements#12168)
   */
  /* SignalConversion generated from: '<S2>/DKnockInt' */
  for (i = 0; i < 8; i++) {
    DKnockInt[(i)] = 0U;
  }

  /* End of SignalConversion generated from: '<S2>/DKnockInt' */

  /* SignalConversion generated from: '<S2>/StRonDetect' incorporates:
   *  Constant: '<S2>/Constant49'
   */
  StRonDetect = RD_INIT;

  /* SignalConversion generated from: '<S2>/CntEOANoTest' incorporates:
   *  Constant: '<S2>/Constant13'
   */
  CntEOANoTest = 0U;

  /* Constant: '<S2>/Constant1' */
  IdVer_RonDetectMgm = ID_VER_RONDETECTMGM_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectMgm_PowerOn' */
}

/* Model initialize function */
void RonDetectMgm_initialize(void)
{
  /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectMgm_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   *
   * Block description for '<Root>/EOA':
   *  This block is the EOA task for software component RonDetectMgm
   */
  /* SystemInitialize for Chart: '<S1>/RonDetectMgm' */
  StRonDetectArg = RD_INIT;

  /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectMgm_EOA' */

  /* SystemInitialize for Merge: '<S3>/Merge50' */
  StRonDetect = RD_INIT;
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T StRonDetect;
uint8_T StRonDetectArg;
uint32_T DnockIntArg;
uint8_T FlgRonStoredEE;
uint32_T OdomRonStartEE;
uint32_T OdomRonStopEE;
uint32_T SecRonStartEE;
uint32_T SecRonStopEE;
uint32_T DKnockInt[N_CYL_MAX];
void RonDetectMgm_Stub(void)
{
  uint8_T index = 0u;
  for (index = 0u; index<N_CYL_MAX; index++) {
    DKnockInt[index] = 0u;
  }

  StRonDetect = 0u;
  StRonDetectArg = 0u;
  DnockIntArg = 0u;
  FlgRonStoredEE = 0u;
  OdomRonStartEE = 0u;
  OdomRonStopEE = 0u;
  SecRonStartEE = 0u;
  SecRonStopEE = 0u;
}

void RonDetectMgm_PowerON(void)
{
  RonDetectMgm_Stub();
}

void RonDetectMgm_EOA(void)
{
  RonDetectMgm_Stub();
}

void RonDetectMgm_100ms(void)
{
  RonDetectMgm_Stub();
}

#endif                                 /* _BUILD_RONDETECTMGM_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/