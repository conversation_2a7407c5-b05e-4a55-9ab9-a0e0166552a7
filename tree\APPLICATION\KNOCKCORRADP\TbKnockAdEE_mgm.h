/******************************************************************************/
/* $HeadURL::                                                              $  */
/* $Revision::                                                             $  */
/* $Date::                                                                 $  */
/* $Author::                                                               $  */
/******************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  KnockCorrAdp
**  Filename        :  TbKnockAdEE_mgm.h
**  Created on      :  17-may-2021 08:00:00
**  Original author :  GiuseppeR
******************************************************************************/
/*****************************************************************************
**
**                        TbKnockAdEE_mgm.h Description
**
**  Header file for management of ee-adaptive coefficients for KnockCorrAdp
 *
******************************************************************************/
#ifndef _BUILD_TBKNOCKADEE_MGM_H_
#define _BUILD_TBKNOCKADEE_MGM_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/

#include "rtwtypes.h"

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
#define MIN(a, b) (((a) < (b))? ((a)) : ((b)))

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
#define TBKNOCKADEE_COLUMN_NUMBER 5u
#define TBKNOCKADEE_ROW_NUMBER   96u
#define TBKNOCKADEE_LENGTH      480u
#define MIN_TBKNOCKADEE       -32768 /* -16 * 2048 (2^-11) */
#define MAX_TBKNOCKADEE        30720 /* -16 * 2048 (2^-11) */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/

extern int16_T TbKnockAdEE[480];
/* Tables of the adaptive coefficients for all cylinders */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Get_TbKnockAdEE
**
**   Description:
**    return the value of a cell of TbKnockAdEE 
**
**   Parameters :
**    uint8_T   row index
**    uint8_T   column index
**
**   Returns:
**    int16_T    cell value
**
******************************************************************************/
int16_T Get_TbKnockAdEE(uint8_T rowIndex, uint8_T columnIndex);

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Set_TbKnockAdEE
**
**   Description:
**    assign the value of a cell of TbKnockAdEE 
**
**   Parameters :
**    uint8_T   row index
**    uint8_T   column index
**    int16_T   cell value
**
**   Returns:
**    void
**
******************************************************************************/
void Set_TbKnockAdEE(uint8_T rowIndex, uint8_T columnIndex, int16_T value);

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Interpolate_TbKnockAdEE
**
**   Description:
**    return the value of direct interpolation on TbKnockAdEE table
**
**   Parameters :
**    uint16_T   row index
**    uint16_T   row ratio
**    uint16_T   column index
**    uint16_T   column ratio
**
**   Returns:
**    int16_T    interpolated value
**
******************************************************************************/
int16_T Interpolate_TbKnockAdEE(uint16_T IdX, uint16_T RatioX, uint16_T IdY, uint16_T RatioY);

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/

  
#endif /* _BUILD_TBKNOCKADEE_MGM_H_ */

