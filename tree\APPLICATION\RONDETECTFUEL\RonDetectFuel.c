/*****************************************************************************************************************/
/* $HeadURL:: https://85.42.57.219/svn/Rep_GeOr/Spec_ECU/Application/RonDetectFuel/ED_001/SWE3/branches/SW_00#$  */
/* $Revision:: 262624                                                                                         $  */
/* $Date:: 2025-05-28 10:33:15 +0300 (Wed, 28 May 2025)                                                       $  */
/* $Author:: simsekoglui                                                                                      $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           RonDetectFuel.c
 **  File Creation Date: 28-May-2025
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         RonDetectFuel
 **  Model Description:
 **  Model Version:      1.1020
 **  Model Author:       MarottaR - Fri Feb 01 12:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: akbalikg - Wed May 28 09:29:15 2025
 **
 **  Last Saved Modification:  akbalikg - Tue May 27 11:09:05 2025
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "RonDetectFuel_out.h"
#include "RonDetectFuel_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<S4>/T100ms_Chart' */
#define RonDetectF_IN_RON_DET_COMPLETED ((uint8_T)2U)
#define RonDetectFue_IN_REFUEL_DETECTED ((uint8_T)1U)
#define RonDetectFuel_IN_WAITING_REFUEL ((uint8_T)3U)
#define Ron_IN_WAIT_REFUEL_SUSP_ENABLED ((uint8_T)4U)

/* Named constants for Chart: '<Root>/RonDetectFuel_Scheduler' */
#define RonDe_event_RonDetectFuel_100ms (1)
#define Ron_event_RonDetectFuel_PowerOn (0)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_RONDETECTFUEL_DEF       11020U                    /* Referenced by: '<Root>/RonDetectFuel_Scheduler' */

/* ID model version define */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_RONDETECTFUEL_

/* Add a check for each important define */
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T ENHOLDOLDVALUES = 1;/* Referenced by: '<S4>/T100ms_Chart' */

/* Enable Hold old Ron Value for the first CNTABSTDCRON cycles */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T ENRONSUSPMODE = 0;/* Referenced by: '<S4>/T100ms_Chart' */

/* ENABLE Ron suspicious mode */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T FOREFUELDETECTED = 0;/* Referenced by: '<S4>/T100ms_Chart' */

/* Force RefuelDetectedRON */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T REFUELRONLEVEL = 0;/* Referenced by: '<S4>/T100ms_Chart' */

/* Ron level after refuel */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T RONSUSPLEVELINIT = 1;/* Referenced by: '<S4>/T100ms_Chart' */

/* Ron threshold level to enter suspicious mode */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T RONSUSPLEVELTHR = 4;/* Referenced by: '<S4>/T100ms_Chart' */

/* Ron threshold level to enter suspicious mode */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
//int32_T CntAbsTdc;                     /* '<Root>/CntAbsTdc' */

/* CntAbsTdc counter RefuelDetectRON get set */
uint8_T EcmEisb2RxFlg;                 /* '<Root>/EcmEisb2RxFlg' */

/* Internal latched refuel detected flag */
uint8_T EnSARonOutput;                 /* '<S2>/Merge1' */

/* SARon Output enable flag */
uint8_T FlgResetStRon;                 /* '<S2>/FlgResetStRon' */

/* Reset State machine Ron */
uint8_T RefuelDetected;                /* '<Root>/RefuelDetected' */

/* Internal latched refuel detected flag */
uint8_T RefuelDetectedRON;             /* '<S2>/RefuelDetectedRON' */

/* Internal latched refuel detected flag */
uint8_T RonLevelFuel;                  /* '<Root>/RonLevelFuel' */

/* RON level stored in EE */
uint8_T RonLevelUsed;                  /* '<S2>/RonLevelUsed' */

/* RON level used for SARon calculation */
enum_StRonRefuelChart StRonRefuelChart;/* '<S2>/Merge' */
uint16_T Rpm;                           /* '<Root>/Rpm' */

/* Rpm */
enum_StRonDetect StRonDetect;          /* '<S2>/StRonDetect' */
/* CntAbsTdc counter RefuelDetectRON get set */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_RonDetectFuel;/* '<Root>/RonDetectFuel_Scheduler' */

/* ID model version */

/*End of static test points section*/

/* Block signals and states (default storage) */
DW_RonDetectFuel_T RonDetectFuel_DW;

/* External outputs (root outports fed by signals with default storage) */
ExtY_RonDetectFuel_T RonDetectFuel_Y;

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void RonD_chartstep_c3_RonDetectFuel(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/RonDetectFuel_Scheduler' */
static void RonD_chartstep_c3_RonDetectFuel(const int32_T *sfEvent)
{
  /* Chart: '<Root>/RonDetectFuel_Scheduler'
   *
   * Block description for '<Root>/RonDetectFuel_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  /* Chart: '<Root>/RonDetectFuel_Scheduler'
   *
   * Block description for '<Root>/RonDetectFuel_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  /* During: RonDetectFuel_Scheduler */
  /* Entry Internal: RonDetectFuel_Scheduler */
  /* Transition: '<S3>:2' */
  if ((*sfEvent) == ((int32_T)Ron_event_RonDetectFuel_PowerOn)) {
    /* Outputs for Function Call SubSystem: '<Root>/Init_fcn'
     *
     * Block description for '<Root>/Init_fcn':
     *  This block performs outputs initialization.
     */
    /* SignalConversion generated from: '<S1>/RonLevelUsed' incorporates:
     *  Constant: '<S1>/Constant'
     */
    /* Transition: '<S3>:5' */
    /* Transition: '<S3>:3' */
    /* Event: '<S3>:19' */
    RonLevelUsed = 0U;

    /* SignalConversion generated from: '<S1>/FlgRonStoredEE' incorporates:
     *  Constant: '<S1>/Constant1'
     */
    FlgRonStoredEE = 0U;

    /* SignalConversion generated from: '<S1>/RefuelDetectedRON' incorporates:
     *  Constant: '<S1>/Constant8'
     */
    RefuelDetectedRON = 0U;

    /* SignalConversion generated from: '<S1>/FlgResetStRon' incorporates:
     *  Constant: '<S1>/Constant9'
     */
    FlgResetStRon = 0U;

    /* Outport: '<Root>/CntAbsTdcRON' incorporates:
     *  Constant: '<S1>/Constant10'
     *  SignalConversion generated from: '<S1>/CntAbsTdcRON'
     */
    RonDetectFuel_Y.CntAbsTdcRON = 0U;

    /* SignalConversion generated from: '<S1>/EnSARonOutput' incorporates:
     *  Constant: '<S1>/Constant3'
     */
    EnSARonOutput = 0U;

    /* SignalConversion generated from: '<S1>/StRonRefuelChart' incorporates:
     *  Constant: '<S1>/Constant5'
     */
    StRonRefuelChart = WAITING_REFUEL;

    /* SignalConversion: '<S1>/Signal Copy' incorporates:
     *  Inport: '<Root>/RonLevelFuel'
     */
    //RonLevelFuel = RonLevelEE;
    RonLevelEE = RonLevelFuel;

    /* End of Outputs for SubSystem: '<Root>/Init_fcn' */
    IdVer_RonDetectFuel = ID_VER_RONDETECTFUEL_DEF;

    /* Transition: '<S3>:10' */
  } else {
    /* Outputs for Function Call SubSystem: '<Root>/T100ms_fcn'
     *
     * Block description for '<Root>/T100ms_fcn':
     *  This block produce signals used for fuel ron detection.
     */
    /* Chart: '<S4>/T100ms_Chart' incorporates:
     *  Inport: '<Root>/CntAbsTdc'
     *  Inport: '<Root>/EcmEisb2RxFlg'
     *  Inport: '<Root>/KeySignal'
     *  Inport: '<Root>/RefuelDetected'
     *  Inport: '<Root>/RonLevelFuel'
     *  Inport: '<Root>/Rpm'
     *  Inport: '<Root>/StRonDetect'
     */
    /* Transition: '<S3>:8' */
    /* RonDetectFuel_100ms */
    /* Transition: '<S3>:6' */
    /* Event: '<S3>:18' */
    /* Gateway: T100ms_fcn/T100ms_Chart */
    /* During: T100ms_fcn/T100ms_Chart */
    if (((uint32_T)RonDetectFuel_DW.is_active_c5_RonDetectFuel) == 0U) {
      /* Entry: T100ms_fcn/T100ms_Chart */
      RonDetectFuel_DW.is_active_c5_RonDetectFuel = 1U;

      /* Entry Internal: T100ms_fcn/T100ms_Chart */
      /* Transition: '<S5>:675' */
      RonDetectFuel_DW.is_c5_RonDetectFuel = RonDetectFuel_IN_WAITING_REFUEL;

      /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
      /* Entry 'WAITING_REFUEL': '<S5>:674' */
      StRonRefuelChart = WAITING_REFUEL;
      //RonLevelEE = RonLevelFuel;
      //RonLevelFuel = RonLevelEE;
      RonDetectFuel_DW.EnSARonOutput_a = 0U;
      RonDetectFuel_DW.RonLevelEE_g = RonLevelFuel;
    } else {
      switch (RonDetectFuel_DW.is_c5_RonDetectFuel) {
       case RonDetectFue_IN_REFUEL_DETECTED:
        /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
        StRonRefuelChart = REFUEL_DETECTED;
        RonDetectFuel_DW.RefuelDetectedRON_b = 1U;
        RonDetectFuel_DW.EnSARonOutput_a = 0U;

        /* During 'REFUEL_DETECTED': '<S5>:684' */
        if (((uint32_T)StRonDetect) == RD_TEST_STOP) {
          /* Transition: '<S5>:677' */
          RonDetectFuel_DW.is_c5_RonDetectFuel = RonDetectF_IN_RON_DET_COMPLETED;

          /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
          /* Entry 'RON_DET_COMPLETED': '<S5>:660' */
          StRonRefuelChart = RON_DET_COMPLETED;
        } else {
          /* Transition: '<S5>:671' */
          if ((((int32_T)Rpm) == 0) && (((int32_T)
                RonDetectFuel_DW.FlgResetStRon_l) == 0)) {
            /* Transition: '<S5>:665' */
            RonDetectFuel_DW.FlgResetStRon_l = 1U;
          } else {
            /* Transition: '<S5>:668' */
          }

          if ((((int32_T)KeySignal) == 0) && (((uint32_T)StRonDetect) !=
               RD_TEST_STOP)) {
            /* Transition: '<S5>:673' */
            RonDetectFuel_DW.RonLevelUsed_m = (uint8_T)REFUELRONLEVEL;
            RonDetectFuel_DW.RonLevelEE_g = RonDetectFuel_DW.RonLevelUsed_m;
            RonLevelEE = RonLevelUsed;
            RonDetectFuel_DW.FlgRonStoredEE_e = 0U;
          } else {
            /* Transition: '<S5>:666' */
          }
        }
        break;

       case RonDetectF_IN_RON_DET_COMPLETED:
        /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
        StRonRefuelChart = RON_DET_COMPLETED;

        /* During 'RON_DET_COMPLETED': '<S5>:660' */
        if (((int32_T)RefuelDetected) == 0) {
          /* Transition: '<S5>:678' */
          RonDetectFuel_DW.is_c5_RonDetectFuel = RonDetectFuel_IN_WAITING_REFUEL;

          /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
          /* Entry 'WAITING_REFUEL': '<S5>:674' */
          StRonRefuelChart = WAITING_REFUEL;
          RonDetectFuel_DW.EnSARonOutput_a = 0U;
          RonDetectFuel_DW.RonLevelEE_g = RonLevelFuel;
        }
        break;

       case RonDetectFuel_IN_WAITING_REFUEL:
        /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
        StRonRefuelChart = WAITING_REFUEL;
        RonDetectFuel_DW.EnSARonOutput_a = 0U;
        RonLevelEE = RonLevelFuel;
        /* During 'WAITING_REFUEL': '<S5>:674' */
        /* Transition: '<S5>:686' */
        if ((FOREFUELDETECTED == 1) || (((int32_T)RefuelDetected) == 1)) {
          /* Transition: '<S5>:655' */
          /* Transition: '<S5>:653' */
          if (ENHOLDOLDVALUES == 0) {
            /* Transition: '<S5>:680' */
            RonDetectFuel_DW.RonLevelUsed_m = (uint8_T)REFUELRONLEVEL;
            RonDetectFuel_DW.RonLevelEE_g = RonDetectFuel_DW.RonLevelUsed_m;
            RonLevelEE = RonDetectFuel_DW.RonLevelUsed_m;
            RonLevelEE = RonLevelUsed;
            RonDetectFuel_DW.FlgRonStoredEE_e = 0U;
          } else {
            /* Transition: '<S5>:670' */
            RonDetectFuel_DW.RonLevelUsed_m = RonDetectFuel_DW.RonLevelEE_g;
          }

          RonDetectFuel_DW.is_c5_RonDetectFuel = RonDetectFue_IN_REFUEL_DETECTED;

          /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
          /* Entry 'REFUEL_DETECTED': '<S5>:684' */
          StRonRefuelChart = REFUEL_DETECTED;
          RonDetectFuel_DW.RefuelDetectedRON_b = 1U;
          RonDetectFuel_DW.CntAbsTdcRON_d = (uint32_T)CntAbsTdc;
          RonDetectFuel_DW.EnSARonOutput_a = 0U;
          RonDetectFuel_DW.FlgResetStRon_l = 1U;
        } else {
          /* Transition: '<S5>:654' */
          if ((((int32_T)EcmEisb2RxFlg) != 0) && ((((ENRONSUSPMODE == 1) &&
                 (((int32_T)RonDetectFuel_DW.FlgRonStoredEE_e) == 1)) &&
                (((uint32_T)StRonDetect) != RD_TEST_STOP)) && (((int32_T)
                 RonDetectFuel_DW.RonLevelEE_g) >= ((int32_T)RONSUSPLEVELTHR))))
          {
            /* Transition: '<S5>:657' */
            /* Transition: '<S5>:672' */
            RonDetectFuel_DW.RonLevelUsed_m = (uint8_T)RONSUSPLEVELINIT;
            RonDetectFuel_DW.is_c5_RonDetectFuel =
              Ron_IN_WAIT_REFUEL_SUSP_ENABLED;

            /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
            /* Entry 'WAIT_REFUEL_SUSP_ENABLED': '<S5>:687' */
            StRonRefuelChart = WAIT_REFUEL_SUSP_ENABLED;
            RonDetectFuel_DW.EnSARonOutput_a = 1U;
          } else {
            /* Transition: '<S5>:676' */
            /* Transition: '<S5>:659' */
          }
        }
        break;

       default:
        /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
        StRonRefuelChart = WAIT_REFUEL_SUSP_ENABLED;
        RonDetectFuel_DW.EnSARonOutput_a = 1U;

        /* During 'WAIT_REFUEL_SUSP_ENABLED': '<S5>:687' */
        if (((uint32_T)StRonDetect) == RD_TEST_STOP) {
          /* Transition: '<S5>:681' */
          RonDetectFuel_DW.is_c5_RonDetectFuel = RonDetectF_IN_RON_DET_COMPLETED;

          /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
          /* Entry 'RON_DET_COMPLETED': '<S5>:660' */
          StRonRefuelChart = RON_DET_COMPLETED;
        } else {
          /* Transition: '<S5>:664' */
          if ((FOREFUELDETECTED == 1) || (((int32_T)RefuelDetected) == 1)) {
            /* Transition: '<S5>:661' */
            /* Transition: '<S5>:667' */
            if (ENHOLDOLDVALUES == 0) {
              /* Transition: '<S5>:683' */
              RonDetectFuel_DW.RonLevelUsed_m = (uint8_T)REFUELRONLEVEL;
              RonDetectFuel_DW.RonLevelEE_g = RonDetectFuel_DW.RonLevelUsed_m;
              RonLevelEE = RonLevelUsed;
              RonDetectFuel_DW.FlgRonStoredEE_e = 0U;
              
            } else {
              /* Transition: '<S5>:682' */
              RonDetectFuel_DW.RonLevelUsed_m = RonDetectFuel_DW.RonLevelEE_g;
            }

            RonDetectFuel_DW.is_c5_RonDetectFuel =
              RonDetectFue_IN_REFUEL_DETECTED;

            /* SignalConversion generated from: '<S4>/StRonRefuelChart' */
            /* Entry 'REFUEL_DETECTED': '<S5>:684' */
            StRonRefuelChart = REFUEL_DETECTED;
            RonDetectFuel_DW.RefuelDetectedRON_b = 1U;
            RonDetectFuel_DW.CntAbsTdcRON_d = (uint32_T)CntAbsTdc;
            RonDetectFuel_DW.EnSARonOutput_a = 0U;
            RonDetectFuel_DW.FlgResetStRon_l = 1U;
          } else {
            /* Transition: '<S5>:663' */
          }
        }
        break;
      }
    }

    /* End of Chart: '<S4>/T100ms_Chart' */

    /* Outport: '<Root>/CntAbsTdcRON' incorporates:
     *  SignalConversion generated from: '<S4>/CntAbsTdcRON'
     */
    RonDetectFuel_Y.CntAbsTdcRON = RonDetectFuel_DW.CntAbsTdcRON_d;

    /* SignalConversion generated from: '<S4>/EnSARonOutput' */
    EnSARonOutput = RonDetectFuel_DW.EnSARonOutput_a;

    /* SignalConversion generated from: '<S4>/FlgResetStRon' */
    FlgResetStRon = RonDetectFuel_DW.FlgResetStRon_l;

    /* SignalConversion generated from: '<S4>/FlgRonStoredEE' */
    FlgRonStoredEE = RonDetectFuel_DW.FlgRonStoredEE_e;

    /* SignalConversion generated from: '<S4>/RefuelDetectedRON' */
    RefuelDetectedRON = RonDetectFuel_DW.RefuelDetectedRON_b;

    /* SignalConversion generated from: '<S4>/RonLevelEE' */
    RonLevelEE = RonDetectFuel_DW.RonLevelEE_g;

    /* SignalConversion generated from: '<S4>/RonLevelUsed' */
    RonLevelUsed = RonDetectFuel_DW.RonLevelUsed_m;

    /* End of Outputs for SubSystem: '<Root>/T100ms_fcn' */
  }

  /* End of Chart: '<Root>/RonDetectFuel_Scheduler' */
  /* Transition: '<S3>:13' */
}

/*
 * Output and update for function-call system: '<Root>/RonDetectFuel_Scheduler'
 * Block description for: '<Root>/RonDetectFuel_Scheduler'
 *   This block is the scheduler for the models functions.
 */
void RonDete_RonDetectFuel_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[2];
  int32_T i;

  /* Chart: '<Root>/RonDetectFuel_Scheduler' incorporates:
   *  TriggerPort: '<S3>/input events'
   *
   * Block description for '<Root>/RonDetectFuel_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  for (i = 0; i < 2; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: RonDetectFuel_Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S3>:14' */
    i = (int32_T)Ron_event_RonDetectFuel_PowerOn;
    RonD_chartstep_c3_RonDetectFuel(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S3>:15' */
    i = (int32_T)RonDe_event_RonDetectFuel_100ms;
    RonD_chartstep_c3_RonDetectFuel(&i);
  }
}

/* Model step function */
void RonDetectFuel_100ms(void)
{
  /* Chart: '<Root>/RonDetectFuel_Scheduler'
   *
   * Block description for '<Root>/RonDetectFuel_Scheduler':
   *  This block is the scheduler for the models functions.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectFuel_100ms' */

  /* Chart: '<Root>/RonDetectFuel_Scheduler'
   *
   * Block description for '<Root>/RonDetectFuel_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  RonDete_RonDetectFuel_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectFuel_100ms' */
}

/* Model step function */
void RonDetectFuel_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectFuel_PowerOn' incorporates:
   *  Chart: '<Root>/RonDetectFuel_Scheduler'
   *
   * Block description for '<Root>/RonDetectFuel_Scheduler':
   *  This block is the scheduler for the models functions.
   */

  /* Chart: '<Root>/RonDetectFuel_Scheduler'
   *
   * Block description for '<Root>/RonDetectFuel_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  RonDete_RonDetectFuel_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectFuel_PowerOn' */
}

/* Model initialize function */
void RonDetectFuel_initialize(void)
{
  /* SystemInitialize for Merge: '<S2>/Merge' */
  StRonRefuelChart = WAITING_REFUEL;
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T FlgRonStoredFuel;
uint8_T RonLevelFuel;
uint8_T StReFuel;
uint8_T FuelLevelEE;
uint8_T CntFuelResetDelay;
int16_T DFuelRon;
uint8_T FlgFirstFuelCalc;
uint8_T FlgFuelResetDelay;
uint8_T FlgReFuel;
uint8_T FuelLevel;
int32_T FuelLevelHiR;
void RonDetectFuel_Stub(void)
{
  FlgRonStoredFuel = FlgRonStoredEE;
  RonLevelFuel = RonLevelEE;
  StReFuel = 0u;
  FuelLevelEE = 0u;
  CntFuelResetDelay = 0u;
  DFuelRon = 0;
  FlgFirstFuelCalc = 1u;
  FlgFuelResetDelay = 0u;
  FlgReFuel = 0u;
  FuelLevel = 0u;
  FuelLevelHiR = 0;
}

void RonDetectFuel_PowerOn(void)
{
  RonDetectFuel_Stub();
}

void RonDetectFuel_100ms(void)
{
  RonDetectFuel_Stub();
}

#endif                                 /* _BUILD_RONDETECTFUEL_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/