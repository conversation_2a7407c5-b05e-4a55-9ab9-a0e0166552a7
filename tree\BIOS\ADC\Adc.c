/*****************************************************************************************************************/
/* $HeadURL::                                                                                                  $ */
/* $Revision::                                                                                                 $ */
/* $Date::                                                                                                     $ */
/* $Author::                                                                                                   $ */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  ADC
**  Filename        :  Adc.c
**  Created on      :  20-lug-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef _BUILD_ADC_

/*****************************************************************************
** INCLUDE FILES
****************************************************************************/
#include "Adc.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* Structure for Sigma-Delta calibration values */
SDCalib_T SDCalib[NUM_SD];
uint16_T SDADCGain[NUM_SD];
uint16_T SDADCInvGain[NUM_SD];
int16_T SDADCOffset[NUM_SD];
uint8_T SDADCFlgGainDone[NUM_SD];
uint8_T SDADCFlgOffsetDone[NUM_SD];

/* ADC configuration struct */
ADCChannel_T ADCConfig[NUM_CHANNELS];

uint16_T ADCConfigStatus = 0U;

uint8_T CntSar0TimOut, CntSar2TimOut;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/* Memory address of the SD modules */
static const SDPtr_T SD_PER[NUM_SD] = { &SDADC_0, &SDADC_3 };

/* Memory address of the SAR modules */
static const SARPtr_T SAR_PER[NUM_SAR] = { &SARADC_0, &SARADC_2, &SARADC_4, &SARADC_6, &SARADC_B };

/* Structure for Sigma-Delta configuration parameters */
static const SDConfig_T SD_CONFIG[NUM_SD] = {
    { SD0_MODE, SD0_VCOMSEL, SD0_GAIN, SD0_PDR, SD0_HPASS_ENABLE, SD0_BIAS_ENABLE, SD0_WATCHDOG_ENABLE, SD0_FIFO_ENABLE, SD0_FIFO_FULL_EVENT_ENABLE, SD0_FIFO_FULL_EVENT_TYPE, SD0_FIFO_FULL_THRESHOLD, SD0_FIFO_OW_ENABLE, SD0_HW_TRIG_EN, SD0_HW_TRIG_SOURCE, SD0_HW_TRIG_EDGE },
    { SD3_MODE, SD3_VCOMSEL, SD3_GAIN, SD3_PDR, SD3_HPASS_ENABLE, SD3_BIAS_ENABLE, SD3_WATCHDOG_ENABLE, SD3_FIFO_ENABLE, SD3_FIFO_FULL_EVENT_ENABLE, SD3_FIFO_FULL_EVENT_TYPE, SD3_FIFO_FULL_THRESHOLD, SD3_FIFO_OW_ENABLE, SD3_HW_TRIG_EN, SD3_HW_TRIG_SOURCE, SD3_HW_TRIG_EDGE }
};

/* Structure for SAR configuration parameters */
static const SARConfig_T SAR_CONFIG[NUM_SAR] = {
    { SAR0_MODE, SAR0_OW_ENABLE, SAR0_ISR_ENABLE, SAR0_HW_TRIGGER_ENABLE, SAR0_HW_TRIGGER_EDGE, SAR0_DMA_ENABLE, SAR0_DMA_CHANNELS_NUM, SAR0_CTRL_CRES, SAR0_CTRL_PRECHG, SAR0_CTRL_INPSAMP },
    { SAR2_MODE, SAR2_OW_ENABLE, SAR2_ISR_ENABLE, SAR2_HW_TRIGGER_ENABLE, SAR2_HW_TRIGGER_EDGE, SAR2_DMA_ENABLE, SAR2_DMA_CHANNELS_NUM, SAR2_CTRL_CRES, SAR2_CTRL_PRECHG, SAR2_CTRL_INPSAMP },
    { SAR4_MODE, SAR4_OW_ENABLE, SAR4_ISR_ENABLE, SAR4_HW_TRIGGER_ENABLE, SAR4_HW_TRIGGER_EDGE, SAR4_DMA_ENABLE, SAR4_DMA_CHANNELS_NUM, SAR4_CTRL_CRES, SAR4_CTRL_PRECHG, SAR4_CTRL_INPSAMP },
    { SAR6_MODE, SAR6_OW_ENABLE, SAR6_ISR_ENABLE, SAR6_HW_TRIGGER_ENABLE, SAR6_HW_TRIGGER_EDGE, SAR6_DMA_ENABLE, SAR6_DMA_CHANNELS_NUM, SAR6_CTRL_CRES, SAR6_CTRL_PRECHG, SAR6_CTRL_INPSAMP },
    { SARSV_MODE, SARSV_OW_ENABLE, SARSV_ISR_ENABLE, SARSV_HW_TRIGGER_ENABLE, SARSV_HW_TRIGGER_EDGE, SARSV_DMA_ENABLE, SARSV_DMA_CHANNELS_NUM, SARSV_CTRL_CRES, SARSV_CTRL_PRECHG, SARSV_CTRL_INPSAMP }
};

/* Array for SARADC DMA Channels */
#if (SAR0_DMA_ENABLE == SARADC_DMA_ENABLED)
static const uint8_T SAR0_DMA_CHANNELS[SAR0_ALL_CHANNELS] = { SAR0_DMA_CH1, SAR0_DMA_CH2, SAR0_DMA_CH3, SAR0_DMA_CH4, SAR0_DMA_CH5, SAR0_DMA_CH6, SAR0_CH7, SAR0_CH8, SAR0_CH9 };
#endif
#if (SAR2_DMA_ENABLE == SARADC_DMA_ENABLED)
static const uint8_T SAR2_DMA_CHANNELS[SAR2_ALL_CHANNELS] = { SAR2_DMA_CH1, SAR2_DMA_CH2, SAR2_DMA_CH3, SAR2_DMA_CH4, SAR2_DMA_CH5, SAR2_DMA_CH6 };
#endif
#if (SAR4_DMA_ENABLE == SARADC_DMA_ENABLED)
static const uint8_T SAR4_DMA_CHANNELS[SAR4_ALL_CHANNELS] = { SAR4_DMA_CH1, SAR4_DMA_CH2, SAR4_DMA_CH3, SAR4_DMA_CH4, SAR4_DMA_CH5 };
#endif
#if (SAR6_DMA_ENABLE == SARADC_DMA_ENABLED)
static const uint8_T SAR6_DMA_CHANNELS[SAR6_ALL_CHANNELS] = { SAR6_DMA_CH1, SAR6_DMA_CH2, SAR6_DMA_CH3, SAR6_DMA_CH4, SAR6_DMA_CH5, SAR6_DMA_CH6, SAR6_DMA_CH7, SAR6_DMA_CH8, SAR6_DMA_CH9, SAR6_DMA_CH10 };
#endif
#if (SARSV_DMA_ENABLE == SARADC_DMA_ENABLED)
static const uint8_T SARSV_DMA_CHANNELS[SARSV_ALL_CHANNELS] = { SARSV_DMA_CH1, SARSV_DMA_CH2, SARSV_DMA_CH3, SARSV_DMA_CH4, SARSV_DMA_CH5, SARSV_DMA_CH6, SARSV_DMA_CH7, SARSV_DMA_CH8, SARSV_DMA_CH9, SARSV_DMA_CH10, SARSV_DMA_CH11, SARSV_DMA_CH12, SARSV_DMA_CH13, SARSV_DMA_CH14, SARSV_DMA_CH15, SARSV_DMA_CH16, SARSV_DMA_CH17, SARSV_DMA_CH18, SARSV_DMA_CH19, SARSV_DMA_CH20, SARSV_DMA_CH21, SARSV_DMA_CH22, SARSV_DMA_CH23, SARSV_DMA_CH24, SARSV_DMA_CH25, SARSV_DMA_CH26, SARSV_DMA_CH27, SARSV_DMA_CH28, SARSV_DMA_CH29, SARSV_DMA_CH30 };
#endif

/* Index of the ICIMR/ICNCMR array for SARADCs */
static const uint8_T SAR_ICNCMR_ID[NUM_SAR - 1U] = {SAR0_ICMR_ID, SAR2_ICMR_ID, SAR4_ICMR_ID, SAR6_ICMR_ID};

/* Array for SARADC acquisitions in SCAN MODE */
#if (SAR0_MODE == SARADC_MODE_SCAN)
uint16_T SAR0DataRVal[SAR0_NUM_CHANNELS];
#endif
#if (SAR2_MODE == SARADC_MODE_SCAN)
uint16_T SAR2DataRVal[SAR2_NUM_CHANNELS];
#endif
#if (SAR4_MODE == SARADC_MODE_SCAN)
uint16_T SAR4DataRVal[SAR4_NUM_CHANNELS];
#endif
#if (SAR6_MODE == SARADC_MODE_SCAN)
uint16_T SAR6DataRVal[SAR6_NUM_CHANNELS];
#endif
#if (SARSV_MODE == SARADC_MODE_SCAN)
uint16_T SARSVDataRVal[SARSV_NUM_CHANNELS];
#endif


/* SAR channels */
uint16_T SAR0_CHANNELS[SAR0_ALL_CHANNELS] = { SAR0_CH1, SAR0_CH2, SAR0_CH3, SAR0_CH4, SAR0_CH5, SAR0_CH6, SAR0_CH7, SAR0_CH8, SAR0_CH9 };
uint16_T SAR2_CHANNELS[SAR2_ALL_CHANNELS] = { SAR2_CH1, SAR2_CH2, SAR2_CH3, SAR2_CH4, SAR2_CH5, SAR2_CH6 };
uint16_T SAR4_CHANNELS[SAR4_ALL_CHANNELS] = { SAR4_CH1, SAR4_CH2, SAR4_CH3, SAR4_CH4, SAR4_CH5 };
uint16_T SAR6_CHANNELS[SAR6_ALL_CHANNELS] = { SAR6_CH1, SAR6_CH2, SAR6_CH3, SAR6_CH4, SAR6_CH5, SAR6_CH6, SAR6_CH7, SAR6_CH8, SAR6_CH9, SAR6_CH10 };
uint16_T SARSV_CHANNELS[SARSV_ALL_CHANNELS] = { SARSV_CH1, SARSV_CH2, SARSV_CH3, SARSV_CH4, SARSV_CH5, SARSV_CH6, SARSV_CH7, SARSV_CH8, SARSV_CH9, SARSV_CH10, SARSV_CH11, SARSV_CH12, SARSV_CH13, SARSV_CH14, SARSV_CH15, SARSV_CH16, SARSV_CH17, SARSV_CH18, SARSV_CH19, SARSV_CH20, SARSV_CH21, SARSV_CH22, SARSV_CH23, SARSV_CH24, SARSV_CH25, SARSV_CH26, SARSV_CH27, SARSV_CH28, SARSV_CH29, SARSV_CH30 };

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : ADC_SWTrig_Init
**
**   Description:
**    This method initializes ADC SW Triggered channel for SAR ADC.
**
**   Parameters :
**    [in] uint8_T channelNumber : channel
**    [in] uint8_T analogChannel : analog channel
**    [in] uint8_T per : peripheral (SAR or SD)
**    [in] uint8_T perNumber : peripheral number 
**
**   Returns:
**    NO_ERROR                      - SW Trigger initialized properly
**    ARG_ERROR                     - Argument error
**    PERIPHERAL_ALREADY_CONFIGURED - Analog channel already configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T ADC_SWTrig_Init (uint8_T channelNumber, uint8_T analogChannel, uint8_T per, uint8_T perNumber)
{
    int16_T error = NO_ERROR;

    if ((per < 1U) || (per > 2U) || (perNumber > 6U))
    {
        error = ARG_ERROR;
    }
    else if (ADCConfig[channelNumber].peripheral != 0U)
    {
        error = PERIPHERAL_ALREADY_CONFIGURED;
    }
    else
    {
        ADCConfig[channelNumber].peripheral = per;
        ADCConfig[channelNumber].perNumber = perNumber;
        ADCConfig[channelNumber].analogChannel = analogChannel;
    }

    return error;
}

/******************************************************************************
**   Function    : ADC_Config
**
**   Description:
**    This method configures the SD/SAR ADC peripherals according to the parameters 
**    selected in ADC.cfg and performs Offset/Gain calibration for SDADCs.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void ADC_Config (void)
{
    uint8_T i;

    if (ADCConfigStatus == 0U) 
    {
        /* Initialize ADCConfig structure */
        for (i = 0U; i < NUM_CHANNELS; i++)
        {
            ADCConfig[i].peripheral = 0U;
            ADCConfig[i].perNumber = 0U;
        }

        /* SD 0 */
    #if (SD0_ENABLE == SDADC_ENABLED)
        SDADC_Config(SD_ENGINE_0);
    #endif /* SD0_ENABLE */

        /* SD 3 */
    #if (SD3_ENABLE == SDADC_ENABLED)
        SDADC_Config(SD_ENGINE_3);
    #endif /* SD3_ENABLE */

        /* SAR 0 */
    #if (SAR0_ENABLE == SARADC_ENABLED)
        SARADC_Config(SAR_ENGINE_0);
    #if (SAR0_MODE == SARADC_MODE_SCAN)
        SAR0_ConfigScanCh();
    #endif /* SAR0_MODE */
    #if (SAR0_DMA_ENABLE == SARADC_DMA_ENABLED)
        SAR0_DMAConfig();
    #endif /* SAR0_DMA_ENABLE */
    #endif /* SAR0_ENABLE */

        /* SAR 2 */
    #if (SAR2_ENABLE == SARADC_ENABLED)
        SARADC_Config(SAR_ENGINE_2);
    #if (SAR2_MODE == SARADC_MODE_SCAN)
        SAR2_ConfigScanCh();
    #endif /* SAR2_MODE */
    #if (SAR2_DMA_ENABLE == SARADC_DMA_ENABLED)
        SAR2_DMAConfig();
    #endif /* SAR2_DMA_ENABLE */
    #endif /* SAR2_ENABLE */

        /* SAR 4 */
    #if (SAR4_ENABLE == SARADC_ENABLED)
        SARADC_Config(SAR_ENGINE_4);
    #if (SAR4_MODE == SARADC_MODE_SCAN)
        SAR4_ConfigScanCh();
    #endif /* SAR4_MODE */
    #if (SAR4_DMA_ENABLE == SARADC_DMA_ENABLED)
        SAR4_DMAConfig();
    #endif /* SAR4_DMA_ENABLE */
    #endif /* SAR4_ENABLE */

        /* SAR 6 */
    #if (SAR6_ENABLE == SARADC_ENABLED)
        SARADC_Config(SAR_ENGINE_6);
    #if (SAR6_MODE == SARADC_MODE_SCAN)
        SAR6_ConfigScanCh();
    #endif /* SAR6_MODE */
    #if (SAR6_DMA_ENABLE == SARADC_DMA_ENABLED)
        SAR6_DMAConfig();
    #endif /* SAR6_DMA_ENABLE */
    #endif /* SAR6_ENABLE */

        /* SAR SV */
    #if (SARSV_ENABLE == SARADC_ENABLED)
        SARADC_Config(SAR_ENGINE_SV);
    #if (SARSV_MODE == SARADC_MODE_SCAN)
        SARSV_ConfigScanCh();
    #endif /* SARSV_MODE */
    #if (SARSV_DMA_ENABLE == SARADC_DMA_ENABLED)
        SARSV_DMAConfig();
    #endif /* SARSV_DMA_ENABLE */
    #endif /* SARSV_ENABLE */

        ADCConfigStatus = 1U;
    }
}

/******************************************************************************
**   Function    : ADC_GetSampleResSoftTrig
**
**   Description:
**    Samples software triggered channel.
**
**   Parameters :
**    [in] uint16_T channelNumber : channel
**    [out] uint16_T *convDest : pointer to the variable for the acquired data
**    [in] uint8_T res : resolution
**    [in] uint8_T ceilingPRI : priority
**    [in] uint8_T enableCeilingPRI : priority ceiling enable
**    [in] uint8_T coreCeilingPRI : priority ceiling core
**
**   Returns:
**    NO_ERROR           - Variable acquired correctly
**    PERIPHERAL_FAILURE - Variable not acquired correctly
**    PERIPHERAL_BUSY    - Peripheral busy
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T ADC_GetSampleResSoftTrig (uint16_T channelNumber, uint16_T *convDest, uint8_T res, uint8_T ceilingPRI, uint8_T enableCeilingPRI, uint8_T coreCeilingPRI)
{
    uint16_T result; /* result of conversion */
    int16_T retValue = NO_ERROR;
    int16_T retSarRead = NO_ERROR;
    int16_T retSDRead = NO_ERROR;
    timeoutHandler_t adcTimeout;
    uint8_T adcStatus = TIMEOUT_PENDING;
    uint8_T regIndex;
    uint8_T bitIndex;
    uint32_T stMsrEE;
    uint32_T currPRI;
    SARPtr_T sarPtr;

    if (ADCConfig[channelNumber].peripheral == SD) /* SD */
    {
        if (SD_PER[ADCConfig[channelNumber].perNumber]->MCR.B.EN == 0U) /* SD Busy condition */
        {
            if (enableCeilingPRI == CEIL_PRI_ENABLE) 
            {
                /****************************/ 
                /*  START CRITICAL SECTION  */
                /****************************/
                stMsrEE = ((getSpecReg32MSR()) & (0x8000u));
                if (stMsrEE != 0u)
                {
                    DisableAllInterrupts();
                }
                currPRI = INTC_CUR_PRI_CORE(coreCeilingPRI);
                if ((uint8_T)currPRI < ceilingPRI)
                {
                    __mbar();
                    __isync();
                    INTC_CUR_PRI_CORE(coreCeilingPRI) = ceilingPRI; 
                }
                if (stMsrEE != 0u)
                {
                    EnableAllInterrupts();
                }
                /****************************/ 
                /*   END CRITICAL SECTION   */
                /****************************/
            }   

            /* Start conversion */
            SDADC_StartConversion(ADCConfig[channelNumber].perNumber, ADCConfig[channelNumber].analogChannel);

            /* Read the value if timeout is not expired */
            retSDRead = SDADC_Read(ADCConfig[channelNumber].perNumber, &result);

            SDADC_StopConversion(ADCConfig[channelNumber].perNumber);
            
            if ((retSDRead != SDADC_DATA_VALID) && (retSDRead != SDADC_DATA_VALID_AFTER_TIMEOUT))
            {
                retValue = PERIPHERAL_FAILURE;
            }
            
            if (enableCeilingPRI == CEIL_PRI_ENABLE) 
            {
                /****************************/ 
                /*  START CRITICAL SECTION  */
                /****************************/
                stMsrEE = ((getSpecReg32MSR()) & (0x8000u));
                if (stMsrEE != 0u)
                {
                    DisableAllInterrupts();
                }
                __mbar();
                INTC_CUR_PRI_CORE(coreCeilingPRI) = currPRI;
                if (stMsrEE != 0u)
                {
                    EnableAllInterrupts();
                }
                /****************************/ 
                /*   END CRITICAL SECTION   */
                /****************************/                
            }   
        }
        else
        {
            retValue = PERIPHERAL_BUSY;
        }
    }
    else if (ADCConfig[channelNumber].peripheral == SAR) /* SAR */
    {
        if (SAR_PER[ADCConfig[channelNumber].perNumber - NUM_SD]->MSR.B.NSTART == 0U) /* Check if the requested SAR module is busy */
        {    
            if (enableCeilingPRI == CEIL_PRI_ENABLE) 
            {
                /****************************/ 
                /*  START CRITICAL SECTION  */
                /****************************/
                stMsrEE = ((getSpecReg32MSR()) & (0x8000u));
                if (stMsrEE != 0u)
                {
                    DisableAllInterrupts();
                }
                currPRI = INTC_CUR_PRI_CORE(coreCeilingPRI);
                if ((uint8_T)currPRI < ceilingPRI)
                {
                    __mbar();
                    __isync();
                    INTC_CUR_PRI_CORE(coreCeilingPRI) = ceilingPRI; 
                }
                if (stMsrEE != 0u)
                {
                    EnableAllInterrupts();
                }
                /****************************/ 
                /*   END CRITICAL SECTION   */
                /****************************/
            }   

            sarPtr = SAR_PER[ADCConfig[channelNumber].perNumber - NUM_SD];
            regIndex = (uint8_T)(ADCConfig[channelNumber].analogChannel / 32U);
            bitIndex = (uint8_T)(ADCConfig[channelNumber].analogChannel % 32U);

            /* Enable Normal Conversion Mode for Channel */
            if (ADCConfig[channelNumber].analogChannel < SAR_INT_CHANNEL_NMB)
            {
            sarPtr->ICNCMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
            }
            else
            {
                if (ADCConfig[channelNumber].analogChannel < (SAR_TEST_CHANNEL_NMB + SAR_INT_CHANNEL_NMB))
                {
                    sarPtr->TCNCMR.R |= (uint32_T)(1UL << bitIndex);
                }
            }
            
            /* Start conversion */
            SARADC_StartConversion(ADCConfig[channelNumber].perNumber - NUM_SD);
            
            /* Read the value if timeout is not expired */
            retSarRead = SARADC_Read(ADCConfig[channelNumber].perNumber - NUM_SD, ADCConfig[channelNumber].analogChannel, &result);
            if ((retSarRead != SARADC_VALID_DATA)&&(retSarRead != SARADC_VALID_DATA_AFTER_TIMEOUT))
            {
                retValue = PERIPHERAL_FAILURE;
            }

            /* Disable Normal Conversion Mode for Channel */
            if (ADCConfig[channelNumber].analogChannel < SAR_INT_CHANNEL_NMB)
            {
            sarPtr->ICNCMR[regIndex].R &= (uint32_T)(0UL << bitIndex);
            }
            else
            {
                if (ADCConfig[channelNumber].analogChannel < (SAR_TEST_CHANNEL_NMB + SAR_INT_CHANNEL_NMB))
                {
                    sarPtr->TCNCMR.R &= (uint32_T)(0UL << bitIndex);
                }
            }

            if (enableCeilingPRI == CEIL_PRI_ENABLE) 
            {
                /****************************/ 
                /*  START CRITICAL SECTION  */
                /****************************/
                stMsrEE = ((getSpecReg32MSR()) & (0x8000u));
                if (stMsrEE != 0u)
                {
                    DisableAllInterrupts();
                }
                __mbar();
                INTC_CUR_PRI_CORE(coreCeilingPRI) = currPRI;
                if (stMsrEE != 0u)
                {
                    EnableAllInterrupts();
                }
                /****************************/ 
                /*   END CRITICAL SECTION   */
                /****************************/                
            }   

        }
        else
        {
            retValue = PERIPHERAL_BUSY;
        }
    }

    /* Resolution update */
    if (retValue == NO_ERROR)
    {
        *convDest = result;
    }
    else
    {
        *convDest = 0U;
    }

    return retValue;
}

/******************************************************************************
**   Function    : SARADC_StopConversion
**
**   Description:
**    Stops a SARADC conversion.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SARADC_StopConversion (uint8_T channel)
{
    SARPtr_T sarPtr;

    sarPtr = SAR_PER[channel];

    /* stop conversion */
    sarPtr->MCR.B.NSTART = 0U;
    
}

/******************************************************************************
**   Function    : SARADC_Start
**
**   Description:
**    Activates the SARADC peripheral.
**
**   Parameters :
**    [in] uint8_T Channel : selected SARADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SARADC_Start (uint8_T channel)
{
    SARPtr_T sarPtr;

    sarPtr = SAR_PER[channel - NUM_SD];

    /* Activates  SARADC Peripheral */
    sarPtr->MCR.B.PWDN = 0U;
    
}

/******************************************************************************
**   Function    : SARADC_Stop
**
**   Description:
**    Deactivates the SARADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SARADC_Stop (uint8_T channel)
{
    uint32_T i;
    SARPtr_T sarPtr;
    uint8_T sarICNCMRIndex;

    /* SARADC engine pointer */
    sarPtr = SAR_PER[channel - NUM_SD];

    /* Index of the ICNCMR array of the selected SARADC */
    sarICNCMRIndex = SAR_ICNCMR_ID[channel - NUM_SD];
    
    /* Disables Normal Conversion Mask Registers */
    if (channel != SARSV)
    {
        sarPtr->ICNCMR[sarICNCMRIndex].R = 0U;
    } 
    else 
    {
        for (i = 0U; i < SARB_ICMR_NUM; i++) 
        {
            SARADC_B.ICNCMR[i].R = 0U;
        }
    }

    /* Disable analog watchdog threshold */
    sarPtr->WTIMR.R = 0U;

    /* Puts the SARADC Peripheral in Power-Down Mode.*/
    sarPtr->MCR.B.PWDN = 1U;
    
}

/******************************************************************************
**   Function    : SARADC_SetChannel
**
**   Description:
**    Set the SARADC channel to be acquired.
**
**   Parameters :
**    [in] uint8_T Channel : selected SARADC Engine
**    [in] uint8_T anCh : analog channel to be acquired
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SARADC_SetChannel(uint8_T channel, uint8_T anCh)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    SARPtr_T sarPtr;

    sarPtr = SAR_PER[channel - NUM_SD];

    regIndex = (uint8_T)(anCh / 32U);
    bitIndex = (uint8_T)(anCh % 32U);

    /* Enable Normal Conversion Mode for Channel */
    sarPtr->ICNCMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    
}

/******************************************************************************
**   Function    : SAR0_ReadScanCh
**
**   Description:
**    Read the SAR0 channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SAR0_MODE == SARADC_MODE_SCAN)
void SAR0_ReadScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SAR0 - NUM_SD];

    for (i = 0U; i < SAR0_NUM_CHANNELS; i++) 
    {
        anCh = SAR0_CHANNELS[i];

        if (sarPtr->ICDR[anCh].B.VALID == 1U)
        {
            SAR0DataRVal[i] = (uint16_T)sarPtr->ICDR[anCh].B.CDATA;
        }
    }
}
#endif

/******************************************************************************
**   Function    : SAR2_ReadScanCh
**
**   Description:
**    Read the SAR2 channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SAR2_MODE == SARADC_MODE_SCAN)
void SAR2_ReadScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SAR2 - NUM_SD];

    for (i = 0U; i < SAR2_NUM_CHANNELS; i++) 
    {
        anCh = SAR2_CHANNELS[i];

        if (sarPtr->ICDR[anCh].B.VALID == 1U)
        {
            SAR2DataRVal[i] = (uint16_T)sarPtr->ICDR[anCh].B.CDATA;
        }
    }
}
#endif

/******************************************************************************
**   Function    : SAR4_ReadScanCh
**
**   Description:
**    Read the SAR4 channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SAR4_MODE == SARADC_MODE_SCAN)
void SAR4_ReadScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SAR4 - NUM_SD];
    
    for (i = 0U; i < SAR4_NUM_CHANNELS; i++) 
    {            
        anCh = SAR4_CHANNELS[i];

        if (sarPtr->ICDR[anCh].B.VALID == 1U)
        {
            SAR4DataRVal[i] = (uint16_T)sarPtr->ICDR[anCh].B.CDATA;
        }
    }
}
#endif

/******************************************************************************
**   Function    : SAR6_ReadScanCh
**
**   Description:
**    Read the SAR6 channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SAR6_MODE == SARADC_MODE_SCAN)
void SAR6_ReadScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SAR6 - NUM_SD];

    for (i = 0U; i < SAR6_NUM_CHANNELS; i++) 
    {
        anCh = SAR6_CHANNELS[i];
        
        if (sarPtr->ICDR[anCh].B.VALID == 1U)
        {
            SAR6DataRVal[i] = (uint16_T)sarPtr->ICDR[anCh].B.CDATA;
        }
    }
}
#endif

/******************************************************************************
**   Function    : SARSV_ReadScanCh
**
**   Description:
**    Read the SARSV channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SARSV_MODE == SARADC_MODE_SCAN)
void SARSV_ReadScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SARSV - NUM_SD];
        
    for (i = 0U; i < SARSV_NUM_CHANNELS; i++) 
    {
        anCh = SARSV_CHANNELS[i];

        if (sarPtr->ICDR[anCh].B.VALID == 1U)
        {
            SARSVDataRVal[i] = (uint16_T)sarPtr->ICDR[anCh].B.CDATA;
        }
    }

}
#endif

/******************************************************************************
**   Function    : SDADC_StopConversion
**
**   Description:
**    Stops an SDADC conversion.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SDADC_StopConversion (uint8_T channel)
{

    SDPtr_T sdPtr;

    sdPtr = SD_PER[channel];

    /* stop conversion by powering down ADC module */
    sdPtr->MCR.B.EN = 0U;
    /* Flush FIFO */
    sdPtr->FCR.B.FRST = 1U;
    /* Reset Data FIFO overrun flag*/
    sdPtr->SFR.B.DFORF = 1U;
    /*Reset Data FIFO Full Flag*/
    sdPtr->SFR.B.DFFF = 1U;
}

/******************************************************************************
**   Function    : SDADC_FlushFifo
**
**   Description:
**    Flushes the FIFO and resets the associated status flags without disabling 
**    the peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SDADC_FlushFifo (uint8_T channel)
{

    SDPtr_T sdPtr;

    sdPtr = SD_PER[channel];

    /* Flush FIFO */
    sdPtr->FCR.B.FRST = 1U;
    /* Reset Data FIFO overrun flag*/
    sdPtr->SFR.B.DFORF = 1U;
    /*Reset Data FIFO Full Flag*/
    sdPtr->SFR.B.DFFF = 1U;
}

/******************************************************************************
**   Function    : SDADC_Start
**
**   Description:
**    Activates the SDADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SDADC_Start (uint8_T channel)
{
    SDPtr_T sdPtr;

    sdPtr = SD_PER[channel];

    /* stop conversion by powering down ADC module */
    sdPtr->MCR.B.EN = 1U;
    
}

/******************************************************************************
**   Function    : SDADC_Stop
**
**   Description:
**    Deactivates the SDADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SDADC_Stop (uint8_T channel)
{
    SDPtr_T sdPtr;

    sdPtr = SD_PER[channel];

    /* stop conversion by powering down ADC module */
    sdPtr->MCR.B.EN = 0U;
    
}

/******************************************************************************
**   Function    : SDADC_SetChannel
**
**   Description:
**    Set the SDADC channel to be acquired.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**    [in] uint8_T ch : analog channel to be acquired
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SDADC_SetChannel (uint8_T channel, uint8_T anCh)
{
    SDPtr_T sdPtr;

    sdPtr = SD_PER[channel];

    /* set channel */
    sdPtr->CSR.B.ANCHSEL = anCh;
    
}

/******************************************************************************
**   Function    : SDADC_StartConversion
**
**   Description:
**    Starts an SDADC conversion.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**    [in] uint16_T anCh : analogic channel to acquire
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SDADC_StartConversion (uint8_T channel, uint16_T anCh)
{

    SDPtr_T sdPtr;

    sdPtr = SD_PER[channel];

    /* set channel */
    sdPtr->CSR.B.ANCHSEL = anCh;

    /* Start Conversion */
    sdPtr->MCR.B.EN = 1U;
    sdPtr->RKR.B.RESET_KEY = 0x5AF0U;
}

/******************************************************************************
**   Function    : SAR0_ConfigScanSingleCh
**
**   Description:
**    Set a SAR0 channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR0_ConfigScanSingleCh (uint8_T anch)
{

    /* Disables Normal Conversion and Interrupt Mask Registers */
    SARADC_0.ICNCMR[SAR0_ICMR_ID].R = 0U;
    SARADC_0.ICIMR[SAR0_ICMR_ID].R = 0U;

    /* Enables Normal Conversion Mode and Interrupt for the selected Channel */
    SARADC_0.ICNCMR[SAR0_ICMR_ID].R |= (uint32_T)(1UL << anch);
    SARADC_0.ICIMR[SAR0_ICMR_ID].R |= (uint32_T)(1UL << anch);
    
}

/******************************************************************************
**   Function    : SAR2_ConfigScanSingleCh
**
**   Description:
**    Set a SAR2 channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR2_ConfigScanSingleCh (uint8_T anch)
{

    /* Disables Normal Conversion and Interrupt Mask Registers */
    SARADC_2.ICNCMR[SAR2_ICMR_ID].R = 0U;
    SARADC_2.ICIMR[SAR2_ICMR_ID].R = 0U;

    /* Enables Normal Conversion Mode and Interrupt for the selected Channel */
    SARADC_2.ICNCMR[SAR2_ICMR_ID].R |= (uint32_T)(1UL << anch);
    SARADC_2.ICIMR[SAR2_ICMR_ID].R |= (uint32_T)(1UL << anch);
    
}

/******************************************************************************
**   Function    : SAR4_ConfigScanSingleCh
**
**   Description:
**    Set a SAR4 channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR4_ConfigScanSingleCh (uint8_T anch)
{
    uint8_T bit_index;

    bit_index = (uint8_T)(anch % 32U);

    /* Disables Normal Conversion and Interrupt Mask Registers */
    SARADC_4.ICNCMR[SAR4_ICMR_ID].R = 0U;
    SARADC_4.ICIMR[SAR4_ICMR_ID].R = 0U;

    /* Enables Normal Conversion Mode and Interrupt for the selected Channel */
    SARADC_4.ICNCMR[SAR4_ICMR_ID].R |= (uint32_T)(1UL << bit_index);
    SARADC_4.ICIMR[SAR4_ICMR_ID].R |= (uint32_T)(1UL << bit_index);
    
}

/******************************************************************************
**   Function    : SAR6_ConfigScanSingleCh
**
**   Description:
**    Set a SAR6 channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR6_ConfigScanSingleCh (uint8_T anch)
{
    uint8_T bit_index;

    bit_index = (uint8_T)(anch % 32U);

    /* Disables Normal Conversion and Interrupt Mask Registers */
    SARADC_6.ICNCMR[SAR6_ICMR_ID].R = 0U;
    SARADC_6.ICIMR[SAR6_ICMR_ID].R = 0U;

    /* Enables Normal Conversion Mode and Interrupt for the selected Channel */
    SARADC_6.ICNCMR[SAR6_ICMR_ID].R |= (uint32_T)(1UL << bit_index);
    SARADC_6.ICIMR[SAR6_ICMR_ID].R |= (uint32_T)(1UL << bit_index);
    
}

/******************************************************************************
**   Function    : SARSV_ConfigScanSingleCh
**
**   Description:
**    Set a SARSV channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SARSV_ConfigScanSingleCh (uint8_T anch)
{
    uint8_T reg_index;
    uint8_T bit_index;
    uint8_T i;

    reg_index = (uint8_T)(anch / 32U);
    bit_index = (uint8_T)(anch % 32U);

    /* Disables Normal Conversion and Interrupt Mask Registers */
    for (i=0U; i<SARB_ICMR_NUM; i++) {
        SARADC_B.ICNCMR[i].R = 0U;
        SARADC_B.ICIMR[i].R = 0U;
    }

    /* Enables Normal Conversion Mode and Interrupt for the selected Channel */
    SARADC_B.ICNCMR[reg_index].R |= (uint32_T)(1UL << bit_index);
    SARADC_B.ICIMR[reg_index].R |= (uint32_T)(1UL << bit_index);
}

/******************************************************************************
**   Function    : SAR0_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR0_StartConversion(void) 
{
timeoutHandler_t sarStopToutHlr;
uint8_T sarStatus = TIMEOUT_PENDING; 

    CntSar0TimOut++;
#if (SAR0_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_0.DMAE.B.DMAEN = 1U;
#endif

    SARADC_0.MCR.B.NSTART = 1U;

    TIMING_SetTimeout(SARADC_STOPTIMEOUT, &sarStopToutHlr);
    if (SARADC_0.MSR.B.NSTART != 1U)
    {
        while(sarStatus == TIMEOUT_PENDING)
        {
            TIMING_GetTimeoutStatus(sarStopToutHlr, &sarStatus);
            if (SARADC_0.MSR.B.NSTART == 1u)
            {
                CntSar0TimOut--;
                break;
            }
        }
    }
    else
    {
        CntSar0TimOut--;
    }
}

/******************************************************************************
**   Function    : SAR2_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR2_StartConversion(void) 
{
timeoutHandler_t sarStopToutHlr;
uint8_T sarStatus = TIMEOUT_PENDING; 

    CntSar2TimOut++;
#if (SAR2_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_2.DMAE.B.DMAEN = 1U;
#endif

    SARADC_2.MCR.B.NSTART = 1U;

    TIMING_SetTimeout(SARADC_STOPTIMEOUT, &sarStopToutHlr);
    if (SARADC_2.MSR.B.NSTART != 1U)
    {
        while(sarStatus == TIMEOUT_PENDING)
        {
            TIMING_GetTimeoutStatus(sarStopToutHlr, &sarStatus);
            if (SARADC_2.MSR.B.NSTART == 1u)
            {
                CntSar2TimOut--;
                break;
            }
        }
    }
    else
    {
        CntSar2TimOut--;
    }
}

/******************************************************************************
**   Function    : SAR4_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR4_StartConversion(void) 
{
#if (SAR4_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_4.DMAE.B.DMAEN = 1U;
#endif

    SARADC_4.MCR.B.NSTART = 1U;
}

/******************************************************************************
**   Function    : SAR6_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR6_StartConversion(void) 
{
#if (SAR6_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_6.DMAE.B.DMAEN = 1U;
#endif

    SARADC_6.MCR.B.NSTART = 1U;
}

/******************************************************************************
**   Function    : SARSV_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SARSV_StartConversion(void) 
{
#if (SARSV_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_B.DMAE.B.DMAEN = 1U;
#endif

    SARADC_B.MCR.B.NSTART = 1U;
}

/******************************************************************************
**   Function    : SAR0_StopConversion
**
**   Description:
**    Stops the sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR0_StopConversion(void) 
{
timeoutHandler_t sarStopToutHlr;
uint8_T sarStatus = TIMEOUT_PENDING; 

	SARADC_0.MCR.B.NSTART = 0U;

	TIMING_SetTimeout(SARADC_STOPTIMEOUT, &sarStopToutHlr);
	if (SARADC_0.MSR.B.NSTART != 0U)
    {
    	while(sarStatus == TIMEOUT_PENDING)
        {
    		TIMING_GetTimeoutStatus(sarStopToutHlr, &sarStatus);
			if (SARADC_0.MSR.B.NSTART == 0u)
			{
				break;
			}			
        }
    }

#if (SAR0_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_0.DMAE.B.DMAEN = 0U;
#endif
	SARADC_0.ISR.R = 0xFFU;
}

/******************************************************************************
**   Function    : SAR2_StopConversion
**
**   Description:
**    Stops the sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR2_StopConversion(void) 
{
timeoutHandler_t sarStopToutHlr;
uint8_T sarStatus = TIMEOUT_PENDING;

    SARADC_2.MCR.B.NSTART = 0U;

	TIMING_SetTimeout(SARADC_STOPTIMEOUT, &sarStopToutHlr);
	if (SARADC_2.MSR.B.NSTART != 0U)
    {
    	while(sarStatus == TIMEOUT_PENDING)
        {
    		TIMING_GetTimeoutStatus(sarStopToutHlr, &sarStatus);
			if (SARADC_2.MSR.B.NSTART == 0u)
			{
				break;
			}			
        }
    }

#if (SAR2_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_2.DMAE.B.DMAEN = 0U;
#endif
	SARADC_2.ISR.R = 0xFFU;
}

/******************************************************************************
**   Function    : SAR4_StopConversion
**
**   Description:
**    Stops the sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR4_StopConversion(void) 
{
timeoutHandler_t sarStopToutHlr;
uint8_T sarStatus = TIMEOUT_PENDING;

    SARADC_4.MCR.B.NSTART = 0U;

	TIMING_SetTimeout(SARADC_STOPTIMEOUT, &sarStopToutHlr);
	if (SARADC_4.MSR.B.NSTART != 0U)
    {
    	while(sarStatus == TIMEOUT_PENDING)
        {
    		TIMING_GetTimeoutStatus(sarStopToutHlr, &sarStatus);
			if (SARADC_4.MSR.B.NSTART == 0u)
			{
				break;
			}			
        }
    }

#if (SAR4_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_4.DMAE.B.DMAEN = 0U;
#endif
	SARADC_4.ISR.R = 0xFFU;
}

/******************************************************************************
**   Function    : SAR6_StopConversion
**
**   Description:
**    Stops the sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR6_StopConversion(void) 
{
timeoutHandler_t sarStopToutHlr;
uint8_T sarStatus = TIMEOUT_PENDING;

    SARADC_6.MCR.B.NSTART = 0U;
	
	TIMING_SetTimeout(SARADC_STOPTIMEOUT, &sarStopToutHlr);
	if (SARADC_6.MSR.B.NSTART != 0U)
    {
    	while(sarStatus == TIMEOUT_PENDING)
        {
    		TIMING_GetTimeoutStatus(sarStopToutHlr, &sarStatus);
			if (SARADC_6.MSR.B.NSTART == 0u)
			{
				break;
			}			
        }
    }

#if (SAR6_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_6.DMAE.B.DMAEN = 0U;
#endif
	SARADC_6.ISR.R = 0xFFU;
}

/******************************************************************************
**   Function    : SARSV_StopConversion
**
**   Description:
**    Stops the sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SARSV_StopConversion(void) 
{
timeoutHandler_t sarStopToutHlr;
uint8_T sarStatus = TIMEOUT_PENDING;

    SARADC_B.MCR.B.NSTART = 0U;

	TIMING_SetTimeout(SARADC_STOPTIMEOUT, &sarStopToutHlr);
	if (SARADC_B.MSR.B.NSTART != 0U)
    {
    	while(sarStatus == TIMEOUT_PENDING)
        {
    		TIMING_GetTimeoutStatus(sarStopToutHlr, &sarStatus);
			if (SARADC_B.MSR.B.NSTART == 0u)
			{
				break;
			}			
        }
    }

#if (SARSV_DMA_ENABLE == SARADC_DMA_ENABLED)
    SARADC_B.DMAE.B.DMAEN = 0U;
#endif
}

/******************************************************************************
**   Function    : SAR0_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR0_DMADisable(void) 
{
    SARADC_0.DMAE.B.DMAEN = 0U;
}

/******************************************************************************
**   Function    : SAR2_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR2_DMADisable(void) 
{
    SARADC_2.DMAE.B.DMAEN = 0U;
}

/******************************************************************************
**   Function    : SAR4_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR4_DMADisable(void) 
{
    SARADC_4.DMAE.B.DMAEN = 0U;
}

/******************************************************************************
**   Function    : SAR6_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR6_DMADisable(void) 
{
    SARADC_6.DMAE.B.DMAEN = 0U;
}

/******************************************************************************
**   Function    : SARSV_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SARSV_DMADisable(void) 
{
    SARADC_B.DMAE.B.DMAEN = 0U;
}

/******************************************************************************
**   Function    : SAR0_ConfigOneShotMode
**
**   Description:
**    Configure peripheral in one shot mode.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR0_ConfigOneShotMode(void)
{
    SARADC_0.MCR.B.MODE = 0u;
}

/******************************************************************************
**   Function    : SAR0_ConfigScanMode
**
**   Description:
**    Configure peripheral in scan mode
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR0_ConfigScanMode(void)
{
    SARADC_0.MCR.B.MODE = 1u;
}

/******************************************************************************
**   Function    : SAR2_ConfigOneShotMode
**
**   Description:
**    Configure peripheral in one shot mode.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR2_ConfigOneShotMode(void)
{
    SARADC_2.MCR.B.MODE = 0u;
}

/******************************************************************************
**   Function    : SARSV_DMADisable
**
**   Description:
**    Configure peripheral in scan mode.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SAR2_ConfigScanMode(void)
{
    SARADC_2.MCR.B.MODE = 1u;
}

/******************************************************************************
**   Function    : ADC_SARInit
**
**   Description:
**    Configure SAR peripheral in scan mode.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void ADC_SARInit(void)
{
    SAR0_ConfigScanMode();
    SAR2_ConfigScanMode();
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : ADC_CalibrationSet
**
**   Description:
**    This method performs a Gain/Offset calibration for the selected 
**    SDADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void ADC_CalibrationSet (uint8_T channel)
{

    if (channel == SD_ENGINE_0)
    {

#if (SD0_ENABLE == SDADC_ENABLED)
        SDCalib[SD_ENGINE_0].gain = SDADC_GainCalib(SD_ENGINE_0);
        SDCalib[SD_ENGINE_0].offset = (int16_T)SDADC_OffsetCalib(SD_ENGINE_0);
#endif
    }
    else if (channel == SD_ENGINE_3)
    {

#if (SD3_ENABLE == SDADC_ENABLED)
        SDCalib[SD_ENGINE_3].gain = SDADC_GainCalib(SD_ENGINE_3);
        SDCalib[SD_ENGINE_3].offset = (int16_T)SDADC_OffsetCalib(SD_ENGINE_3);
#endif
    }
    else
    {
        /* Do nothing */
    }
}

/******************************************************************************
**   Function    : SDADC_Config
**
**   Description:
**     Configures and activates the SDADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SDADC_Config (uint8_T channel)
{

    float_T step;
    float_T thr;
    uint16_T low;
    uint16_T high;
    SDPtr_T sdPtr;

    sdPtr = SD_PER[channel];

    /* SD calibration */
    ADC_CalibrationSet(channel);

    /* Flush FIFO */
    sdPtr->FCR.B.FRST = 1U;

    /* Reset Data FIFO overrun flag*/
    sdPtr->SFR.B.DFORF = 1U;

    /*Reset Data FIFO Full Flag*/
    sdPtr->SFR.B.DFFF = 1U;

    /* Set conversion mode */
    sdPtr->MCR.B.MODE = SD_CONFIG[channel].sdMode;

    /* Set VCOMSEL */
    sdPtr->MCR.B.VCOMSEL = SD_CONFIG[channel].sdVcomsel;

    /* set gain */
    sdPtr->MCR.B.PGAN = SD_CONFIG[channel].sdGain;

    /* enable gain error calibration mode*/
    sdPtr->MCR.B.GECEN = 1U;

    /* set output settling time */
    sdPtr->OSDR.B.OSD = SDADC_OSD_75US; //Min value: 18us

    /* set decimation rate */
    sdPtr->MCR.B.PDR = SD_CONFIG[channel].sdPdr;

    /* set highpass filter */
    sdPtr->MCR.B.HPFEN = SD_CONFIG[channel].sdHpassEn;

    /* set BIAS enable */
    sdPtr->CSR.B.BIASEN = SD_CONFIG[channel].sdBiasEn;

    /* Fifo configuration */
    sdPtr->FCR.B.FE = SD_CONFIG[channel].sdFifoEn;
    sdPtr->FCR.B.FOWEN = SD_CONFIG[channel].sdFifoOwEn;
    sdPtr->FCR.B.FTHLD = SD_CONFIG[channel].sdFifoFullThres;

    /* Fifo event configuration */
    if (SD_CONFIG[channel].sdFifoFullEventEn == SDADC_FIFO_FULL_EVENT_ENABLED) 
    { 
        sdPtr->RSER.B.DFFDIRE = SDADC_FIFO_FULL_EVENT_ENABLED;
        sdPtr->RSER.B.DFFDIRS = SD_CONFIG[channel].sdFifoFullEventType;
    } 

    /* enable watchdog */
    sdPtr->MCR.B.WDGEN = SD_CONFIG[channel].sdWdgEn;

#if 0
    /* set low threshold */
    step = (float_T)32768U / (float_T)(SDADC_VREFP - SDADC_VREFN);
    thr = (step * (float_T)SDADC_WATCHDOG_LOWTH * SDCalib[channel].gain) - (float_T)SDCalib[channel].offset;
    low = (uint16_T)thr;
    sdPtr->WTHHLR.B.THRL = low;
  
    /* set high threshold*/
    thr = (step * (float_T)SDADC_WATCHDOG_HIGHTH * SDCalib[channel].gain) - (float_T)SDCalib[channel].offset;
    high = (uint16_T)thr;
    sdPtr->WTHHLR.B.THRH = high;
#endif

    if (SD_CONFIG[channel].sdTrigEn == SDADC_HW_TRIGGER_ENABLED)
    {
        /* Enable trigger */
        sdPtr->MCR.B.TRIGEN = SDADC_HW_TRIGGER_ENABLED;

        /* Select trigger channel on GTM */
        sdPtr->MCR.B.TRIGSEL = SD_CONFIG[channel].sdTrigSource;

        /* Select trigger edge */
        sdPtr->MCR.B.TRIGEDSEL = SD_CONFIG[channel].sdTrigEdge;
    }

    /* enable interrupt */
    // sdPtr->RSER.B.WTHDIRE = 1U;

}

/******************************************************************************
**   Function    : SARADC_Config
**
**   Description:
**     Configures and activates the SARADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SARADC_Config (uint8_T channel)
{

    uint16_T i;
    SARPtr_T sarPtr;

    sarPtr = SAR_PER[channel];

    /* Set power down delay [PDED * 1/(SARADC clock frequency)]. */
    sarPtr->PDEDR.B.PDED = 0x10U;
    /* power up module */
    sarPtr->MCR.B.PWDN = 0U;
    /* Oneshot or Scan mode selection */
    sarPtr->MCR.B.MODE = SAR_CONFIG[channel].sarMode;
    /* Enables converted data to be overwritten by a new conversion */
    sarPtr->MCR.B.OWREN = SAR_CONFIG[channel].sarOwrEn;
    /*  Freeze ADC when enter debug mode */
    sarPtr->MCR.B.FRZ = 1U;

    /* Configure triggers */
    if (SAR_CONFIG[channel].sarTriggerEn == SARADC_TRIGGER_ENABLED) 
    {
        sarPtr->MCR.B.NTRGEN = SAR_CONFIG[channel].sarTriggerEn;
        sarPtr->MCR.B.NEDGESEL = SAR_CONFIG[channel].sarTriggerSel;
    }

    /* configure CTRL registers */
    for (i = 0U; i < 4U; i++)
    {
        sarPtr->CTR[i].B.CRES = SAR_CONFIG[channel].sarCtrlCres;
        sarPtr->CTR[i].B.PRECHG = SAR_CONFIG[channel].sarCtrlPrechg;
        sarPtr->CTR[i].B.INPSAMP = SAR_CONFIG[channel].sarCtrlInpsamp;
    }

    /* Configure analog watchdog thresolds */
    // for (i = 0; i < saradcp->config->numofthresholds; i++) {
    //    float_T step;
    //    float_T thr;
    //    uint16_T low;
    //    uint16_T high;
    //    step = (float_T)4096 / saradcp->config->vref;
    //    thr = step * saradcp->config->thr[i].low;
    //    low = (uint16_T)(thr);
    //    if (saradcp->config->thr[i].high >= saradcp->config->vref) {
    //    high = 0xFFFFU;
    //    }
    //    else {
    //    thr = step * saradcp->config->thr[i].high;
    //    high = (uint16_T)(thr);
    //    }
    //    saradcp->WTHRHLR[i].B.THRL = low;
    //    saradcp->WTHRHLR[i].B.THRH = high;
    // }

    /* configure channels if DMA is not used */
    SARADC_ConfigAnCh(channel);

    /* Clear interrupt Flags */
    sarPtr->ISR.R = 0x3FU;

    /* Enable interrupt for end of conversion */
    sarPtr->IMR.R = SAR_CONFIG[channel].sarIsrEn;

    /* Clear Watchdog interrupt flags */
    // saradcp->WTISR.R = 0xFFUL;

    /* Enable interrupts for configured thresholds */
    // for (i = 0; i < saradcp->config->numofthresholds; i++) {
    //    saradcp->WTIMR.R |= 3UL << (i * 2U);
    // }
}

/******************************************************************************
**   Function    : SAR0_ConfigScanCh
**
**   Description:
**    Set the SAR0 channels to be acquired in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SAR0_ConfigScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SAR0 - NUM_SD];

    for (i = 0u; i < SAR0_NUM_CHANNELS; i++) 
    {
        anCh = SAR0_CHANNELS[i];
        regIndex = (uint8_T)(anCh / 32U);
        bitIndex = (uint8_T)(anCh % 32U);

        /* Enable Normal Conversion Mode for Channel */
        sarPtr->ICNCMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
        sarPtr->ICIMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }
    
}

/******************************************************************************
**   Function    : SAR2_ConfigScanCh
**
**   Description:
**    Set the SAR2 channels to be acquired in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SAR2_ConfigScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SAR2 - NUM_SD];

    for (i = 0u; i < SAR2_NUM_CHANNELS; i++) 
    {
        anCh = SAR2_CHANNELS[i];
        regIndex = (uint8_T)(anCh / 32U);
        bitIndex = (uint8_T)(anCh % 32U);

        /* Enable Normal Conversion Mode for Channel */
        sarPtr->ICNCMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
        sarPtr->ICIMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }
    
}

/******************************************************************************
**   Function    : SAR4_ConfigScanCh
**
**   Description:
**    Set the SAR4 channels to be acquired in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SAR4_ConfigScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SAR4 - NUM_SD];

    for (i = 0u; i < SAR4_NUM_CHANNELS; i++) 
    {
        anCh = SAR4_CHANNELS[i];
        regIndex = (uint8_T)(anCh / 32U);
        bitIndex = (uint8_T)(anCh % 32U);

        /* Enable Normal Conversion Mode for Channel */
        sarPtr->ICNCMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
        sarPtr->ICIMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }
    
}

/******************************************************************************
**   Function    : SAR6_ConfigScanCh
**
**   Description:
**    Set the SAR6 channels to be acquired in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SAR6_ConfigScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SAR6 - NUM_SD];

    for (i = 0u; i < SAR6_NUM_CHANNELS; i++) 
    {
        anCh = SAR6_CHANNELS[i];
        regIndex = (uint8_T)(anCh / 32U);
        bitIndex = (uint8_T)(anCh % 32U);

        /* Enable Normal Conversion Mode for Channel */
        sarPtr->ICNCMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
        sarPtr->ICIMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }
    
}

/******************************************************************************
**   Function    : SARSV_ConfigScanCh
**
**   Description:
**    Set the SARSV channels to be acquired in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SARSV_ConfigScanCh (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint16_T anCh;
    SARPtr_T sarPtr;
    uint8_T i;

    sarPtr = SAR_PER[SARSV - NUM_SD];

    for (i = 0u; i < SARSV_NUM_CHANNELS; i++) 
    {
        anCh = SARSV_CHANNELS[i];
        regIndex = (uint8_T)(anCh / 32U);
        bitIndex = (uint8_T)(anCh % 32U);

        /* Enable Normal Conversion Mode for Channel */
        if (anCh < SAR_INT_CHANNEL_NMB)
        {
        sarPtr->ICNCMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
        sarPtr->ICIMR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }
        else
        {
            if (anCh < (SAR_TEST_CHANNEL_NMB + SAR_INT_CHANNEL_NMB))
            {
                sarPtr->TCNCMR.R |= (uint32_T)(1UL << bitIndex);
                sarPtr->TCIMR.R |= (uint32_T)(1UL << bitIndex);
            }
        }
    
}

}

/******************************************************************************
**   Function    : SAR0_DMAConfig
**
**   Description:
**    Configures DMA for SAR0.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SAR0_DMA_ENABLE == SARADC_DMA_ENABLED)
static void SAR0_DMAConfig (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint8_T dmaChNum;
    uint32_T i;

    dmaChNum = SAR_CONFIG[SAR_ENGINE_0].sarDmaChNum;

    for (i = 0U; i < dmaChNum; i++) 
    {
        regIndex = (uint8_T)(SAR0_DMA_CHANNELS[i] / 32U);
        bitIndex = (uint8_T)(SAR0_DMA_CHANNELS[i] % 32U);
        
        /* Associate channel to DMA */
        SARADC_0.ICDSR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }

    /* Enable DMA */
    SARADC_0.DMAE.B.DMAEN = 1U;

    /* Clear DMA request on data reading */
    SARADC_0.DMAE.B.DCLR = 1U;
}
#endif

/******************************************************************************
**   Function    : SAR2_DMAConfig
**
**   Description:
**    Configures DMA for SAR2.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SAR2_DMA_ENABLE == SARADC_DMA_ENABLED)
static void SAR2_DMAConfig (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint8_T dmaChNum;
    uint32_T i;

    dmaChNum = SAR_CONFIG[SAR_ENGINE_2].sarDmaChNum;

    for (i = 0U; i < dmaChNum; i++) 
    {
        regIndex = (uint8_T)(SAR2_DMA_CHANNELS[i] / 32U);
        bitIndex = (uint8_T)(SAR2_DMA_CHANNELS[i] % 32U);
        
        /* Associate channel to DMA */
        SARADC_2.ICDSR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }

    /* Enable DMA */
    SARADC_2.DMAE.B.DMAEN = 1U;

    /* Clear DMA request on data reading */
    SARADC_2.DMAE.B.DCLR = 1U;
}
#endif

/******************************************************************************
**   Function    : SAR4_DMAConfig
**
**   Description:
**    Configures DMA for SAR4.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SAR4_DMA_ENABLE == SARADC_DMA_ENABLED)
static void SAR4_DMAConfig (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint8_T dmaChNum;
    uint32_T i;

    dmaChNum = SAR_CONFIG[SAR_ENGINE_4].sarDmaChNum;

    for (i = 0U; i < dmaChNum; i++) 
    {
        regIndex = (uint8_T)(SAR4_DMA_CHANNELS[i] / 32U);
        bitIndex = (uint8_T)(SAR4_DMA_CHANNELS[i] % 32U);
        
        /* Associate channel to DMA */
        SARADC_4.ICDSR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }

    /* Enable DMA */
    SARADC_4.DMAE.B.DMAEN = 1U;

    /* Clear DMA request on data reading */
    SARADC_4.DMAE.B.DCLR = 1U;
}
#endif

/******************************************************************************
**   Function    : SAR6_DMAConfig
**
**   Description:
**    Configures DMA for SAR6.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SAR6_DMA_ENABLE == SARADC_DMA_ENABLED)
static void SAR6_DMAConfig (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint8_T dmaChNum;
    uint32_T i;

    dmaChNum = SAR_CONFIG[SAR_ENGINE_6].sarDmaChNum;

    for (i = 0U; i < dmaChNum; i++) 
    {
        regIndex = (uint8_T)(SAR6_DMA_CHANNELS[i] / 32U);
        bitIndex = (uint8_T)(SAR6_DMA_CHANNELS[i] % 32U);
        
        /* Associate channel to DMA */
        SARADC_6.ICDSR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }

    /* Enable DMA */
    SARADC_6.DMAE.B.DMAEN = 1U;

    /* Clear DMA request on data reading */
    SARADC_6.DMAE.B.DCLR = 1U;
}
#endif

/******************************************************************************
**   Function    : SARSV_DMAConfig
**
**   Description:
**    Configures DMA for SARADC Supervisor.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (SARSV_DMA_ENABLE == SARADC_DMA_ENABLED)
static void SARSV_DMAConfig (void)
{
    uint8_T regIndex;
    uint8_T bitIndex;
    uint8_T dmaChNum;
    uint32_T i;

    dmaChNum = SAR_CONFIG[SAR_ENGINE_SV].sarDmaChNum;

    for (i = 0U; i < dmaChNum; i++) 
    {
        regIndex = (uint8_T)(SARSV_DMA_CHANNELS[i] / 32U);
        bitIndex = (uint8_T)(SARSV_DMA_CHANNELS[i] % 32U);
        
        /* Associate channel to DMA */
        SARADC_B.ICDSR[regIndex].R |= (uint32_T)(1UL << bitIndex);
    }

    /* Enable DMA */
    SARADC_B.DMAE.B.DMAEN = 1U;

    /* Clear DMA request on data reading */
    SARADC_B.DMAE.B.DCLR = 1U;
}
#endif

/******************************************************************************
**   Function    : SDADC_OffsetCalib
**
**   Description:
**    This method performs an Offset calibration for the selected SDADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    float_T calibration_value : calibration value
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static float_T SDADC_OffsetCalib (uint8_T channel)
{

    int32_T calibration_value = 0;
    int32_T tmp_offset;
    int16_T convertedValue;
    uint8_T counter;
    int16_T temp[16];
    SDPtr_T sdPtr;

    sdPtr = SD_PER[channel];

    /* Flush FIFO */
    sdPtr->FCR.B.FRST = 1U;

    /* Reset Data Fifo Overrun Flag */
    sdPtr->SFR.B.DFORF = 1U;

    /*Reset Data FIFO Full Flag*/
    sdPtr->SFR.B.DFFF = 1U;

    /* Enable FIFO */
    sdPtr->FCR.B.FE = 1U;

    /* set FIFO size to 16byte*/
    // sdPtr->FCR.B.FSIZE = SDADC_FIFO_16_BYTE; //FIFO size is 16 set by hardware

    /* set fifo threshold to 16 byte*/
    sdPtr->FCR.B.FTHLD = 0x0FU;

    /*Set Output Settling Time */
    sdPtr->OSDR.R = 0x000000FFU;

    /* set gain used to calculate offset calibration */
    sdPtr->MCR.B.PGAN = SDADC_GAIN_1;

    /* Gain Error Mode disabled */
    sdPtr->MCR.B.GECEN = 0U;

    /* select differential mode */
    sdPtr->MCR.B.MODE = SDADC_MODE_DIFFERENTIAL;
    /*
    * select channel to convert depending on which type of conversion
    * will be performed after calibration
    */
    sdPtr->CSR.B.ANCHSEL = SDADC_OFFSET_CALIB_ANCH; /* VREFN - VREFN*/

    /* Disable BIAS on all input */
    sdPtr->CSR.B.BIASEN = 0U;

    /* disable high pass filter*/
    sdPtr->MCR.B.HPFEN = 0U;
    sdPtr->MCR.B.EN = 1U;

    /* start conversion */
    sdPtr->RKR.B.RESET_KEY = 0x5AF0U;

    /* wait until converted datas are ready */
    while (sdPtr->SFR.B.DFFF == 0U)
    {
    }

    for (counter = 0U; counter < 16U; counter++)
    {
        /* read data */
        temp[counter] = (int16_T)((uint16_T)sdPtr->CDR.B.CDATA);
        convertedValue = temp[counter];
        /* calculate offset calibration */
        calibration_value += ((int32_T)0U - (int32_T)convertedValue);
    }

    /* Fixed-point offset */
    tmp_offset = (calibration_value>>4u);
    if (tmp_offset > (int32_T)MAX_int16_T)
    {
        SDADCOffset[channel] = MAX_int16_T;
    }
    else if (tmp_offset < (int32_T)MIN_int16_T)
    {
        SDADCOffset[channel] = MIN_int16_T;
    }
    else
    {
        SDADCOffset[channel] = (int16_T)tmp_offset;
    }
    SDADCFlgOffsetDone[channel] = 1u;

    /*Disable SDADC module */
    sdPtr->MCR.B.EN = 0U;

    /* return average value of calibration value */
    return (float_T)calibration_value / (float_T)16U;
}

/******************************************************************************
**   Function    : SDADC_GainCalib
**
**   Description:
**    This method performs a Gain calibration for the selected SDADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    float_T calculated_gain : calculated gain calibration
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static float_T SDADC_GainCalib (uint8_T channel)
{

    uint8_T counter;
    int32_T calibration_value;
    int32_T tmp_gain_p;
    int32_T tmp_gain_n;
    uint32_T tmp_inv_gain;
    float_T calculated_gain;
    float_T dp, dn;
    int16_T temp[16];
    SDPtr_T sdPtr;

    sdPtr = SD_PER[channel];

    /* Flush FIFO */
    sdPtr->FCR.B.FRST = 1U;

    /* Reset Data Fifo Overrun Flag */
    sdPtr->SFR.B.DFORF = 1U;

    /*Reset Data FIFO Full Flag*/
    sdPtr->SFR.B.DFFF = 1U;

    /* Enable FIFO */
    sdPtr->FCR.B.FE = 1U;

    /* set FIFO size to 16byte*/
    // sdPtr->FCR.B.FSIZE = SDADC_FIFO_16_BYTE; //FIFO size is 16 set by hardware

    /* set fifo threshold to 16 byte*/
    sdPtr->FCR.B.FTHLD = 0x0FU;

    /*Set Output Settling Time */
    sdPtr->OSDR.R = 0x000000FFU;

    /* set decimation rate */
    sdPtr->MCR.B.PDR = SDADC_PDR_OSR_24;

    /* set gain 1 used to calculate gain calibration */
    sdPtr->MCR.B.PGAN = 0U;

    /* Gain Error Mode enabled */
    sdPtr->MCR.B.GECEN = 1U;

    /* select differential mode */
    sdPtr->MCR.B.MODE = SDADC_MODE_DIFFERENTIAL;

    /* Disable BIAS on all input */
    sdPtr->CSR.B.BIASEN = 0U;

    /* disable high pass filter*/
    sdPtr->MCR.B.HPFEN = 0U;

    /* select channel  DREFP - DREFN*/
    sdPtr->CSR.B.ANCHSEL = SDADC_GAIN_CALIB_PH1_ANCH;

    /*Enable SDADC module */
    sdPtr->MCR.B.EN = 1U;

    /* start conversion */
    sdPtr->RKR.B.RESET_KEY = 0x5AF0U;

    /* wait until converted datas are ready */
    while (sdPtr->SFR.B.DFFF == 0U)
    {
    }
    calibration_value = 0;

    /*
    * Expected output if there is no gain error is 0b0111_1001_1001_1001, corresponding to
    * full positive scale after attenuation inserted by the internal filter (1*0.95). The
    * measurement should be repeated to reduce contribution of noise during calibration
    * process. Dp is the average value of attenuated positive full scale
    */
    for (counter = 0U; counter < 16U; counter++)
    {
        temp[counter] = (int16_T)((uint16_T)sdPtr->CDR.B.CDATA);
        calibration_value += temp[counter]; //(int16_T)((uint16_T)sdPtr->sdadc->CDR.B.CDATA);
    }
    
    dp = (float_T)calibration_value / (float_T)16U;

    /* Fixed point */
    tmp_gain_p = (calibration_value>>5u);
    
    /*Disable SDADC module */
    sdPtr->MCR.B.EN = 0U;

    /* Flush FIFO */
    sdPtr->FCR.B.FRST = 1U;

    /* Reset Data Fifo Overrun Flag */
    sdPtr->SFR.B.DFORF = 1U;

    /*Reset Data FIFO Full Flag*/
    sdPtr->SFR.B.DFFF = 1U;

    /* select channel  DREFN - DREFP*/
    sdPtr->CSR.B.ANCHSEL = SDADC_GAIN_CALIB_PH2_ANCH;

    /*Enable SDADC module */
    sdPtr->MCR.B.EN = 1U;

    /* start conversion */
    sdPtr->RKR.B.RESET_KEY = 0x5AF0U;

    /* wait until converted datas are ready */
    while (sdPtr->SFR.B.DFFF == 0U)
    {
    }
    /*
    * Expected output if there is no gain error is 0b1000_0110_0110_0110, corresponding to
    * full negative scale after attenuation inserted by the internal filter (-1*0.95). The
    * measurement should be repeated to reduce contribution of noise during calibration
    * process. Dn is the average value of attenuated negative full scale
    */
    calibration_value = 0;
    counter = 0U;
    for (counter = 0U; counter < 16U; counter++)
    {
        temp[counter] = (int16_T)((uint16_T)sdPtr->CDR.B.CDATA);
        calibration_value += temp[counter]; //(int16_T)((uint16_T)sdPtr->sdadc->CDR.B.CDATA);
    }

    dn = (float_T)calibration_value / (float_T)16U;

    calculated_gain = (dp - dn) / (float_T)65536U;

    /* Fixed-point */
    tmp_gain_n = (calibration_value>>5u);
    tmp_gain_p = (tmp_gain_p - tmp_gain_n);
    tmp_inv_gain = ((uint32_T)1u)<<15u;
    
    if (tmp_gain_p < ((int32_T)MAX_uint16_T))
    {
        SDADCGain[channel] = (uint16_T)tmp_gain_p;

        tmp_inv_gain = (tmp_inv_gain<<15u);
        tmp_inv_gain = tmp_inv_gain/SDADCGain[channel];
        SDADCInvGain[channel] = (uint16_T)tmp_inv_gain;
        
        SDADCFlgGainDone[channel] = 1u;
    }
    else
    {
        /* Calculation not OK */
        SDADCGain[channel] = (uint16_T)tmp_inv_gain;
        SDADCInvGain[channel] = (uint16_T)tmp_inv_gain;
        SDADCFlgGainDone[channel] = 2u;
    }
    
    /*Disable SDADC module */
    sdPtr->MCR.B.EN = 0U;

    return calculated_gain;
}

/******************************************************************************
**   Function    : SARADC_ConfigAnCh
**
**   Description:
**    Configures Channels for Conversion when DMA mode is not used.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SARADC_ConfigAnCh(uint8_T channel)
{
    uint8_T i;
    SARPtr_T sarPtr;

    sarPtr = SAR_PER[channel];

    if (channel == SAR_ENGINE_0) /* SAR 0 */
    {
        for (i = 0U; i < SAR0_NUM_CHANNELS; i++)
        {
            /* Channel Precharge Configuration */
            sarPtr->ICDR[SAR0_CHANNELS[i]].B.PCE = SARADC_PRECHARGE_DISABLED;
            /* Channel CTR register Selection */
            sarPtr->ICDR[SAR0_CHANNELS[i]].B.CTSEL = SARADC_CTR0;

            sarPtr->ICDR[SAR0_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;

#if 0 /* Unused functionality  */
            /* Channel voltage reference Selection (if supported) */
            /* Select threshold register and activate if enabled */
            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
#endif
        }
    }
    else if (channel == SAR_ENGINE_2) /* SAR 2 */
    {
        for (i = 0U; i < SAR2_NUM_CHANNELS; i++)
        {
            /* Channel Precharge Configuration */
            sarPtr->ICDR[SAR2_CHANNELS[i]].B.PCE = SARADC_PRECHARGE_DISABLED;
            /* Channel CTR register Selection */
            sarPtr->ICDR[SAR2_CHANNELS[i]].B.CTSEL = SARADC_CTR0;
            
            sarPtr->ICDR[SAR2_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;

#if 0 /* Unused functionality  */
            /* Channel voltage reference Selection (if supported) */
            /* Select threshold register and activate if enabled */
            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
#endif
        }
    }
    else if (channel == SAR_ENGINE_4) /* SAR 4 */
    {
        for (i = 0U; i < SAR4_NUM_CHANNELS; i++)
        {
            /* Channel Precharge Configuration */
            sarPtr->ICDR[SAR4_CHANNELS[i]].B.PCE = SARADC_PRECHARGE_DISABLED;
            /* Channel CTR register Selection */
            sarPtr->ICDR[SAR4_CHANNELS[i]].B.CTSEL = SARADC_CTR0;
            
            sarPtr->ICDR[SAR4_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;

#if 0 /* Unused functionality  */
            /* Channel voltage reference Selection (if supported) */
            /* Select threshold register and activate if enabled */
            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
#endif
        }
    }
    else if (channel == SAR_ENGINE_6) /* SAR 6 */
    {
        for (i = 0U; i < SAR6_NUM_CHANNELS; i++)
        {
            /* Channel Precharge Configuration */
            sarPtr->ICDR[SAR6_CHANNELS[i]].B.PCE = SARADC_PRECHARGE_DISABLED;
            /* Channel CTR register Selection */
            sarPtr->ICDR[SAR6_CHANNELS[i]].B.CTSEL = SARADC_CTR0;

            sarPtr->ICDR[SAR6_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;

#if 0 /* Unused functionality  */
            /* Channel voltage reference Selection (if supported) */
            /* Select threshold register and activate if enabled */
            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
#endif
        }
    }
    else if (channel == SAR_ENGINE_SV) /* SARSV */
    {
        for (i = 0U; i < SARSV_NUM_CHANNELS; i++)
        {
            /* Channel Precharge Configuration */
            sarPtr->ICDR[SARSV_CHANNELS[i]].B.PCE = SARADC_PRECHARGE_DISABLED;
            /* Channel CTR register Selection */
            sarPtr->ICDR[SARSV_CHANNELS[i]].B.CTSEL = SARADC_CTR0;

            sarPtr->ICDR[SARSV_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;
            
#if 0 /* Unused functionality  */
            /* Channel voltage reference Selection (if supported) */
            /* Select threshold register and activate if enabled */
            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
#endif

        }
    }
}

/******************************************************************************
**   Function    : SARADC_StartConversion
**
**   Description:
**    Starts a SARADC conversion.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SARADC_StartConversion (uint8_T channel)
{

    SARPtr_T sarPtr;
    uint32_T i;
    sarPtr = SAR_PER[channel];

    sarPtr->MCR.B.NTRGEN = 0U;

    /* Start Conversion */
    sarPtr->MCR.B.NSTART = 1U;

}

/******************************************************************************
**   Function    : SARADC_Read
**
**   Description:
**    This method reads the converted value of a SARADC analog channel.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**    [in] uint16_T anCh : analogic channel to acquire
**    [out] uint16_T* convertedValue : pointer to the variable for the acquired data
**
**   Returns:
**    SARADC_VALID_DATA               - Data valid
**    SARADC_INVALID_DATA             - Data invalid
**    PERIPHERAL_FAILURE              - Timeout occurred with invalid data
**    SARADC_VALID_DATA_AFTER_TIMEOUT - Data valid after timeout
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T SARADC_Read (uint8_T channel, uint16_T anCh, uint16_T *convertedValue)
{
    timeoutHandler_t adcTimeout;
    uint8_T adcStatus = TIMEOUT_PENDING;
    uint16_T data;
    int16_T retValue = SARADC_INVALID_DATA;
    SARPtr_T sarPtr;
    uint32_T i; 

    sarPtr = SAR_PER[channel];
    
    if (anCh < SAR_INT_CHANNEL_NMB)
    {
        TIMING_SetTimeout(SARADC_TIMEOUT, &adcTimeout); //SARADC_TIMEOUT in us
        while (retValue == SARADC_INVALID_DATA)
        {
            TIMING_GetTimeoutStatus(adcTimeout, &adcStatus);
            if (adcStatus == TIMEOUT_EXPIRED)
            {
                if (sarPtr->ICDR[anCh].B.VALID == 1U)
                {
                    data = (uint16_T)sarPtr->ICDR[anCh].B.CDATA;
                    retValue = SARADC_VALID_DATA_AFTER_TIMEOUT;
                } else {
                    sarPtr->MCR.B.ABORTCHAIN = 1U;
                    retValue = PERIPHERAL_FAILURE;
                }                
                break;
            }
            else
            {
                if (sarPtr->ICDR[anCh].B.VALID == 1U)
                {
                    data = (uint16_T)sarPtr->ICDR[anCh].B.CDATA;
                    retValue = SARADC_VALID_DATA;
                    break;
                }
            }
        }
    }
    else
    {
        TIMING_SetTimeout(SARADC_TIMEOUT, &adcTimeout); //SARDC_TIMEOUT in us
        while (retValue == SARADC_INVALID_DATA)
        {
            TIMING_GetTimeoutStatus(adcTimeout, &adcStatus);
            if (adcStatus == TIMEOUT_EXPIRED)
            {
                if (sarPtr->TCDR[anCh - SAR_INT_CHANNEL_NMB].B.VALID == 1U)
                {
                    data = (uint16_T)sarPtr->TCDR[anCh - SAR_INT_CHANNEL_NMB].B.CDATA;
                    retValue = SARADC_VALID_DATA_AFTER_TIMEOUT;
                } 
                else 
                {
                    sarPtr->MCR.B.ABORTCHAIN = 1U;
                    retValue = PERIPHERAL_FAILURE;
                }
                break;
            }
            else
            {
                if (sarPtr->TCDR[anCh - SAR_INT_CHANNEL_NMB].B.VALID == 1U)
                {
                    data = (uint16_T)sarPtr->TCDR[anCh - SAR_INT_CHANNEL_NMB].B.CDATA;
                    retValue = SARADC_VALID_DATA;
                    break;
                }
            }
        }
    }

    *convertedValue = data;

    return retValue;
}

/******************************************************************************
**   Function    : SDADC_Read
**
**   Description:
**    Read converted value of a SDADC channel.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**    [out] uint16_T* convertedValue : pointer to the variable for the acquired data
**
**   Returns:
**    SDADC_DATA_VALID               - Data valid
**    SDADC_DATA_NOTVALID            - Data invalid
**    PERIPHERAL_FAILURE             - Timeout occurred with invalid data
**    SDADC_DATA_VALID_AFTER_TIMEOUT - Data valid after timeout
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T SDADC_Read (uint8_T channel, uint16_T *convertedValue)
{

    uint16_T data;
    int16_T retValue = SDADC_DATA_NOTVALID;
    SDPtr_T sdPtr;
    uint16_T i;
    timeoutHandler_t adcTimeout;
    uint8_T adcStatus = TIMEOUT_PENDING;
    sdPtr = SD_PER[channel];

    TIMING_SetTimeout(SDADC_TIMEOUT, &adcTimeout); //SDADC_TIMEOUT in us
    while (retValue == SDADC_DATA_NOTVALID)
    {
        TIMING_GetTimeoutStatus(adcTimeout, &adcStatus);
        if (adcStatus == TIMEOUT_EXPIRED)
        {
            /* Check if valid data is available */
            if (sdPtr->SFR.B.DFEF == 0U)
            {
                /* read converted data */
                data = (uint16_T)sdPtr->CDR.R;

                /* check value */
                if (data > (uint16_T)MAX_int16_T)
                {
                    retValue = SDADC_DATA_NOTVALID;
                    break;
                }
                else
                {

                    /* apply calibration */
                    *convertedValue = data + (uint16_T)SDCalib[channel].offset;
                    *convertedValue = ((*convertedValue) * SDADCInvGain[channel]) >> 15;
                    retValue = SDADC_DATA_VALID_AFTER_TIMEOUT;
                    break;
                } 
            } 
            else 
            {
                retValue = PERIPHERAL_FAILURE;
                break;
            }
        }
        else
        {
            /* Check if valid data is available */
            if (sdPtr->SFR.B.DFEF == 0U)
            {
                /* read converted data */
                data = (uint16_T)sdPtr->CDR.R;

                /* check value */
                if (data < (uint16_T)MAX_int16_T)
                {
                    /* apply calibration */
                    *convertedValue = data + (uint16_T)SDCalib[channel].offset;
                    *convertedValue = ((*convertedValue) * SDADCInvGain[channel]) >> 15;
                    retValue = SDADC_DATA_VALID;
                    break;
                }
            }
            
        }
    }
    return retValue;
}

#endif /* _BUILD_ADC_ */

/****************************************************************************
 ****************************************************************************/
