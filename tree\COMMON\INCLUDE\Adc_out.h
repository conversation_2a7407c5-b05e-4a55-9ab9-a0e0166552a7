/*****************************************************************************************************************/
/* $HeadURL::                                                                                                  $ */
/* $Revision::                                                                                                 $ */
/* $Date::                                                                                                     $ */
/* $Author::                                                                                                   $ */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  ADC
**  Filename        :  Adc_out.h
**  Created on      :  20-lug-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
/*****************************************************************************
**
**                        ADC Description
**
**  ADC Sw module provides driver and interface to manage SAR/SD peripheral on chip.
**
******************************************************************************/
#ifndef _ADC_OUT_H_
#define _ADC_OUT_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "typedefs.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* ADC modules */
#define SD                    1U
#define SAR                   2U

/* ADC peripherals */
#define SD0                   0U
#define SD3                   1U
#define SAR0                  2U
#define SAR2                  3U
#define SAR4                  4U
#define SAR6                  5U
#define SARSV                 6U

/* Voltage reference levels */
#define SARADC_VREFP          5U
#define SARADC_VREFN          0U
#define SDADC_VREFP           5U
#define SDADC_VREFN           0U

/* Ceiling PRI */
#define CEIL_PRI_DISABLE      0U
#define CEIL_PRI_ENABLE       1U
#define CEIL_PRI_CORE0        0U //Z4
#define CEIL_PRI_CORE2        2U //Z2

/* An. channels */
#define AN0                   0U
#define AN1                   1U
#define AN2                   2U
#define AN3                   3U
#define AN4                   4U
#define AN5                   5U
#define AN6                   6U
#define AN7                   7U
#define AN8                   8U
#define AN10                 10U
#define AN11                 11U
#define AN12                 12U
#define AN13                 13U
#define AN16                 16U
#define AN17                 17U
#define AN20                 20U
#define AN21                 21U
#define AN24                 24U
#define AN25                 25U
#define AN35                 35U
#define AN44                 44U
#define AN45                 45U
#define AN46                 46U
#define AN47                 47U
#define AN48                 48U
#define AN51                 51U
#define AN52                 52U
#define AN53                 53U
#define AN56                 56U
#define AN58                 58U
#define AN59                 59U
#define AN60                 60U
#define AN61                 61U
#define AN120               120U
#define AN121               121U

/* Channels */
#define CH0                   0U
#define CH1                   1U
#define CH2                   2U
#define CH3                   3U
#define CH4                   4U
#define CH5                   5U
#define CH6                   6U
#define CH7                   7U
#define CH8                   8U
#define CH9                   9U
#define CH10                 10U
#define CH11                 11U
#define CH12                 12U
#define CH13                 13U
#define CH14                 14U
#define CH15                 15U
#define CH16                 16U
#define CH17                 17U
#define CH18                 18U
#define CH19                 19U
#define CH20                 20U
#define CH21                 21U
#define CH22                 22U
#define CH23                 23U
#define CH24                 24U
#define CH25                 25U
#define CH26                 26U
#define CH27                 27U
#define CH28                 28U
#define CH29                 29U
#define CH30                 30U

/* Number of the modules for each ADC */
#define NUM_SD                2U
#define NUM_SAR               5U

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
#define SDADC0_STOPCONVERSION()  \
        {\
            SDADC_0.MCR.B.EN = 0U;\
            SDADC_0.FCR.B.FRST = 1U;\
            SDADC_0.SFR.B.DFORF = 1U;\
            SDADC_0.SFR.B.DFFF = 1U;\
        }

#define SDADC3_STOPCONVERSION()  \
        {\
            SDADC_3.MCR.B.EN = 0U;\
            SDADC_3.FCR.B.FRST = 1U;\
            SDADC_3.SFR.B.DFORF = 1U;\
            SDADC_3.SFR.B.DFFF = 1U;\
        }

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
typedef float float_T;

/* Calibration structure (SD only) */
typedef struct {
    float_T gain;
    int16_T offset;
} SDCalib_T;

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
extern uint16_T ADCConfigStatus;
extern uint16_T SDADCGain[NUM_SD];
extern uint16_T SDADCInvGain[NUM_SD];
extern int16_T  SDADCOffset[NUM_SD];

extern uint8_T CntSar0TimOut, CntSar2TimOut;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : ADC_GetSampleResSoftTrig
**
**   Description:
**    Samples software triggered channel.
**
**   Parameters :
**    [in] uint16_T channelNumber : channel
**    [out] uint16_T *convDest : pointer to the variable for the acquired data
**    [in] uint8_T res : resolution
**    [in] uint8_T ceilingPRI : priority
**    [in] uint8_T enableCeilingPRI : priority ceiling enable
**    [in] uint8_T coreCeilingPRI : priority ceiling core
**
**   Returns:
**    NO_ERROR           - Variable acquired correctly
**    PERIPHERAL_FAILURE - Variable not acquired correctly
**    PERIPHERAL_BUSY    - Peripheral busy
**
******************************************************************************/
extern int16_T ADC_GetSampleResSoftTrig (uint16_T channelNumber, uint16_T *convDest, uint8_T res, uint8_T ceilingPRI, uint8_T enableCeilingPRI, uint8_T coreCeilingPRI);

/******************************************************************************
**   Function    : ADC_Config
**
**   Description:
**    This method configures the SD/SAR ADC peripherals according to the parameters 
**    selected in ADC.cfg and performs Offset/Gain calibration for SDADCs.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void ADC_Config (void);

/******************************************************************************
**   Function    : ADC_SWTrig_Init
**
**   Description:
**    This method initializes ADC SW Triggered channel for SAR ADC.
**
**   Parameters :
**    [in] uint8_T channelNumber : channel
**    [in] uint8_T analogChannel : analog channel
**    [in] uint8_T per : peripheral (SAR or SD)
**    [in] uint8_T perNumber : peripheral number 
**
**   Returns:
**    NO_ERROR                      - SW Trigger initialized properly
**    ARG_ERROR                     - Argument error
**    PERIPHERAL_ALREADY_CONFIGURED - Analog channel already configured
**
******************************************************************************/
extern int16_T ADC_SWTrig_Init (uint8_T channelNumber, uint8_T analogChannel, uint8_T per, uint8_T perNumber);

/******************************************************************************
**   Function    : SDADC_StopConversion
**
**   Description:
**    Stops an SDADC conversion.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
******************************************************************************/
extern void SDADC_StopConversion (uint8_T channel);

/******************************************************************************
**   Function    : SDADC_FlushFifo
**
**   Description:
**    Flushes the FIFO and resets the associated status flags without disabling 
**    the peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
******************************************************************************/
extern void SDADC_FlushFifo (uint8_T channel);

/******************************************************************************
**   Function    : SARADC_StopConversion
**
**   Description:
**    Stops a SARADC conversion.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARADC_StopConversion(uint8_T channel);

/******************************************************************************
**   Function    : SAR0_ReadScanCh
**
**   Description:
**    Read the SAR0 channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR0_ReadScanCh(void);

/******************************************************************************
**   Function    : SAR2_ReadScanCh
**
**   Description:
**    Read the SAR2 channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SAR2_ReadScanCh(void);

/******************************************************************************
**   Function    : SAR4_ReadScanCh
**
**   Description:
**    Read the SAR4 channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR4_ReadScanCh(void);

/******************************************************************************
**   Function    : SAR6_ReadScanCh
**
**   Description:
**    Read the SAR6 channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR6_ReadScanCh(void);

/******************************************************************************
**   Function    : SARSV_ReadScanCh
**
**   Description:
**    Read the SARSV channels, if configured in SCAN MODE.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARSV_ReadScanCh(void);

/******************************************************************************
**   Function    : SARADC_Start
**
**   Description:
**    Starts a SARADC conversion.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARADC_Start(uint8_T channel);

/******************************************************************************
**   Function    : SARADC_Stop
**
**   Description:
**    Deactivates the SARADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARADC_Stop(uint8_T channel);

/******************************************************************************
**   Function    : SDADC_Start
**
**   Description:
**    Activates the SDADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
******************************************************************************/
extern void SDADC_Start(uint8_T channel);

/******************************************************************************
**   Function    : SDADC_Stop
**
**   Description:
**    Deactivates the SDADC peripheral.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**
**   Returns:
**    void
**
******************************************************************************/
extern void SDADC_Stop (uint8_T channel);

/******************************************************************************
**   Function    : SDADC_SetChannel
**
**   Description:
**    Set the SDADC channel to be acquired.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**    [in] uint8_T anCh : analog channel to be acquired
**
**   Returns:
**    void
**
******************************************************************************/
extern void SDADC_SetChannel (uint8_T channel, uint8_T anCh);

/******************************************************************************
**   Function    : SARADC_SetChannel
**
**   Description:
**    Set the SARADC channel to be acquired.
**
**   Parameters :
**    [in] uint8_T channel : selected SARADC Engine
**    [in] uint8_T anCh : analog channel to be acquired
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARADC_SetChannel(uint8_T channel, uint8_T anCh);

/******************************************************************************
**   Function    : SDADC_StartConversion
**
**   Description:
**    Starts an SDADC conversion.
**
**   Parameters :
**    [in] uint8_T channel : selected SDADC Engine
**    [in] uint16_T anCh : analogic channel to acquire
**
**   Returns:
**    void
**
******************************************************************************/
extern void SDADC_StartConversion (uint8_T channel, uint16_T anCh);

/******************************************************************************
**   Function    : SAR0_ConfigScanSingleCh
**
**   Description:
**    Set a SAR0 channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR0_ConfigScanSingleCh(uint8_T anch);

/******************************************************************************
**   Function    : SAR2_ConfigScanSingleCh
**
**   Description:
**    Set a SAR2 channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR2_ConfigScanSingleCh(uint8_T anch);

/******************************************************************************
**   Function    : SAR4_ConfigScanSingleCh
**
**   Description:
**    Set a SAR4 channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR4_ConfigScanSingleCh(uint8_T anch);

/******************************************************************************
**   Function    : SAR6_ConfigScanSingleCh
**
**   Description:
**    Set a SAR6 channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR6_ConfigScanSingleCh(uint8_T anch);

/******************************************************************************
**   Function    : SARSV_ConfigScanSingleCh
**
**   Description:
**    Set a SARSV channel to be acquired in SCAN MODE.
**
**   Parameters :
**    [in] uint8_T anch : analogic channel to acquire
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARSV_ConfigScanSingleCh(uint8_T anch);

/******************************************************************************
**   Function    : SAR0_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR0_StartConversion(void); 

/******************************************************************************
**   Function    : SAR2_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR2_StartConversion(void);

/******************************************************************************
**   Function    : SAR4_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR4_StartConversion(void);

/******************************************************************************
**   Function    : SAR6_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR6_StartConversion(void);

/******************************************************************************
**   Function    : SARSV_StartConversion
**
**   Description:
**    Starts a new sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARSV_StartConversion(void);

/******************************************************************************
**   Function    : SAR0_StopConversion
**
**   Description:
**    Stops a new conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR0_StopConversion(void);

/******************************************************************************
**   Function    : SAR2_StopConversion
**
**   Description:
**    Stops the sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR2_StopConversion(void);

/******************************************************************************
**   Function    : SAR4_StopConversion
**
**   Description:
**    Stops the sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR4_StopConversion(void);

/******************************************************************************
**   Function    : SAR6_StopConversion
**
**   Description:
**    Stops the sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR6_StopConversion(void);

/******************************************************************************
**   Function    : SARSV_StopConversion
**
**   Description:
**    Stops the sw-triggered conversion.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARSV_StopConversion(void);

/******************************************************************************
**   Function    : SAR0_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR0_DMADisable(void);

/******************************************************************************
**   Function    : SAR2_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR2_DMADisable(void);

/******************************************************************************
**   Function    : SAR4_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR4_DMADisable(void);

/******************************************************************************
**   Function    : SAR6_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR6_DMADisable(void);

/******************************************************************************
**   Function    : SARSV_DMADisable
**
**   Description:
**    Disables the DMA interface.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARSV_DMADisable(void);

/******************************************************************************
**   Function    : SD0_ISR
**
**   Description:
**    ISR handler for SD0.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SD0_ISR(void);

/******************************************************************************
**   Function    : SD3_ISR
**
**   Description:
**    ISR handler for SD3.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SD3_ISR(void);

/******************************************************************************
**   Function    : SAR0_ISR
**
**   Description:
**    ISR handler for SARADC_0.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR0_ISR(void);

/******************************************************************************
**   Function    : SAR2_ISR
**
**   Description:
**    ISR handler for SARADC_2.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR2_ISR(void);

/******************************************************************************
**   Function    : SAR4_ISR
**
**   Description:
**    ISR handler for SARADC_4.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR4_ISR(void);

/******************************************************************************
**   Function    : SAR6_ISR
**
**   Description:
**    ISR handler for SARADC_6.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SAR6_ISR(void);

/******************************************************************************
**   Function    : SARSV_ISR
**
**   Description:
**    ISR handler for SAR Supervisor.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SARSV_ISR(void);

/******************************************************************************
**   Function    : SAR0_ConfigOneShotMode
**
**   Description:
**    Configure peripheral in one shot mode.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
extern void SAR0_ConfigOneShotMode(void);

/******************************************************************************
**   Function    : SAR0_ConfigScanMode
**
**   Description:
**    Configure peripheral in scan mode
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
extern void SAR0_ConfigScanMode(void);

/******************************************************************************
**   Function    : SAR2_ConfigOneShotMode
**
**   Description:
**    Configure peripheral in one shot mode.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
extern void SAR2_ConfigOneShotMode(void);

/******************************************************************************
**   Function    : SARSV_DMADisable
**
**   Description:
**    Configure peripheral in scan mode.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
extern void SAR2_ConfigScanMode(void);

/******************************************************************************
**   Function    : ADC_SARConfig
**
**   Description:
**    Configure SAR peripheral in scan mode.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
extern void ADC_SARInit(void);

#endif /* _ADC_OUT_H_ */

/****************************************************************************
 ****************************************************************************/
