/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  MCAN
**  Filename        :  Mcan_events.c
**  Created on      :  09-giu-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef  _BUILD_CAN_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "typedefs.h"
#include "mcan.h"
#include "Task.h"
#include "Tasksdefs.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
#if (CAN_CHA_EN == 1u)
static uint8_T CanARxExc[MCAN_1_RXBUFF_NUM] = 
{
    CAN_CHA_BUF0_RX_EXC,  CAN_CHA_BUF1_RX_EXC, CAN_CHA_BUF2_RX_EXC, CAN_CHA_BUF3_RX_EXC, CAN_CHA_BUF4_RX_EXC, CAN_CHA_BUF5_RX_EXC,
    CAN_CHA_BUF6_RX_EXC,  CAN_CHA_BUF7_RX_EXC, CAN_CHA_BUF8_RX_EXC, CAN_CHA_BUF9_RX_EXC, CAN_CHA_BUF10_RX_EXC,CAN_CHA_BUF11_RX_EXC,
    CAN_CHA_BUF12_RX_EXC, CAN_CHA_BUF13_RX_EXC, CAN_CHA_BUF14_RX_EXC, CAN_CHA_BUF15_RX_EXC, CAN_CHA_BUF16_RX_EXC, CAN_CHA_BUF17_RX_EXC,
    CAN_CHA_BUF18_RX_EXC, CAN_CHA_BUF19_RX_EXC, CAN_CHA_BUF20_RX_EXC, CAN_CHA_BUF21_RX_EXC, CAN_CHA_BUF22_RX_EXC, CAN_CHA_BUF23_RX_EXC,
    CAN_CHA_BUF24_RX_EXC, CAN_CHA_BUF25_RX_EXC, CAN_CHA_BUF26_RX_EXC, CAN_CHA_BUF27_RX_EXC, CAN_CHA_BUF28_RX_EXC, CAN_CHA_BUF29_RX_EXC,
    CAN_CHA_BUF30_RX_EXC, CAN_CHA_BUF31_RX_EXC, CAN_CHA_BUF32_RX_EXC, CAN_CHA_BUF33_RX_EXC, CAN_CHA_BUF34_RX_EXC, CAN_CHA_BUF35_RX_EXC,
    CAN_CHA_BUF36_RX_EXC, CAN_CHA_BUF37_RX_EXC, CAN_CHA_BUF38_RX_EXC, CAN_CHA_BUF39_RX_EXC, CAN_CHA_BUF40_RX_EXC, CAN_CHA_BUF41_RX_EXC,
    CAN_CHA_BUF42_RX_EXC, CAN_CHA_BUF43_RX_EXC, CAN_CHA_BUF44_RX_EXC, CAN_CHA_BUF45_RX_EXC, CAN_CHA_BUF46_RX_EXC, CAN_CHA_BUF46_RX_EXC,
    CAN_CHA_BUF48_RX_EXC, CAN_CHA_BUF49_RX_EXC, CAN_CHA_BUF50_RX_EXC, CAN_CHA_BUF51_RX_EXC, CAN_CHA_BUF52_RX_EXC, CAN_CHA_BUF53_RX_EXC,
    CAN_CHA_BUF54_RX_EXC, CAN_CHA_BUF55_RX_EXC, CAN_CHA_BUF56_RX_EXC, CAN_CHA_BUF57_RX_EXC, CAN_CHA_BUF58_RX_EXC, CAN_CHA_BUF59_RX_EXC,
    CAN_CHA_BUF60_RX_EXC, CAN_CHA_BUF61_RX_EXC, CAN_CHA_BUF62_RX_EXC, CAN_CHA_BUF63_RX_EXC
};
#endif

#if (CAN_CHB_EN == 1u)
static uint8_T CanBRxExc[MCAN_2_RXBUFF_NUM] = 
{
    CAN_CHB_BUF0_RX_EXC,  CAN_CHB_BUF1_RX_EXC, CAN_CHB_BUF2_RX_EXC, CAN_CHB_BUF3_RX_EXC, CAN_CHB_BUF4_RX_EXC, CAN_CHB_BUF5_RX_EXC,
    CAN_CHB_BUF6_RX_EXC,  CAN_CHB_BUF7_RX_EXC, CAN_CHB_BUF8_RX_EXC, CAN_CHB_BUF9_RX_EXC, CAN_CHB_BUF10_RX_EXC,CAN_CHB_BUF11_RX_EXC,
    CAN_CHB_BUF12_RX_EXC, CAN_CHB_BUF13_RX_EXC, CAN_CHB_BUF14_RX_EXC, CAN_CHB_BUF15_RX_EXC, CAN_CHB_BUF16_RX_EXC, CAN_CHB_BUF17_RX_EXC,
    CAN_CHB_BUF18_RX_EXC, CAN_CHB_BUF19_RX_EXC, CAN_CHB_BUF20_RX_EXC, CAN_CHB_BUF21_RX_EXC, CAN_CHB_BUF22_RX_EXC, CAN_CHB_BUF23_RX_EXC,
    CAN_CHB_BUF24_RX_EXC, CAN_CHB_BUF25_RX_EXC, CAN_CHB_BUF26_RX_EXC, CAN_CHB_BUF27_RX_EXC, CAN_CHB_BUF28_RX_EXC, CAN_CHB_BUF29_RX_EXC,
    CAN_CHB_BUF30_RX_EXC, CAN_CHB_BUF31_RX_EXC, CAN_CHB_BUF32_RX_EXC, CAN_CHB_BUF33_RX_EXC, CAN_CHB_BUF34_RX_EXC, CAN_CHB_BUF35_RX_EXC,
    CAN_CHB_BUF36_RX_EXC, CAN_CHB_BUF37_RX_EXC, CAN_CHB_BUF38_RX_EXC, CAN_CHB_BUF39_RX_EXC, CAN_CHB_BUF40_RX_EXC, CAN_CHB_BUF41_RX_EXC,
    CAN_CHB_BUF42_RX_EXC, CAN_CHB_BUF43_RX_EXC, CAN_CHB_BUF44_RX_EXC, CAN_CHB_BUF45_RX_EXC, CAN_CHB_BUF46_RX_EXC, CAN_CHB_BUF46_RX_EXC,
    CAN_CHB_BUF48_RX_EXC, CAN_CHB_BUF49_RX_EXC, CAN_CHB_BUF50_RX_EXC, CAN_CHB_BUF51_RX_EXC, CAN_CHB_BUF52_RX_EXC, CAN_CHB_BUF53_RX_EXC,
    CAN_CHB_BUF54_RX_EXC, CAN_CHB_BUF55_RX_EXC, CAN_CHB_BUF56_RX_EXC, CAN_CHB_BUF57_RX_EXC, CAN_CHB_BUF58_RX_EXC, CAN_CHB_BUF59_RX_EXC,
    CAN_CHB_BUF60_RX_EXC, CAN_CHB_BUF61_RX_EXC, CAN_CHB_BUF62_RX_EXC, CAN_CHB_BUF63_RX_EXC
};
#endif

extern CanUsedMb_T CanUsedMb[NUM_MCAN_ENGINES];

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : CAN_CHA_L0
**
**   Description:
**    ISR handler for interrupt line 0 on MCAN_1.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CAN_CHA_L0(void) 
{
#if (CAN_CHA_EN == 1u)
    uint8_T rxBuf = 0u;
    uint8_T canLowMbUsed = CanUsedMb[MCAN_ENG_A].rxLowMbUsed;
    uint8_T canHighMbUsed = CanUsedMb[MCAN_ENG_A].rxHighMbUsed;

#if (MCAN_BOFF_ISR_ENABLE == 1u)
#if MCAN_BOFF_INT_LINE == CAN_LINE0_INT

    if (MCAN_1.IR.B.BO == MCAN_BUSOFF_NOT_OCCURRED) /* Check Bus-Off state */
    { 
#endif
#endif

#ifdef MCAN_1_RX_INT_LINE
#if MCAN_1_RX_INT_LINE == CAN_LINE0_INT

#ifdef _BUILD_SAFETYMNGR_INTC_
        /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
        SafetyMngr_INTC_CheckCtx(MCAN1_LINE0_ISR_POS); // Interrupt no. 688

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
        /* Run INT_CHECK_TRIGGER_SET mechanism */
        if ((MCAN_1.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
#ifdef FORCE_SAFETY_ERROR
        || (SAFEERRIDPOS[SAFE_ERR_CANCHAL0_INTC_TRIGSET_POS] == TRUE)
#endif
        )
        {
            SafetyMngr_ReportError((uint16_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE, SF_SM_INTC_CMD, NO_STORED_DIAG, NO_PT_FAULT);
        }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_


  /* check if Rx buffer interrupt occurred on line 0 */
        if (MCAN_1.IR.B.DRX == MCAN_RX_ISR_OCCURRED) 
        {
            if (MCAN_1.ILS.B.DRXL == CAN_LINE0_INT) 
            {
                for (rxBuf = 0u; rxBuf < canLowMbUsed; rxBuf++) 
                {
                    if (((MCAN_1.NDAT1.R) & (1UL << rxBuf)) != 0U) 
                    {
                        CAN_RxData_ISR(MCAN_ENG_A, rxBuf);
                        MCAN_1.NDAT1.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHA_EN == 1u)
#if (CAN_CHA_TXRX_EXC)
                        if (CanARxExc[rxBuf] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChAID);
                        }
#endif
#endif
                    }
                }

                for (rxBuf = 0u; rxBuf < canHighMbUsed; rxBuf++) 
                {
                    if (((MCAN_1.NDAT2.R) & (1UL << rxBuf)) != 0U) 
                    {
                        CAN_RxData_ISR(MCAN_ENG_A, rxBuf + MCAN_RXB_NDAT1_NUMBUF);
                        MCAN_1.NDAT2.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHA_EN == 1u)
#if (CAN_CHA_TXRX_EXC)
                        if (CanARxExc[rxBuf + MCAN_RXB_NDAT1_NUMBUF] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChAID);
                        }
#endif
#endif
                    }
                }
            }

            /* clear flag */
            MCAN_1.IR.R = CAN_IR_DRX;
        }
#endif
#endif

#if (MCAN_BOFF_ISR_ENABLE == 1u)
#if MCAN_BOFF_INT_LINE == CAN_LINE0_INT

    }
    else //Bus-Off state
    {
        CAN_BusOffRecovery(MCAN_ENG_A);
    }
#endif
#endif
#endif
}

/******************************************************************************
**   Function    : CAN_CHA_L1
**
**   Description:
**    ISR handler for interrupt line 1 on MCAN_1.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CAN_CHA_L1(void) 
{
#if (CAN_CHA_EN == 1u)
    uint8_T rxBuf = 0u;
    uint8_T canLowMbUsed = CanUsedMb[MCAN_ENG_A].rxLowMbUsed;
    uint8_T canHighMbUsed = CanUsedMb[MCAN_ENG_A].rxHighMbUsed;

#if (MCAN_BOFF_ISR_ENABLE == 1u)
#if MCAN_BOFF_INT_LINE == CAN_LINE1_INT

    if (MCAN_1.IR.B.BO == MCAN_BUSOFF_NOT_OCCURRED) /* Check Bus-Off state */
    { 
#endif
#endif
      

#ifdef MCAN_1_RX_INT_LINE
#if MCAN_1_RX_INT_LINE == CAN_LINE1_INT

#ifdef _BUILD_SAFETYMNGR_INTC_
        /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
        SafetyMngr_INTC_CheckCtx(MCAN1_LINE1_ISR_POS); // Interrupt no. 689

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
        /* Run INT_CHECK_TRIGGER_SET mechanism */
        if ((MCAN_1.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
#ifdef FORCE_SAFETY_ERROR
                    || (SAFEERRIDPOS[SAFE_ERR_CANCHAL1_INTC_TRIGSET_POS] == TRUE)
#endif
        )
        {
            SafetyMngr_ReportError((uint16_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE, SF_SM_INTC_CMD, NO_STORED_DIAG, NO_PT_FAULT);
        }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

        /* check if Rx buffer interrupt occurred on line 0 */
        if (MCAN_1.IR.B.DRX == MCAN_RX_ISR_OCCURRED) 
        {
            if (MCAN_1.ILS.B.DRXL == CAN_LINE1_INT) 
            {
                for (rxBuf = 0u; rxBuf < canLowMbUsed; rxBuf++) 
                {
                    if (((MCAN_1.NDAT1.R) & (1UL << rxBuf)) != 0U) 
                    {
                        CAN_RxData_ISR(MCAN_ENG_A, rxBuf);
                        MCAN_1.NDAT1.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHA_EN == 1u)
#if (CAN_CHA_TXRX_EXC)
                        if (CanARxExc[rxBuf] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChAID);
                        }
#endif
#endif
                    }
                }

                for (rxBuf = 0u; rxBuf < canHighMbUsed; rxBuf++) 
                {
                    if (((MCAN_1.NDAT2.R) & (1UL << rxBuf)) != 0U) 
                    {
                        CAN_RxData_ISR(MCAN_ENG_A, rxBuf + MCAN_RXB_NDAT1_NUMBUF);
                        MCAN_1.NDAT2.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHA_EN == 1u)
#if (CAN_CHA_TXRX_EXC)
                        if (CanARxExc[rxBuf+ MCAN_RXB_NDAT1_NUMBUF] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChAID);
                        }
#endif
#endif
                    }
                }
            }

            /* clear flag */
            MCAN_1.IR.R = CAN_IR_DRX;          
        }
#endif
#endif

#if (MCAN_BOFF_ISR_ENABLE == 1u)
#if MCAN_BOFF_INT_LINE == CAN_LINE1_INT

    }
    else //Bus-Off state
    {
        CAN_BusOffRecovery(MCAN_ENG_A);
    }
#endif
#endif
#endif
}

#if defined (CAN_CHA_EN) 
#if (CAN_CHA_TXRX_EXC)
/******************************************************************************
**   Function    : FuncCAN_ExTxDoneChA
**
**   Description:
**    Exception task for MCAN_1 transmission
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void FuncCAN_ExTxDoneChA (void)
{
    /*
    This exception will be generated
    at the end of CAN data transmission
    of the MCAN_1
     */
    if(ExTxDoneChA != 0u)
    {
        ExTxDoneChA();
    }
    TerminateTask();
}

/******************************************************************************
**   Function    : FuncCAN_ExRxDoneChA
**
**   Description:
**    Exception task for MCAN_1 reception
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void FuncCAN_ExRxDoneChA (void)
{
    /*
    This exception will be generated
    at the end of CAN data reception
    of the MCAN_1
     */
    if(ExRxDoneChA != 0u)
    {
        ExRxDoneChA();
    }
    TerminateTask();
}
#endif /* #if (CAN_CHA_TXRX_EXC) */
#endif /* #if defined CAN_CHA_EN */

/******************************************************************************
**   Function    : CAN_CHB_L0
**
**   Description:
**    ISR handler for interrupt line 0 on MCAN_2.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CAN_CHB_L0(void) 
{
#if (CAN_CHB_EN == 1u)
    uint8_T rxBuf = 0u;
    uint8_T canLowMbUsed = CanUsedMb[MCAN_ENG_B].rxLowMbUsed;
    uint8_T canHighMbUsed = CanUsedMb[MCAN_ENG_B].rxHighMbUsed;

#if (MCAN_BOFF_ISR_ENABLE == 1u)
#if MCAN_BOFF_INT_LINE == CAN_LINE0_INT

    if (MCAN_2.IR.B.BO == MCAN_BUSOFF_NOT_OCCURRED) /* Check Bus-Off state */
    { 
#endif
#endif

#ifdef MCAN_2_RX_INT_LINE
#if MCAN_2_RX_INT_LINE == CAN_LINE0_INT

#ifdef _BUILD_SAFETYMNGR_INTC_
        /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
        SafetyMngr_INTC_CheckCtx(MCAN2_LINE0_ISR_POS); // Interrupt no. 690

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
        /* Run INT_CHECK_TRIGGER_SET mechanism */
        if ((MCAN_2.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
#ifdef FORCE_SAFETY_ERROR
                || (SAFEERRIDPOS[SAFE_ERR_CANCHBL0_INTC_TRIGSET_POS] == TRUE)
#endif
        )
        {
            SafetyMngr_ReportError((uint16_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE, SF_SM_INTC_CMD, NO_STORED_DIAG, NO_PT_FAULT);
        }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

        /* check if Rx buffer interrupt occurred on line 0 */
        if (MCAN_2.IR.B.DRX == MCAN_RX_ISR_OCCURRED) 
        {
            if (MCAN_2.ILS.B.DRXL == CAN_LINE0_INT) 
            {
                for (rxBuf = 0u; rxBuf < canLowMbUsed; rxBuf++) 
                {
                    if (((MCAN_2.NDAT1.R) & (1UL << rxBuf)) != 0U) 
                    {
                        CAN_RxData_ISR(MCAN_ENG_B, rxBuf);
                        MCAN_2.NDAT1.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHB_EN == 1u)
#if (CAN_CHB_TXRX_EXC)
                        if (CanBRxExc[rxBuf] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChBID);
                        }
#endif
#endif
                    }
                }

                for (rxBuf = 0u; rxBuf < canHighMbUsed; rxBuf++) 
                {
                    if (((MCAN_2.NDAT2.R) & (1UL << rxBuf)) != 0U) 
                    {
                        CAN_RxData_ISR(MCAN_ENG_B, rxBuf + MCAN_RXB_NDAT1_NUMBUF);
                        MCAN_2.NDAT2.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHB_EN == 1u)
#if (CAN_CHB_TXRX_EXC)
                        if (CanBRxExc[rxBuf + MCAN_RXB_NDAT1_NUMBUF] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChBID);
                        }
#endif
#endif
                    }
                }
            }


            /* clear flag */
            MCAN_2.IR.R = CAN_IR_DRX;
        }
#endif
#endif

#if (MCAN_BOFF_ISR_ENABLE == 1u)
#if MCAN_BOFF_INT_LINE == CAN_LINE0_INT
    }
    else //Bus-Off state
    {
        CAN_BusOffRecovery(MCAN_ENG_B);
    }
#endif
#endif
#endif
}

/******************************************************************************
**   Function    : CAN_CHB_L1
**
**   Description:
**    ISR handler for interrupt line 1 on MCAN_2.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CAN_CHB_L1(void) 
{
#if (CAN_CHB_EN == 1u)
    uint8_T rxBuf = 0u;
    uint8_T canLowMbUsed = CanUsedMb[MCAN_ENG_B].rxLowMbUsed;
    uint8_T canHighMbUsed = CanUsedMb[MCAN_ENG_B].rxHighMbUsed;

#if (MCAN_BOFF_ISR_ENABLE == 1u)
#if MCAN_BOFF_INT_LINE == CAN_LINE1_INT

    if (MCAN_2.IR.B.BO == MCAN_BUSOFF_NOT_OCCURRED) /* Check Bus-Off state */
    { 
#endif
#endif      

#ifdef MCAN_2_RX_INT_LINE
#if MCAN_2_RX_INT_LINE == CAN_LINE1_INT

#ifdef _BUILD_SAFETYMNGR_INTC_
        /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
        SafetyMngr_INTC_CheckCtx(MCAN2_LINE1_ISR_POS); // Interrupt no. 691

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
        /* Run INT_CHECK_TRIGGER_SET mechanism */
        if ((MCAN_2.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
#ifdef FORCE_SAFETY_ERROR
            || (SAFEERRIDPOS[SAFE_ERR_CANCHBL1_INTC_TRIGSET_POS] == TRUE)
#endif
        )
        {
            SafetyMngr_ReportError((uint16_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE, SF_SM_INTC_CMD, NO_STORED_DIAG, NO_PT_FAULT);
        }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

        /* check if Rx buffer interrupt occurred on line 0 */
        if (MCAN_2.IR.B.DRX == MCAN_RX_ISR_OCCURRED) 
        {
            if (MCAN_2.ILS.B.DRXL == CAN_LINE1_INT) 
            {
                for (rxBuf = 0u; rxBuf < canLowMbUsed; rxBuf++) 
                {
                    if (((MCAN_2.NDAT1.R) & (1UL << rxBuf)) != 0U) 
                    {
                        CAN_RxData_ISR(MCAN_ENG_B, rxBuf);
                        MCAN_2.NDAT1.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHB_EN == 1u)
#if (CAN_CHB_TXRX_EXC)
                        if (CanBRxExc[rxBuf] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChBID);
                        }
#endif
#endif
                    }
                }

                for (rxBuf = 0u; rxBuf < canHighMbUsed; rxBuf++) 
                {
                    if (((MCAN_2.NDAT2.R) & (1UL << rxBuf)) != 0U) 
                    {
                        CAN_RxData_ISR(MCAN_ENG_B, rxBuf + MCAN_RXB_NDAT1_NUMBUF);
                        MCAN_2.NDAT2.R = (vuint32_t)(1UL << rxBuf);
#if (CAN_CHB_EN == 1u)
#if (CAN_CHB_TXRX_EXC)
                        if (CanBRxExc[rxBuf + MCAN_RXB_NDAT1_NUMBUF] == 1u)
                        {
                            ActivateTask((TaskType)CAN_ExRxDoneChBID);
                        }
#endif
#endif
                    }
                }
            }

            /* clear flag */
            MCAN_2.IR.R = CAN_IR_DRX;
        }
#endif
#endif

#if (MCAN_BOFF_ISR_ENABLE == 1u)
#if MCAN_BOFF_INT_LINE == CAN_LINE1_INT
    }
    else //Bus-Off state
    {
        CAN_BusOffRecovery(MCAN_ENG_B);
    }
#endif
#endif
#endif
}

#if defined (CAN_CHB_EN) 
#if (CAN_CHB_TXRX_EXC)
/******************************************************************************
**   Function    : FuncCAN_ExTxDoneChB
**
**   Description:
**    Exception task for MCAN_2 transmission
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void FuncCAN_ExTxDoneChB (void)
{
    /*
    This exception will be generated
    at the end of CAN data transmission
    of the MCAN_2
     */
    if(ExTxDoneChB != 0u)
    {
        ExTxDoneChB();
    }
    TerminateTask();
}

/******************************************************************************
**   Function    : FuncCAN_ExRxDoneChB
**
**   Description:
**    Exception task for MCAN_2 reception
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void FuncCAN_ExRxDoneChB (void)
{
    /*
    This exception will be generated
    at the end of CAN data reception
    of the MCAN_2
     */
    if(ExRxDoneChB != 0u)
    {
        ExRxDoneChB();
    }
    TerminateTask();
}
#endif /* #if (CAN_CHB_TXRX_EXC) */
#endif /* #if defined CAN_CHB_EN */

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/


#endif /* _BUILD_CAN_ */

/****************************************************************************
 ****************************************************************************/
