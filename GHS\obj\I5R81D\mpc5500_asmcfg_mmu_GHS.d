obj\I5R81D\mpc5500_asmcfg_mmu_GHS.o: \
 ..\tree\BIOS\STARTUP\mpc5500_asmcfg_mmu_GHS.s \
 ..\tree\COMMON\CONFIG\ASM\mpc5500_usrdefs.inc \
 ../tree/COMMON/CONFIG/ASM/mpc5500_defs.inc \
 ..\tree\COMMON\CONFIG\asm\asm_ghs_macros.inc

:cmdList=ccppc -c -MD -I ..\tree\BIOS\GTM\include -I ..\tree\BIOS\GTM\cfg -I ..\tree\COMMON\CONFIG\asm -I ..\tree\COMMON\CONFIG\C -I ..\tree\COMMON\INCLUDE -I ..\tree\COMMON\LIB -I ..\tree\BIOS\COMMON -I ..\tree\AK_OSEK -I ..\tree\DD\COMMON -I ..\tree\APPLICATION\COMMON -I ..\tree\EEPCOM -I ..\common -D__GHS__ -passsource -D__PPC_EABI__ -U__CWWRKS__ --misra_2004=-1.1,1.2-2.1,-2.2,2.3-4.2,-5.1,5.2-5.4,-5.7,6.1-7.1,8.2-8.6,-8.7,8.8-8.9,-8.10,8.11-10.2,-10.3,10.4-11.2,-11.3,11.4-12.6,12.8-19.3,19.5-19.6,-19.7,19.8-19.12,19.14-19.17,-20.1,20.2-21.1 --no_misra_runtime --no_trace_includes -Olimit=peephole,pipeline --no_commons --no_preprocess_linker_directive -list -full_macro_debug_info -full_debug_info --asm_silent --scan_source -no_discard_zero_initializers --no_short_enum --misra_req=error --misra_adv=error -vle -bsp generic -include I5R81D_config.h -object_dir=obj\I5R81D -Ospeed --misra_2004=-5.5-5.6 -c99 -inline_prologue -dwarf2 -g -cpu=ppc5744kz410 --misra_2004=-8.1,-12.7,-19.4,-19.13 -filetype.assembly ..\tree\BIOS\STARTUP\mpc5500_asmcfg_mmu_GHS.s -o obj\I5R81D\mpc5500_asmcfg_mmu_GHS.o ; 
:cmdHash=0x23eb767b

:installDir=c:\ghs\comp_201516
:installDirHash=0x9d4d044d
