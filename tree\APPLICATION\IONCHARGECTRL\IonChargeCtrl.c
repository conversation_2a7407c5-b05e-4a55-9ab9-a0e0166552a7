/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonChargeCtrl.c
 **  File Creation Date: 29-Nov-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonChargeCtrl
 **  Model Description:
 **  Model Version:      1.372
 **  Model Author:       SalimbeniT - Tue Jan 20 16:01:59 2015
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: PanettaM - Mon Nov 29 08:51:25 2021
 **
 **  Last Saved Modification:  PanettaM - Mon Nov 29 08:47:23 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonChargeCtrl_out.h"
#include "IonChargeCtrl_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKKPTHRCION_dim                8U                        /* Referenced by:
                                                                  * '<S27>/BKKPTHRCION_dim'
                                                                  * '<S28>/BKKPTHRCION_dim'
                                                                  */

/* Last index of BKKPTHRCION breakpoint */
#define BKKPTHRDSION_dim               5U                        /* Referenced by:
                                                                  * '<S27>/BKKPTHRDSION_dim'
                                                                  * '<S29>/BKKPTHRDSION_dim'
                                                                  */

/* Last index of BKKPTHRDSION breakpoint */
#define BKRPMSATCION_dim               6U                        /* Referenced by:
                                                                  * '<S17>/BKRPMSATCION_dim'
                                                                  * '<S23>/BKRPMSATCION_dim'
                                                                  * '<S40>/BKRPMSATCION_dim'
                                                                  */

/* Last index of BKRPMSATCION breakpoint */
#define BKVCHARGECION_dim              7U                        /* Referenced by:
                                                                  * '<S17>/BKVCHARGECION_dim'
                                                                  * '<S27>/BKVCHARGECION_dim'
                                                                  * '<S28>/BKVCHARGECION_dim'
                                                                  * '<S29>/BKVCHARGECION_dim'
                                                                  */

/* Last index of BKVCHARGECION breakpoint */
#define DIVERRINTCION_FACT_DEF         2U                        /* Referenced by: '<S37>/DIVERRINTCION_FACT_DEF' */

/* Factor 2 to calculate DivErrIntCION. */
#define ID_VER_IONCHARGECTRL_DEF       1372U                     /* Referenced by: '<S3>/ID_VER_IONCHARGECTRL_DEF' */

/* Id model version define */
#define MAX_VSUPCRG_DEF                9600                      /* Referenced by: '<S3>/MAX_VSUPCRG_DEF' */

/* Maximum value for VSupVCharge */
#define ONE                            1U                        /* Referenced by:
                                                                  * '<S1>/VChargeVect_calc'
                                                                  * '<S28>/Anti_wind_up'
                                                                  */

/* Define for value 1 */
#define TIMEDELAYTIMCMDSTALLION        1U                        /* Referenced by: '<S1>/VChargeVect_calc' */

/* Time delay to detect a stall */
#define ZERO                           0                         /* Referenced by: '<S1>/VChargeVect_calc' */

/* 0 value define */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONCHARGECTRL_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint16_T VChargeObjNom_tbl;     /* '<S15>/Conversion' */

/*Exported calibration memory section */
/*Init of exported calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_EXPORT_CALIBRATION */
CALQUAL CALQUAL_POST uint16_T BKLOADVCRGION[9] = { 0U, 1280U, 2560U, 5120U,
  6400U, 7680U, 8960U, 10240U, 12800U } ;/* Referenced by:
                                          * '<S1>/BKLOADVCRGION'
                                          * '<S17>/BKLOADVCRGION'
                                          */

/* Load VChargeObj breakpoint */
CALQUAL CALQUAL_POST uint16_T BKRPMVCRGION[10] = { 0U, 1000U, 2000U, 3000U,
  4000U, 5000U, 6000U, 7000U, 8000U, 9000U } ;/* Referenced by: '<S1>/BKRPMVCRGION' */

/* Rpm VchargeObj breakpoint */

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKKPTHRCION[9] = { -800, -640, -32, 32,
  112, 240, 400, 640, 960 } ;          /* Referenced by: '<S27>/BKKPTHRCION' */

/* PI CION Error breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKKPTHRDSION[6] = { -800, -640, -32, 32,
  112, 240 } ;                         /* Referenced by: '<S27>/BKKPTHRDSION' */

/* PI CION discharge Error breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMSATCION[7] = { 600U, 800U, 1100U,
  1800U, 2500U, 3500U, 6000U } ;       /* Referenced by: '<S17>/BKRPMSATCION' */

/* Rpm Max Saturation CION breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKVCHARGECION[8] = { 0, 800, 1600, 2400,
  3200, 4000, 4800, 5600 } ;          /* Referenced by: '<S17>/BKVCHARGECION' */

/* Vcharge breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T CIDISCERRMAXSAT = 32768U;
                                    /* Referenced by: '<S29>/CIDISCERRMAXSAT' */

/* Max P Sat discharge */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T CIDISCERRMINSAT = 0U;
                                    /* Referenced by: '<S29>/CIDISCERRMINSAT' */

/* Min P Sat discharge */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T CIMINERRINTSAT = 0U;
                                     /* Referenced by: '<S28>/CIMINERRINTSAT' */

/* Low threshold to saturate the integral PI part */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T CNTTDCCIONCL = 10U;/* Referenced by: '<S19>/CNTTDCCIONCL' */

/* Number Tdc after stall to enable VCharge PI control */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENCLPSELOL = 1;/* Referenced by: '<S37>/ENCLPSELOL' */

/* Enable clamp reset by OL ctrl */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T FOFLGCIONDISC = -1;
                                      /* Referenced by: '<S27>/FOFLGCIONDISC' */

/* Force closed loop working mode (-1 for not forced, 0 force charge phase, 1 force discharge phase). */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T GNTIMTHRCION = 0;/* Referenced by: '<S31>/GNTIMTHRCION' */

/* Gain to convert the PI output into a delay time portion to be added at the nominal delay to acquire ION capacitor voltage */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T LATCHVCAPPICTRL = 1;
                                    /* Referenced by: '<S19>/LATCHVCAPPICTRL' */

/* Latch PI Ctrl when it's set to 0 */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXSATVCRGOBJ = 8000U;/* Referenced by: '<S1>/MAXSATVCRGOBJ' */

/* High saturation for ION capacitor nominal voltage target */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXTHRIONCRG = 16220U;/* Referenced by:
                                                                 * '<S30>/MAXTHRIONCRG'
                                                                 * '<S31>/MAXTHRIONCRG'
                                                                 */

/* Maximum threshold value to start ION charge capacitor */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MINSATVCRGOBJ = 0U;/* Referenced by: '<S1>/MINSATVCRGOBJ' */

/* Low saturation for ION capacitor nominal voltage target */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T PMOS_CONFIG = 4U;/* Referenced by: '<S1>/PMOS_CONFIG' */

/* Number of PMOS configured in the system */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T SELCIONOL = 0;/* Referenced by: '<S19>/SELCIONOL' */

/* Enable OL VCharge strategy */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T SELERCION0 = 0U;/* Referenced by:
                                                          * '<S18>/SELERCION0'
                                                          * '<S21>/SELERCION0'
                                                          */

/* Selector */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TBKITHRCION[72] = { 721, 393, 0, 0, 85,
  131, 98, 26, 26, 721, 393, 0, 0, 85, 131, 98, 26, 26, 721, 393, 0, 0, 85, 131,
  98, 26, 26, 721, 393, 0, 0, 85, 131, 98, 26, 26, 721, 393, 0, 0, 85, 131, 98,
  26, 26, 721, 393, 0, 0, 85, 131, 98, 26, 26, 721, 393, 0, 0, 85, 131, 98, 26,
  26, 721, 393, 0, 0, 85, 131, 98, 26, 26 } ;/* Referenced by: '<S28>/TBKITHRCION' */

/* KI Charge */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TBKPTHRCION[72] = { 459, 393, 0, 0, 0,
  0, 164, 655, 983, 459, 393, 0, 0, 0, 0, 164, 655, 983, 459, 393, 0, 0, 0, 0,
  164, 655, 983, 459, 393, 0, 0, 0, 0, 164, 655, 983, 459, 393, 0, 0, 0, 0, 164,
  655, 983, 459, 393, 0, 0, 0, 0, 164, 655, 983, 459, 393, 0, 0, 0, 0, 164, 655,
  983, 459, 393, 0, 0, 0, 0, 164, 655, 983 } ;/* Referenced by: '<S28>/TBKPTHRCION' */

/* KP Charge */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TBKPTHRDISCION[48] = { 29, 25, 0, 0, 0,
  0, 29, 25, 0, 0, 0, 0, 29, 25, 0, 0, 0, 0, 29, 25, 0, 0, 0, 0, 29, 25, 0, 0, 0,
  0, 29, 25, 0, 0, 0, 0, 29, 25, 0, 0, 0, 0, 29, 25, 0, 0, 0, 0 } ;
                                     /* Referenced by: '<S29>/TBKPTHRDISCION' */

/* KP Discharge */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBTHRCRGCIONOL[63] = { 11141U, 11141U,
  11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U,
  11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U,
  11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U,
  11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U,
  11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U,
  11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U, 11141U,
  11141U } ;                         /* Referenced by: '<S23>/TBTHRCRGCIONOL' */

/* Vcharge openloop setpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBVCRGIONOBJ[90] = { 4640U, 4640U,
  4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U,
  4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U,
  4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U,
  4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U,
  4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U,
  4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U,
  4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U,
  4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U, 4640U } ;/* Referenced by:
                                                                      * '<S1>/TBVCRGIONOBJ'
                                                                      * '<S3>/TBVCRGIONOBJ'
                                                                      */

/*  Table for ION capacitor nominal voltage target */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TBVINFVCHARGE[90] = { -2880, -2880,
  -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880,
  -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880,
  -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880,
  -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880,
  -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880,
  -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880,
  -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880,
  -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880, -2880 } ;/* Referenced by: '<S6>/TBVINFVCHARGE' */

/* Table for Diagnosis low threshold on the VCharge error */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TBVSUPVCHARGE[90] = { 2880, 2880, 2880,
  2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880,
  2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880,
  2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880,
  2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880,
  2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880,
  2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880,
  2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880, 2880 } ;/* Referenced by: '<S6>/TBVSUPVCHARGE' */

/* Table for Diagnosis high threshold on the VCharge error */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRGAINCION = 256U;/* Referenced by:
                                                              * '<S30>/THRGAINCION'
                                                              * '<S31>/THRGAINCION'
                                                              */

/* Gain to calculate threshold to start ION charge capacitor */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THROFFCION = 8356U;/* Referenced by:
                                                              * '<S3>/THROFFCION'
                                                              * '<S20>/THROFFCION'
                                                              * '<S30>/THROFFCION'
                                                              * '<S31>/THROFFCION'
                                                              */

/* Offset to calculate threshold to start ION charge capacitor */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T TIMCIONSTALL = 200U;/* Referenced by: '<S19>/TIMCIONSTALL' */

/* Time to stall VCharge PI control */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VCIONCLAMP = 3440;/* Referenced by: '<S37>/VCIONCLAMP' */

/* CION voltage clamping */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTCIMAXERRINTSAT0[7] = { 30720U,
  30720U, 30720U, 30720U, 30720U, 30720U, 30720U } ;
                                  /* Referenced by: '<S40>/VTCIMAXERRINTSAT0' */

/* Max integral saturation 0 */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTCIMAXERRINTSAT1[7] = { 30720U,
  30720U, 30720U, 30720U, 30720U, 30720U, 30720U } ;
                                  /* Referenced by: '<S40>/VTCIMAXERRINTSAT1' */

/* Max integral saturation 1 */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTFOTHRCRGCION[4] = { -3, -3, -3, -3 } ;
                                     /* Referenced by: '<S21>/VTFOTHRCRGCION' */

/* Force PMOS threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTFOVCHARGEOBJ[4] = { -16, -16, -16,
  -16 } ;                             /* Referenced by: '<S1>/VTFOVCHARGEOBJ' */

/* ION capacitor nominal voltage target forced (negative value means not force) */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T VTSELCYLCION[8] = { 1, 1, 1, 1, 1, 1,
  1, 1 } ;                             /* Referenced by: '<S16>/VTSELCYLCION' */

/* Enable capacitor cylinder charge */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T VTSELSATCYLCION[4] = { 1, 1, 1, 1 } ;
                                    /* Referenced by: '<S40>/VTSELSATCYLCION' */

/* Select saturation capacitor cylinder charge */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTTHRERRCIONDISC[8] = { -800, -800,
  -800, -800, -800, -800, -800, -800 } ;
                                   /* Referenced by: '<S27>/VTTHRERRCIONDISC' */

/* Error threshold below that the closed loop works in discharge mode. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTTIMACQCION[4] = { 500U, 500U, 500U,
  500U } ;                             /* Referenced by:
                                        * '<S3>/VTTIMACQCION'
                                        * '<S20>/VTTIMACQCION'
                                        * '<S31>/VTTIMACQCION'
                                        */

/* Nominal delay time to acquire ION capacitor voltage */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T VChargeObj[4];                /* '<S4>/Merge20' */

/* ION capacitor voltage target */
uint16_T VChargeObjNom;                /* '<S4>/Merge15' */

/* ION capacitor nominal voltage target coming from the table TBVCRGIONOBJ */
int16_T VCrgObjOffMinMax[4];           /* '<S4>/Merge16' */

/* Ion charge capacitor Obj saturation offset */
int16_T VInfVCharge;                   /* '<S4>/Merge13' */

/* Diagnosis high threshold on the VCharge error */
int16_T VSupVCharge;                   /* '<S4>/Merge14' */

/* Diagnosis high threshold on the VCharge error */
int16_T VtErrCION[4];                  /* '<S4>/Merge5' */

/* Error input Acq ION capacitor */
boolean_T VtFlgCIonDisc[4];            /* '<S4>/Merge1' */

/* Enable discharge control */
boolean_T VtFlgVCrgObjSatMax[4];       /* '<S4>/Merge17' */

/* Ion charge capacitor Obj saturation max flag */
boolean_T VtFlgVCrgObjSatMin[4];       /* '<S4>/Merge18' */

/* Ion charge capacitor Obj saturation min flag */
boolean_T VtSelCIONOL[4];              /* '<S4>/Merge23' */

/* Flag to Enable OL */
uint16_T VtThrCrgCION[4];              /* '<S4>/Merge2' */

/* Threshold to start ION charge capacitor */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT int16_T ErrCION0;    /* '<S4>/Merge12' */

/* Error */
STATIC_TEST_POINT uint32_T IdVer_IonChargeCtrl;/* '<S3>/ID_VER_IONCHARGECTRL_DEF' */

/* Id model version */
STATIC_TEST_POINT uint32_T InvPIErrCION;/* '<S30>/Switch1' */

/* Inversion of the spark threshold, as it was the closed loop control output. */
STATIC_TEST_POINT uint32_T MaxInvPIErrCION;/* '<S30>/Divide1' */

/* Inversion of the maximum spark threshold, as it was the closed loop control output. */
STATIC_TEST_POINT boolean_T SelCIONOL_latched;/* '<S19>/Logical Operator' */

/* Open loop enabled signal after latching of LATCHVCAPPICTRL */
STATIC_TEST_POINT int16_T ThrErrCIonDisc;/* '<S4>/Merge11' */

/* Discherge error threshold */
STATIC_TEST_POINT uint32_T TmpCntIGNInOffCIon[4];/* '<S4>/Merge22' */

/* Counter Tdc */
STATIC_TEST_POINT int16_T VCharge0;    /* '<S4>/Merge24' */

/* Breakpoint tag */
STATIC_TEST_POINT uint32_T VtDivErrIntCION[4];/* '<S4>/Merge7' */

/* Error Int Acq ION capacitor */
STATIC_TEST_POINT uint32_T VtErrIntCION[4];/* '<S4>/Merge10' */

/* Error Int Acq ION capacitor */
STATIC_TEST_POINT int32_T VtErrPropCION[4];/* '<S4>/Merge4' */

/* Error Prop Acq ION capacitor */
STATIC_TEST_POINT boolean_T VtMemClamp[4];/* '<S4>/Merge6' */

/* Clamp memory */
STATIC_TEST_POINT uint32_T VtPIErrCION[4];/* '<S4>/Merge9' */

/* Error PI Acq ION capacitor */
STATIC_TEST_POINT int8_T VtPIErrCionAWupDir[4];/* '<S4>/Merge25' */

/* Direction of the PI anti wind-up (0=no saturation, -1= low saturation, 1=high saturation). */
STATIC_TEST_POINT uint32_T VtPIErrDisCION[4];/* '<S4>/Merge8' */

/* Error P Acq ION capacitor discharge */
STATIC_TEST_POINT uint16_T VtTimAcqCION[4];/* '<S4>/Merge3' */

/* Delay time to acquire ION capacitor voltage */
STATIC_TEST_POINT uint32_T VtTimCmdStallCIon[4];/* '<S4>/Merge21' */

/* Time to stall VCharge PI control */
STATIC_TEST_POINT uint16_T VtVChargeObjNom[4];/* '<S4>/Merge19' */

/* ION capacitor nominal voltage target array */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void IonChargeCtrl_Init(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonChargeCtrl_Init' incorporates:
   *  SubSystem: '<Root>/IonChargeCtrl_setup'
   *
   * Block description for '<Root>/IonChargeCtrl_setup':
   *  Variables initialization
   *
   * Block requirements for '<Root>/IonChargeCtrl_setup':
   *  1. EISB_FCA6CYL_SW_REQ_1911: At the PowerOn event the software shall reset the variables as fol... (ECU_SW_Requirements#7785)
   */
  /* SignalConversion generated from: '<S3>/VChargeObjNom' incorporates:
   *  Constant: '<S3>/E1'
   *  Constant: '<S3>/E2'
   *  Constant: '<S3>/TBVCRGIONOBJ'
   *  Selector: '<S3>/Selector2'
   */
  VChargeObjNom = TBVCRGIONOBJ[0];

  /* Constant: '<S3>/ID_VER_IONCHARGECTRL_DEF' */
  IdVer_IonChargeCtrl = ID_VER_IONCHARGECTRL_DEF;

  /* Constant: '<S3>/ZERO1' */
  ErrCION0 = 0;

  /* Constant: '<S3>/ZERO_VOLT' */
  VInfVCharge = 0;

  /* Constant: '<S3>/MAX_VSUPCRG_DEF' */
  VSupVCharge = ((int16_T)MAX_VSUPCRG_DEF);

  /* Constant: '<S3>/ZERO9' */
  ThrErrCIonDisc = 0;

  /* Constant: '<S3>/ZERO16' */
  VCharge0 = 0;
  for (i = 0; i < 4; i++) {
    /* Product: '<S3>/Product2' incorporates:
     *  Constant: '<S3>/E1'
     *  Constant: '<S3>/E2'
     *  Constant: '<S3>/TBVCRGIONOBJ'
     *  Constant: '<S3>/ones2'
     *  Selector: '<S3>/Selector2'
     */
    VChargeObj[(i)] = TBVCRGIONOBJ[0];

    /* Product: '<S3>/Product' incorporates:
     *  Constant: '<S3>/E'
     *  Constant: '<S3>/VTTIMACQCION'
     *  Selector: '<S3>/Selector1'
     */
    VtTimAcqCION[(i)] = VTTIMACQCION[0];

    /* Product: '<S3>/Product1' incorporates:
     *  Constant: '<S3>/THROFFCION'
     */
    VtThrCrgCION[(i)] = THROFFCION;

    /* Constant: '<S3>/ZERO5' */
    VtTimCmdStallCIon[(i)] = 0U;

    /* Constant: '<S3>/ZERO6' */
    TmpCntIGNInOffCIon[(i)] = 0U;

    /* Constant: '<S3>/ZERO7' */
    VtFlgCIonDisc[(i)] = false;

    /* Constant: '<S3>/ZERO8' */
    VtErrIntCION[(i)] = 0U;

    /* Constant: '<S3>/ZERO3' */
    VtPIErrCION[(i)] = 0U;

    /* Constant: '<S3>/ZERO2' */
    VtErrCION[(i)] = 0;

    /* Constant: '<S3>/ZERO4' */
    VtErrPropCION[(i)] = 0;

    /* Constant: '<S3>/ZERO10' */
    VCrgObjOffMinMax[(i)] = 0;

    /* Constant: '<S3>/ZERO12' */
    VtFlgVCrgObjSatMax[(i)] = false;

    /* Constant: '<S3>/ZERO11' */
    VtFlgVCrgObjSatMin[(i)] = false;

    /* Constant: '<S3>/ZERO13' */
    VtVChargeObjNom[(i)] = 0U;

    /* Constant: '<S3>/ZERO14' */
    VtMemClamp[(i)] = false;

    /* Constant: '<S3>/ZERO15' */
    VtSelCIONOL[(i)] = true;

    /* Constant: '<S3>/ZERO18' */
    VtDivErrIntCION[(i)] = 0U;

    /* Constant: '<S3>/ZERO17' */
    VtPIErrDisCION[(i)] = 0U;

    /* Constant: '<S3>/ZERO19' */
    VtPIErrCionAWupDir[(i)] = 0;
  }

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonChargeCtrl_Init' */
}

/* Model step function */
void IonChargeCtrl_T5ms(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2_n;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_g;
  int16_T rtb_Look2D_IR_S16_h;
  int16_T rtb_Look2D_IR_S16_m;
  uint8_T pmos_idx;
  int32_T vcharge_tmp;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonChargeCtrl_T5ms' incorporates:
   *  SubSystem: '<Root>/IonChargeCtrl_T5ms_mgm'
   *
   * Block description for '<Root>/IonChargeCtrl_T5ms_mgm':
   *  Calculation of the VCharge target on temporal task.
   */
  /* DataTypeConversion: '<S9>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  VChargeObjNom_tbl = Rpm;

  /* S-Function (PreLookUpIdSearch_U16): '<S9>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S1>/BKRPMVCRGION'
   *  Constant: '<S1>/BKRPMVCRGION_dim'
   */
  PreLookUpIdSearch_U16( (&(VChargeObjNom_tbl)), &rtb_PreLookUpIdSearch_U16_o2_n,
                        VChargeObjNom_tbl, &BKRPMVCRGION[0], ((uint8_T)
    BKRPMVCRGION_dim));

  /* DataTypeConversion: '<S8>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/Load'
   */
  rtb_PreLookUpIdSearch_U16_o1_i = Load;

  /* S-Function (PreLookUpIdSearch_U16): '<S8>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S1>/BKLOADVCRGION'
   *  Constant: '<S1>/BKLOADVCRGION_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_i,
                        &rtb_PreLookUpIdSearch_U16_o2_g,
                        rtb_PreLookUpIdSearch_U16_o1_i, &BKLOADVCRGION[0],
                        ((uint8_T)BKLOADVCRGION_dim));

  /* S-Function (Look2D_IR_S16): '<S11>/Look2D_IR_S16' incorporates:
   *  Constant: '<S6>/BKLOADVCRGION_dim'
   *  Constant: '<S6>/BKRPMVCRGION_dim'
   *  Constant: '<S6>/TBVSUPVCHARGE'
   *
   * Block requirements for '<S6>/TBVSUPVCHARGE':
   *  1. EISB_FCA6CYL_SW_REQ_1910: The software shall calculate an high threshold (VSupVCharge) on th... (ECU_SW_Requirements#7782)
   */
  Look2D_IR_S16( &rtb_Look2D_IR_S16_h, &TBVSUPVCHARGE[0], VChargeObjNom_tbl,
                rtb_PreLookUpIdSearch_U16_o2_n, ((uint8_T)BKRPMVCRGION_dim),
                rtb_PreLookUpIdSearch_U16_o1_i, rtb_PreLookUpIdSearch_U16_o2_g,
                ((uint8_T)BKLOADVCRGION_dim));

  /* S-Function (Look2D_IR_S16): '<S12>/Look2D_IR_S16' incorporates:
   *  Constant: '<S6>/BKLOADVCRGION_dim'
   *  Constant: '<S6>/BKRPMVCRGION_dim'
   *  Constant: '<S6>/TBVINFVCHARGE'
   *
   * Block requirements for '<S6>/TBVINFVCHARGE':
   *  1. EISB_FCA6CYL_SW_REQ_1909: The software shall calculate a low threshold (VInfVCharge) on the ... (ECU_SW_Requirements#7781)
   */
  Look2D_IR_S16( &rtb_Look2D_IR_S16_m, &TBVINFVCHARGE[0], VChargeObjNom_tbl,
                rtb_PreLookUpIdSearch_U16_o2_n, ((uint8_T)BKRPMVCRGION_dim),
                rtb_PreLookUpIdSearch_U16_o1_i, rtb_PreLookUpIdSearch_U16_o2_g,
                ((uint8_T)BKLOADVCRGION_dim));

  /* S-Function (Look2D_IR_U16): '<S7>/Look2D_IR_U16' incorporates:
   *  Constant: '<S1>/BKLOADVCRGION_dim1'
   *  Constant: '<S1>/BKRPMVCRGION_dim1'
   *  Constant: '<S1>/TBVCRGIONOBJ'
   *
   * Block requirements for '<S1>/TBVCRGIONOBJ':
   *  1. EISB_FCA6CYL_SW_REQ_274: The nominal voltage target for the ION capacitors shall be calcula... (ECU_SW_Requirements#344)
   */
  Look2D_IR_U16( (&(VChargeObjNom_tbl)), &TBVCRGIONOBJ[0], VChargeObjNom_tbl,
                rtb_PreLookUpIdSearch_U16_o2_n, ((uint8_T)BKRPMVCRGION_dim),
                rtb_PreLookUpIdSearch_U16_o1_i, rtb_PreLookUpIdSearch_U16_o2_g,
                ((uint8_T)BKLOADVCRGION_dim));

  /* Chart: '<S1>/VChargeVect_calc' incorporates:
   *  Constant: '<S1>/MAXSATVCRGOBJ'
   *  Constant: '<S1>/MINSATVCRGOBJ'
   *  Constant: '<S1>/PMOS_CONFIG'
   *  Constant: '<S1>/VTFOVCHARGEOBJ'
   *  DataTypeConversion: '<S1>/Data Type Conversion'
   *  DataTypeConversion: '<S1>/Data Type Conversion1'
   *  Inport: '<Root>/VChargeObjOff'
   *  SignalConversion generated from: '<S1>/VChargeObj'
   *  SignalConversion generated from: '<S1>/VCrgObjOffMinMax'
   */
  /* Gateway: IonChargeCtrl_T5ms_mgm/VChargeVect_calc */
  /* During: IonChargeCtrl_T5ms_mgm/VChargeVect_calc */
  /* This stateflow performs:
     1.  calculation of the voltage target;
     2.  storing of nominal voltage target;
     3. increasing of the timer to count the elasped time between two angular events associated at the same pmos element. */
  /* Entry Internal: IonChargeCtrl_T5ms_mgm/VChargeVect_calc */
  /* Transition: '<S10>:10' */
  for (pmos_idx = 0U; pmos_idx < PMOS_CONFIG; pmos_idx += ((uint8_T)ONE)) {
    /* Transition: '<S10>:12' */
    if (((VChargeObjNom_tbl < VtVChargeObjNom[(pmos_idx)]) &&
         (!VtFlgVCrgObjSatMin[(pmos_idx)])) || ((VChargeObjNom_tbl >
          VtVChargeObjNom[(pmos_idx)]) && (!VtFlgVCrgObjSatMax[(pmos_idx)]))) {
      /* Transition: '<S10>:43'
       * Requirements for Transition: '<S10>:43':
       *  1. EISB_FCA6CYL_SW_REQ_1908: The software shall store the current nominal target voltage (VChar... (ECU_SW_Requirements#7783)
       */
      VtVChargeObjNom[(pmos_idx)] = VChargeObjNom_tbl;
    } else {
      /* Transition: '<S10>:44' */
    }

    /* Transition: '<S10>:38' */
    VtFlgVCrgObjSatMin[(pmos_idx)] = false;
    VtFlgVCrgObjSatMax[(pmos_idx)] = false;
    if (VTFOVCHARGEOBJ[(pmos_idx)] >= ((int16_T)ZERO)) {
      /* Transition: '<S10>:31' */
      vcharge_tmp = (int32_T)VTFOVCHARGEOBJ[(pmos_idx)];
    } else {
      /* Transition: '<S10>:32' */
      vcharge_tmp = ((int32_T)VChargeObjNom_tbl) + ((int32_T)VChargeObjOff
        [(pmos_idx)]);
    }

    if (vcharge_tmp <= ((int32_T)MINSATVCRGOBJ)) {
      /* Transition: '<S10>:14'
       * Requirements for Transition: '<S10>:14':
       *  1. EISB_FCA6CYL_SW_REQ_276: The voltage target (VChargeObj array) for each PMOS configured in ... (ECU_SW_Requirements#346)
       */
      VtFlgVCrgObjSatMin[(pmos_idx)] = true;
      vcharge_tmp = (int32_T)MINSATVCRGOBJ;
    } else {
      /* Transition: '<S10>:16' */
      if (vcharge_tmp >= ((int32_T)MAXSATVCRGOBJ)) {
        /* Transition: '<S10>:18'
         * Requirements for Transition: '<S10>:18':
         *  1. EISB_FCA6CYL_SW_REQ_276: The voltage target (VChargeObj array) for each PMOS configured in ... (ECU_SW_Requirements#346)
         */
        VtFlgVCrgObjSatMax[(pmos_idx)] = true;
        vcharge_tmp = (int32_T)MAXSATVCRGOBJ;
      } else {
        /* Transition: '<S10>:20' */
      }

      /* Transition: '<S10>:35' */
    }

    /* Transition: '<S10>:34'
     * Requirements for Transition: '<S10>:34':
     *  1. EISB_FCA6CYL_SW_REQ_276: The voltage target (VChargeObj array) for each PMOS configured in ... (ECU_SW_Requirements#346)
     *  2. EISB_FCA6CYL_SW_REQ_1908: The software shall store the current nominal target voltage (VChar... (ECU_SW_Requirements#7783)
     */
    VChargeObj[(pmos_idx)] = (uint16_T)vcharge_tmp;
    VCrgObjOffMinMax[(pmos_idx)] = (int16_T)(vcharge_tmp - ((int32_T)
      VtVChargeObjNom[(pmos_idx)]));

    /* Transition: '<S10>:47'
     * Requirements for Transition: '<S10>:47':
     *  1. EISB_FCA6CYL_SW_REQ_1912: The software shall increase on the temporal event a counter of the... (ECU_SW_Requirements#7784)
     */
    VtTimCmdStallCIon[(pmos_idx)] = VtTimCmdStallCIon[(pmos_idx)] +
      TIMEDELAYTIMCMDSTALLION;
  }

  /* End of Chart: '<S1>/VChargeVect_calc' */

  /* Gain: '<S1>/Gain2' */
  /* Transition: '<S10>:49' */
  VChargeObjNom = VChargeObjNom_tbl;

  /* SignalConversion generated from: '<S1>/VInfVCharge' */
  VInfVCharge = rtb_Look2D_IR_S16_m;

  /* SignalConversion generated from: '<S1>/VSupVCharge' */
  VSupVCharge = rtb_Look2D_IR_S16_h;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonChargeCtrl_T5ms' */
}

/* Model step function */
void IonChargeCtrl_Tdc(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_S16_o1;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_l;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_f;
  uint16_T rtb_PreLookUpIdSearch_S16_o1_g;
  uint16_T rtb_PreLookUpIdSearch_S16_o2_o;
  uint16_T rtb_PreLookUpIdSearch_S16_o1_b;
  uint16_T rtb_PreLookUpIdSearch_S16_o2_ov;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_LookUp_IR_U16_l;
  uint16_T rtb_Look2D_IR_U16;
  uint16_T rtb_PreLookUpIdSearch_S16_o2_f;
  int16_T rtb_LookUp_IR_S16;
  int16_T rtb_Look2D_IR_S16;
  int16_T rtb_Look2D_IR_S16_b;
  int8_T pierrcion_dirsat;
  boolean_T rtb_LogicalOperator1;
  uint16_T rtb_Merge3;
  int16_T rtb_Selector3_f;
  uint32_T rtb_Switch2;
  boolean_T rtb_FlgCIonDisc;
  boolean_T rtb_RelationalOperator1_p;
  uint32_T rtb_Merge10;
  uint16_T rtb_Switch_p;
  uint32_T rtb_ThrCrgCION_o;
  int16_T rtb_Add1_j;
  uint32_T rtb_Switch2_a;
  int32_T rtb_Divide;
  int16_T rtb_Add2;
  int32_T rtb_Switch;
  int32_T rtb_PIErrCION_nosat;
  int32_T rtb_MinErrIntSat;
  int32_T rtb_ErrIntCION;
  uint32_T rtb_DataTypeConversion1;
  uint32_T rtb_DataTypeConversion3_k;
  int16_T rtb_Selector5;
  int16_T rtb_Add_i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonChargeCtrl_Tdc' incorporates:
   *  SubSystem: '<Root>/IonChargeCtrl_Tdc_mgm'
   *
   * Block description for '<Root>/IonChargeCtrl_Tdc_mgm':
   *  Calculations on the angular event
   */
  /* Selector: '<S18>/Selector5' incorporates:
   *  Inport: '<Root>/CylPlaMOS_idx'
   *  Inport: '<Root>/VCharge'
   */
  rtb_Selector5 = VCharge[(CylPlaMOS_idx)];

  /* Logic: '<S19>/Logical Operator1' incorporates:
   *  Constant: '<S19>/TIMCIONSTALL'
   *  Inport: '<Root>/CylPlaMOS_idx'
   *  Inport: '<Root>/ICCReset'
   *  RelationalOperator: '<S19>/Relational Operator1'
   *  Selector: '<S19>/Selector1'
   *  SignalConversion generated from: '<S2>/VtTimCmdStallCIon_old'
   */
  rtb_LogicalOperator1 = ((VtTimCmdStallCIon[(CylPlaMOS_idx)] > TIMCIONSTALL) ||
    (ICCReset));

  /* Switch: '<S19>/Switch2' incorporates:
   *  Inport: '<Root>/CntVCapInOn'
   *  Inport: '<Root>/CylPlaMOS_idx'
   *  Selector: '<S19>/Selector3'
   *  Selector: '<S19>/Selector4'
   *  SignalConversion generated from: '<S2>/TmpCntIGNInOffCIon_old'
   */
  if (rtb_LogicalOperator1) {
    rtb_Switch2 = CntVCapInOn[(CylPlaMOS_idx)];
  } else {
    rtb_Switch2 = TmpCntIGNInOffCIon[(CylPlaMOS_idx)];
  }

  /* End of Switch: '<S19>/Switch2' */

  /* Switch: '<S19>/Switch' incorporates:
   *  Constant: '<S19>/SELCIONOL'
   *  Inport: '<Root>/CylPlaMOS_idx'
   *  Logic: '<S19>/Logical Operator2'
   *  Selector: '<S19>/Selector2'
   *  SignalConversion generated from: '<S2>/VtSelCIONOL_old'
   *  Switch: '<S19>/Switch3'
   */
  if (SELCIONOL) {
    rtb_LogicalOperator1 = SELCIONOL;
  } else if (rtb_LogicalOperator1 || (VtSelCIONOL[(CylPlaMOS_idx)])) {
    /* Switch: '<S19>/Switch3' incorporates:
     *  Constant: '<S19>/CNTTDCCIONCL'
     *  Inport: '<Root>/CntVCapInOn'
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  RelationalOperator: '<S19>/Relational Operator3'
     *  Selector: '<S19>/Selector3'
     *  Sum: '<S19>/Add'
     */
    rtb_LogicalOperator1 = (CntVCapInOn[(CylPlaMOS_idx)] <= (rtb_Switch2 +
      CNTTDCCIONCL));
  } else {
    /* Switch: '<S19>/Switch3' incorporates:
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  Selector: '<S19>/Selector2'
     *  SignalConversion generated from: '<S2>/VtSelCIONOL_old'
     */
    rtb_LogicalOperator1 = VtSelCIONOL[(CylPlaMOS_idx)];
  }

  /* End of Switch: '<S19>/Switch' */

  /* Logic: '<S19>/Logical Operator' incorporates:
   *  Constant: '<S19>/LATCHVCAPPICTRL'
   *
   * Block requirements for '<S19>/Logical Operator':
   *  1. EISB_FCA6CYL_SW_REQ_441: The spark threshold calculation (i.e. signal VtThrCrgCION), after ... (ECU_SW_Requirements#348)
   */
  SelCIONOL_latched = (rtb_LogicalOperator1 && (LATCHVCAPPICTRL));

  /* DataTypeConversion: '<S56>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_Merge3 = Rpm;

  /* S-Function (PreLookUpIdSearch_U16): '<S56>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S17>/BKRPMSATCION'
   *  Constant: '<S17>/BKRPMSATCION_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, rtb_Merge3,
                        &BKRPMSATCION[0], ((uint8_T)BKRPMSATCION_dim));

  /* S-Function (PreLookUpIdSearch_S16): '<S54>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S17>/BKVCHARGECION'
   *  Constant: '<S17>/BKVCHARGECION_dim'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1,
                        &rtb_PreLookUpIdSearch_S16_o2, rtb_Selector5,
                        &BKVCHARGECION[0], ((uint8_T)BKVCHARGECION_dim));

  /* DataTypeConversion: '<S55>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/Load'
   */
  rtb_Merge3 = Load;

  /* S-Function (PreLookUpIdSearch_U16): '<S55>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S17>/BKLOADVCRGION'
   *  Constant: '<S17>/BKLOADVCRGION_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_l,
                        &rtb_PreLookUpIdSearch_U16_o2_f, rtb_Merge3,
                        &BKLOADVCRGION[0], ((uint8_T)BKLOADVCRGION_dim));

  /* DataTypeConversion: '<S17>/Data Type Conversion' incorporates:
   *  Inport: '<Root>/CylPlaMOS_idx'
   *  Selector: '<S18>/Selector6'
   */
  rtb_Selector3_f = (int16_T)VChargeObj[(CylPlaMOS_idx)];

  /* S-Function (PreLookUpIdSearch_S16): '<S53>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S17>/BKVCHARGECION'
   *  Constant: '<S17>/BKVCHARGECION_dim'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1_g,
                        &rtb_PreLookUpIdSearch_S16_o2_o, rtb_Selector3_f,
                        &BKVCHARGECION[0], ((uint8_T)BKVCHARGECION_dim));

  /* If: '<S16>/If' incorporates:
   *  Constant: '<S16>/VTSELCYLCION'
   *  Constant: '<S20>/THROFFCION'
   *  Constant: '<S20>/ZERO5'
   *  Constant: '<S20>/ZERO6'
   *  Constant: '<S20>/ZERO7'
   *  Constant: '<S20>/ZERO8'
   *  Constant: '<S20>/ZERO9'
   *  Inport: '<Root>/CylPlaMOSRes_idx'
   *  Selector: '<S16>/Selector2'
   *
   * Block requirements for '<S16>/VTSELCYLCION':
   *  1. EISB_FCA6CYL_SW_REQ_1914: The software shall give the possibility to disable the ION Capacit... (ECU_SW_Requirements#7794)
   */
  if (!VTSELCYLCION[(CylPlaMOSRes_idx)]) {
    /* Outputs for IfAction SubSystem: '<S16>/Disabled_Ctrl' incorporates:
     *  ActionPort: '<S20>/Action Port'
     *
     * Block description for '<S16>/Disabled_Ctrl':
     *  Definition of the default values when the Charge capacitor control is
     *  disabled by calibration on a specific PMOS.
     *
     * Block requirements for '<S16>/Disabled_Ctrl':
     *  1. EISB_FCA6CYL_SW_REQ_1914: The software shall give the possibility to disable the ION Capacit... (ECU_SW_Requirements#7794)
     */
    /* Assignment: '<S21>/Assignment1' incorporates:
     *  Constant: '<S20>/VTTIMACQCION'
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  Selector: '<S20>/Selector1'
     */
    VtTimAcqCION[(CylPlaMOS_idx)] = VTTIMACQCION[(CylPlaMOS_idx)];
    rtb_Merge3 = THROFFCION;

    /* Assignment: '<S21>/Assignment4' incorporates:
     *  Constant: '<S20>/THROFFCION'
     *  Constant: '<S20>/ZERO'
     *  Inport: '<Root>/CylPlaMOS_idx'
     */
    VtFlgCIonDisc[(CylPlaMOS_idx)] = false;
    ThrErrCIonDisc = 0;

    /* Assignment: '<S21>/Assignment3' incorporates:
     *  Constant: '<S20>/ZERO1'
     *  Constant: '<S20>/ZERO8'
     *  Inport: '<Root>/CylPlaMOS_idx'
     */
    VtErrPropCION[(CylPlaMOS_idx)] = 0;
    rtb_RelationalOperator1_p = false;
    rtb_Switch2_a = 0U;

    /* Assignment: '<S21>/Assignment8' incorporates:
     *  Constant: '<S20>/ZERO3'
     *  Constant: '<S20>/ZERO6'
     *  Constant: '<S20>/ZERO7'
     *  Inport: '<Root>/CylPlaMOS_idx'
     */
    VtPIErrDisCION[(CylPlaMOS_idx)] = 0U;

    /* Assignment: '<S21>/Assignment9' incorporates:
     *  Constant: '<S20>/ZERO4'
     *  Inport: '<Root>/CylPlaMOS_idx'
     */
    VtPIErrCION[(CylPlaMOS_idx)] = 0U;
    rtb_Merge10 = 0U;

    /* Assignment: '<S21>/Assignment5' incorporates:
     *  Constant: '<S20>/ZERO2'
     *  Constant: '<S20>/ZERO5'
     *  Inport: '<Root>/CylPlaMOS_idx'
     */
    VtErrCION[(CylPlaMOS_idx)] = 0;
    pierrcion_dirsat = 0;

    /* End of Outputs for SubSystem: '<S16>/Disabled_Ctrl' */
  } else {
    /* Outputs for IfAction SubSystem: '<S16>/PI_Assign' incorporates:
     *  ActionPort: '<S22>/Action Port'
     *
     * Block description for '<S16>/PI_Assign':
     *  Calculations when control is enabled
     */
    /* Outputs for Atomic SubSystem: '<S22>/PI_CTRL'
     *
     * Block description for '<S22>/PI_CTRL':
     *  Closed loop control implementation.
     */
    /* Selector: '<S31>/Selector1' incorporates:
     *  Constant: '<S31>/VTTIMACQCION'
     *  Inport: '<Root>/CylPlaMOS_idx'
     */
    rtb_Merge3 = VTTIMACQCION[(CylPlaMOS_idx)];

    /* DataTypeConversion: '<S31>/Data Type Conversion' */
    rtb_Selector3_f = (int16_T)rtb_Merge3;

    /* Sum: '<S27>/Add' incorporates:
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  Selector: '<S18>/Selector6'
     *
     * Block requirements for '<S27>/Add':
     *  1. EISB_FCA6CYL_SW_REQ_290: The software shall evaluate the closed loop error (i.e. VtErrCION)... (ECU_SW_Requirements#354)
     */
    rtb_Add_i = (int16_T)(((int32_T)VChargeObj[(CylPlaMOS_idx)]) - ((int32_T)
      rtb_Selector5));

    /* S-Function (LookUp_IR_S16): '<S32>/LookUp_IR_S16' incorporates:
     *  Constant: '<S27>/BKVCHARGECION_dim'
     *  Constant: '<S27>/VTTHRERRCIONDISC'
     *
     * Block requirements for '<S27>/VTTHRERRCIONDISC':
     *  1. EISB_FCA6CYL_SW_REQ_1915: The software shall define a threshold coming from the table VTTHRE... (ECU_SW_Requirements#7804)
     */
    LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTTHRERRCIONDISC[0],
                  rtb_PreLookUpIdSearch_S16_o1_g, rtb_PreLookUpIdSearch_S16_o2_o,
                  ((uint8_T)BKVCHARGECION_dim));

    /* Switch: '<S27>/Switch' incorporates:
     *  Constant: '<S27>/FOFLGCIONDISC'
     *  Constant: '<S27>/ZERO'
     *  Logic: '<S27>/Logical Operator'
     *  Logic: '<S27>/Logical Operator1'
     *  RelationalOperator: '<S27>/Relational Operator'
     *  RelationalOperator: '<S27>/Relational Operator1'
     *
     * Block requirements for '<S27>/FOFLGCIONDISC':
     *  1. EISB_FCA6CYL_SW_REQ_1915: The software shall define a threshold coming from the table VTTHRE... (ECU_SW_Requirements#7804)
     */
    if (FOFLGCIONDISC >= 0) {
      rtb_FlgCIonDisc = (FOFLGCIONDISC != 0);
    } else {
      rtb_FlgCIonDisc = ((rtb_Add_i < rtb_LookUp_IR_S16) && (!SelCIONOL_latched));
    }

    /* End of Switch: '<S27>/Switch' */

    /* S-Function (PreLookUpIdSearch_S16): '<S33>/PreLookUpIdSearch_S16' incorporates:
     *  Constant: '<S27>/BKKPTHRCION'
     *  Constant: '<S27>/BKKPTHRCION_dim'
     */
    PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1_b,
                          &rtb_PreLookUpIdSearch_S16_o2_ov, rtb_Add_i,
                          &BKKPTHRCION[0], ((uint8_T)BKKPTHRCION_dim));

    /* S-Function (PreLookUpIdSearch_S16): '<S34>/PreLookUpIdSearch_S16' incorporates:
     *  Constant: '<S27>/BKKPTHRDSION'
     *  Constant: '<S27>/BKKPTHRDSION_dim'
     */
    PreLookUpIdSearch_S16( &rtb_Merge3, &rtb_PreLookUpIdSearch_S16_o2_f,
                          rtb_Add_i, &BKKPTHRDSION[0], ((uint8_T)
      BKKPTHRDSION_dim));

    /* S-Function (Look2D_IR_S16): '<S38>/Look2D_IR_S16' incorporates:
     *  Constant: '<S28>/BKKPTHRCION_dim'
     *  Constant: '<S28>/BKVCHARGECION_dim'
     *  Constant: '<S28>/TBKPTHRCION'
     */
    Look2D_IR_S16( &rtb_Look2D_IR_S16, &TBKPTHRCION[0],
                  rtb_PreLookUpIdSearch_S16_o1_b,
                  rtb_PreLookUpIdSearch_S16_o2_ov, ((uint8_T)BKKPTHRCION_dim),
                  rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                  ((uint8_T)BKVCHARGECION_dim));

    /* Switch: '<S40>/Switch2' incorporates:
     *  Constant: '<S40>/VTSELSATCYLCION'
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  Selector: '<S40>/Selector2'
     */
    if (VTSELSATCYLCION[(CylPlaMOS_idx)]) {
      /* S-Function (LookUp_IR_U16): '<S47>/LookUp_IR_U16' incorporates:
       *  Constant: '<S40>/BKRPMSATCION_dim'
       *  Constant: '<S40>/VTCIMAXERRINTSAT1'
       */
      LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTCIMAXERRINTSAT1[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKRPMSATCION_dim));
      rtb_Switch_p = rtb_LookUp_IR_U16;
    } else {
      /* S-Function (LookUp_IR_U16): '<S46>/LookUp_IR_U16' incorporates:
       *  Constant: '<S40>/BKRPMSATCION_dim'
       *  Constant: '<S40>/VTCIMAXERRINTSAT0'
       */
      LookUp_IR_U16( &rtb_LookUp_IR_U16_l, &VTCIMAXERRINTSAT0[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKRPMSATCION_dim));
      rtb_Switch_p = rtb_LookUp_IR_U16_l;
    }

    /* End of Switch: '<S40>/Switch2' */

    /* DataTypeConversion: '<S40>/Data Type Conversion1' */
    rtb_ThrCrgCION_o = (((uint32_T)rtb_Switch_p) << ((uint32_T)6));

    /* Sum: '<S30>/Add1' incorporates:
     *  Constant: '<S30>/MAXTHRIONCRG'
     *  Constant: '<S30>/THROFFCION'
     */
    rtb_Add1_j = (int16_T)((int32_T)(((int32_T)MAXTHRIONCRG) - ((int32_T)
      THROFFCION)));

    /* MinMax: '<S30>/Min of Elements1' incorporates:
     *  Constant: '<S30>/ZERO1'
     */
    if (rtb_Add1_j <= 0) {
      rtb_Add1_j = 0;
    }

    /* End of MinMax: '<S30>/Min of Elements1' */

    /* Product: '<S30>/Divide1' incorporates:
     *  Constant: '<S30>/THRGAINCION'
     *  DataTypeConversion: '<S30>/Data Type Conversion2'
     *
     * Block requirements for '<S30>/Divide1':
     *  1. EISB_FCA6CYL_SW_REQ_1929: The software shall calculate (SelCIONOL_latched at true) the inver... (ECU_SW_Requirements#7796)
     */
    MaxInvPIErrCION = (((uint32_T)rtb_Add1_j) << ((uint32_T)16)) / ((uint32_T)
      THRGAINCION);

    /* MinMax: '<S28>/Min of Elements' */
    if (rtb_ThrCrgCION_o >= MaxInvPIErrCION) {
      rtb_ThrCrgCION_o = MaxInvPIErrCION;
    }

    /* Product: '<S41>/Divide' incorporates:
     *  Product: '<S28>/Product1'
     */
    rtb_Divide = (((int32_T)rtb_Look2D_IR_S16) * ((int32_T)rtb_Add_i)) / 16;

    /* Switch: '<S30>/Switch1' incorporates:
     *  Constant: '<S30>/THRGAINCION'
     *  Constant: '<S30>/ZERO'
     *  Product: '<S30>/Divide'
     *
     * Block requirements for '<S30>/Switch1':
     *  1. EISB_FCA6CYL_SW_REQ_1929: The software shall calculate (SelCIONOL_latched at true) the inver... (ECU_SW_Requirements#7796)
     */
    if (SelCIONOL_latched) {
      /* Sum: '<S30>/Add2' incorporates:
       *  Constant: '<S30>/THROFFCION'
       *  Inport: '<Root>/CylPlaMOS_idx'
       *  Selector: '<S30>/Selector4'
       *  SignalConversion generated from: '<S2>/VtThrCrgCION_old'
       */
      rtb_Add2 = (int16_T)((int32_T)(((int32_T)VtThrCrgCION[(CylPlaMOS_idx)]) -
        ((int32_T)THROFFCION)));

      /* MinMax: '<S30>/MinMax1' incorporates:
       *  Constant: '<S30>/MIN_ERRINT_SAT1'
       */
      if (rtb_Add2 <= 0) {
        rtb_Add2 = 0;
      }

      /* End of MinMax: '<S30>/MinMax1' */
      InvPIErrCION = (((uint32_T)rtb_Add2) << ((uint32_T)16)) / ((uint32_T)
        THRGAINCION);
    } else {
      InvPIErrCION = 0U;
    }

    /* End of Switch: '<S30>/Switch1' */

    /* RelationalOperator: '<S37>/Relational Operator1' incorporates:
     *  Constant: '<S37>/VCIONCLAMP'
     */
    rtb_RelationalOperator1_p = (rtb_Selector5 < VCIONCLAMP);

    /* Switch: '<S37>/Switch2' incorporates:
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  Logic: '<S37>/Logical Operator1'
     *  Selector: '<S22>/Selector1'
     *  Selector: '<S22>/Selector2'
     *  Selector: '<S22>/Selector3'
     *  SignalConversion generated from: '<S2>/VtDivErrIntCION_old'
     *  SignalConversion generated from: '<S2>/VtErrIntCION_old'
     *  SignalConversion generated from: '<S2>/VtMemClamp_old'
     *  Switch: '<S28>/Switch'
     */
    if ((VtMemClamp[(CylPlaMOS_idx)]) || rtb_RelationalOperator1_p) {
      rtb_Switch2_a = VtErrIntCION[(CylPlaMOS_idx)];

      /* Switch: '<S28>/Switch1' incorporates:
       *  DataTypeConversion: '<S28>/Data Type Conversion5'
       *  Product: '<S28>/Product'
       *  Product: '<S42>/Divide'
       *  Selector: '<S22>/Selector3'
       *  SignalConversion generated from: '<S2>/VtErrIntCION_old'
       *  Sum: '<S28>/Add'
       *  Sum: '<S28>/Add1'
       */
      if (SelCIONOL_latched) {
        rtb_Switch = ((int32_T)InvPIErrCION) - rtb_Divide;
      } else {
        /* S-Function (Look2D_IR_S16): '<S39>/Look2D_IR_S16' incorporates:
         *  Constant: '<S28>/BKKPTHRCION_dim'
         *  Constant: '<S28>/BKVCHARGECION_dim'
         *  Constant: '<S28>/TBKITHRCION'
         */
        Look2D_IR_S16( &rtb_Look2D_IR_S16_b, &TBKITHRCION[0],
                      rtb_PreLookUpIdSearch_S16_o1_b,
                      rtb_PreLookUpIdSearch_S16_o2_ov, ((uint8_T)BKKPTHRCION_dim),
                      rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                      ((uint8_T)BKVCHARGECION_dim));
        rtb_Switch = ((((int32_T)rtb_Look2D_IR_S16_b) * ((int32_T)rtb_Add_i)) /
                      16) + ((int32_T)VtErrIntCION[(CylPlaMOS_idx)]);
      }

      /* End of Switch: '<S28>/Switch1' */
    } else {
      rtb_Switch2_a = VtDivErrIntCION[(CylPlaMOS_idx)];
      rtb_Switch = (int32_T)VtDivErrIntCION[(CylPlaMOS_idx)];
    }

    /* End of Switch: '<S37>/Switch2' */

    /* Switch: '<S43>/Switch2' incorporates:
     *  DataTypeConversion: '<S28>/Data Type Conversion4'
     *  MinMax: '<S28>/Min of Elements'
     *  RelationalOperator: '<S43>/LowerRelop1'
     */
    if (rtb_Switch > ((int32_T)rtb_ThrCrgCION_o)) {
      rtb_Switch = (int32_T)rtb_ThrCrgCION_o;
    } else {
      /* RelationalOperator: '<S43>/UpperRelop' incorporates:
       *  Constant: '<S28>/CIMINERRINTSAT'
       *  Switch: '<S43>/Switch'
       */
      rtb_ErrIntCION = (int32_T)((uint32_T)(((uint32_T)CIMINERRINTSAT) <<
        ((uint32_T)6)));

      /* Switch: '<S43>/Switch' incorporates:
       *  RelationalOperator: '<S43>/UpperRelop'
       */
      if (rtb_Switch < rtb_ErrIntCION) {
        rtb_Switch = rtb_ErrIntCION;
      }
    }

    /* End of Switch: '<S43>/Switch2' */

    /* Sum: '<S28>/Add2' */
    rtb_PIErrCION_nosat = rtb_Divide + rtb_Switch;

    /* DataTypeConversion: '<S28>/Data Type Conversion3' incorporates:
     *  Constant: '<S28>/CIMINERRINTSAT'
     */
    rtb_MinErrIntSat = (int32_T)((uint32_T)(((uint32_T)CIMINERRINTSAT) <<
      ((uint32_T)6)));

    /* Chart: '<S28>/Anti_wind_up' incorporates:
     *  DataTypeConversion: '<S28>/Data Type Conversion4'
     *  DataTypeConversion: '<S28>/Data Type Conversion5'
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  MinMax: '<S28>/Min of Elements'
     *  Selector: '<S22>/Selector3'
     *  Selector: '<S22>/Selector4'
     *  SignalConversion generated from: '<S2>/VtErrIntCION_old'
     *  SignalConversion generated from: '<S2>/VtPIErrCionAWupDir_old'
     */
    /* Gateway: IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Anti_wind_up */
    /* During: IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Anti_wind_up */
    /* Anti wind-up strategy.
       In case of the PI output is saturated, the anti wind-up strategy freezes the integrator part and it is enabled only if the integral moves in the same direction of the saturation and however at the steps after the first detection. */
    /* Entry Internal: IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Anti_wind_up */
    /* Transition: '<S36>:2' */
    pierrcion_dirsat = 0;
    rtb_ErrIntCION = (int32_T)VtErrIntCION[(CylPlaMOS_idx)];
    if (rtb_PIErrCION_nosat >= ((int32_T)rtb_ThrCrgCION_o)) {
      /* Transition: '<S36>:4'
       * Requirements for Transition: '<S36>:4':
       *  1. EISB_FCA6CYL_SW_REQ_291: The software shall implement a PI closed loop control using the es... (ECU_SW_Requirements#355)
       */
      rtb_PIErrCION_nosat = (int32_T)rtb_ThrCrgCION_o;
      pierrcion_dirsat = (int8_T)((uint8_T)ONE);
    } else {
      /* Transition: '<S36>:6' */
      if (rtb_PIErrCION_nosat <= rtb_MinErrIntSat) {
        /* Transition: '<S36>:8'
         * Requirements for Transition: '<S36>:8':
         *  1. EISB_FCA6CYL_SW_REQ_291: The software shall implement a PI closed loop control using the es... (ECU_SW_Requirements#355)
         */
        rtb_PIErrCION_nosat = rtb_MinErrIntSat;
        pierrcion_dirsat = (int8_T)(-((int8_T)((uint8_T)ONE)));
      } else {
        /* Transition: '<S36>:9'
         * Requirements for Transition: '<S36>:9':
         *  1. EISB_FCA6CYL_SW_REQ_291: The software shall implement a PI closed loop control using the es... (ECU_SW_Requirements#355)
         */
      }

      /* Transition: '<S36>:10' */
    }

    /* The integrator part is updated if:
       1. there is not saturation active (pierrcion_dirsat=0) OR
       2. there was not any saturation active at the previous step
       (PIErrCionAWupDir!=pierrcion_dirsat) OR
       3. high saturation active (pierrcion_dirsat=1) and the current integrator
       value is lower than the old OR
       4. low saturation active (pierrcion_dirsat=-1) and the current integrator
       value is higher than the old.
       Note: these conditions are expressed below as the negation of the ones
       used to freeze the integrator.
     */
    if ((((pierrcion_dirsat <= 0) || (rtb_Switch <= ((int32_T)VtErrIntCION
            [(CylPlaMOS_idx)]))) && ((pierrcion_dirsat >= 0) || (rtb_Switch >=
           ((int32_T)VtErrIntCION[(CylPlaMOS_idx)])))) || (VtPIErrCionAWupDir
         [(CylPlaMOS_idx)] != pierrcion_dirsat)) {
      /* Transition: '<S36>:24' */
      rtb_ErrIntCION = rtb_Switch;
    } else {
      /* Transition: '<S36>:25' */
    }

    /* End of Chart: '<S28>/Anti_wind_up' */

    /* DataTypeConversion: '<S28>/Data Type Conversion1' */
    /* Transition: '<S36>:27' */
    rtb_DataTypeConversion1 = (uint32_T)rtb_PIErrCION_nosat;

    /* DataTypeConversion: '<S29>/Data Type Conversion1' incorporates:
     *  Constant: '<S29>/CIDISCERRMAXSAT'
     */
    rtb_Switch = (int32_T)((uint32_T)(((uint32_T)CIDISCERRMAXSAT) << ((uint32_T)
      6)));

    /* DataTypeConversion: '<S29>/Data Type Conversion2' incorporates:
     *  Constant: '<S29>/CIDISCERRMINSAT'
     */
    rtb_PIErrCION_nosat = (int32_T)((uint32_T)(((uint32_T)CIDISCERRMINSAT) <<
      ((uint32_T)6)));

    /* S-Function (Look2D_IR_S16): '<S50>/Look2D_IR_S16' incorporates:
     *  Constant: '<S29>/BKKPTHRDSION_dim'
     *  Constant: '<S29>/BKVCHARGECION_dim'
     *  Constant: '<S29>/TBKPTHRDISCION'
     */
    Look2D_IR_S16( &rtb_Add1_j, &TBKPTHRDISCION[0], rtb_Merge3,
                  rtb_PreLookUpIdSearch_S16_o2_f, ((uint8_T)BKKPTHRDSION_dim),
                  rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                  ((uint8_T)BKVCHARGECION_dim));

    /* Product: '<S29>/Product' */
    rtb_MinErrIntSat = ((int32_T)rtb_Add1_j) * ((int32_T)rtb_Add_i);

    /* MinMax: '<S29>/MinMax' */
    if (rtb_MinErrIntSat > rtb_PIErrCION_nosat) {
      rtb_PIErrCION_nosat = rtb_MinErrIntSat;
    }

    /* End of MinMax: '<S29>/MinMax' */

    /* MinMax: '<S29>/MinMax1'
     *
     * Block requirements for '<S29>/MinMax1':
     *  1. EISB_FCA6CYL_SW_REQ_1930: The software shall calculate a proportional closed loop control as... (ECU_SW_Requirements#7806)
     */
    if (rtb_Switch < rtb_PIErrCION_nosat) {
      /* DataTypeConversion: '<S29>/Data Type Conversion3' */
      rtb_DataTypeConversion3_k = (uint32_T)rtb_Switch;
    } else {
      /* DataTypeConversion: '<S29>/Data Type Conversion3' */
      rtb_DataTypeConversion3_k = (uint32_T)rtb_PIErrCION_nosat;
    }

    /* End of MinMax: '<S29>/MinMax1' */

    /* Switch: '<S31>/Switch' */
    if (rtb_FlgCIonDisc) {
      rtb_Merge3 = (uint16_T)(rtb_DataTypeConversion3_k >> ((uint32_T)6));
    } else {
      rtb_Merge3 = (uint16_T)(rtb_DataTypeConversion1 >> ((uint32_T)6));
    }

    /* DataTypeConversion: '<S52>/Conversion2' incorporates:
     *  Constant: '<S31>/GNTIMTHRCION'
     *  Product: '<S31>/Product2'
     *  Product: '<S52>/Divide'
     *  Switch: '<S31>/Switch'
     */
    rtb_Add1_j = (int16_T)((((int32_T)GNTIMTHRCION) * ((int32_T)rtb_Merge3)) /
      131072);

    /* Sum: '<S31>/Add1' */
    rtb_Add1_j += rtb_Selector3_f;

    /* MinMax: '<S31>/Min1'
     *
     * Block requirements for '<S31>/Min1':
     *  1. EISB_FCA6CYL_SW_REQ_1932: The software shall calculate the delay time to acquire ION capacit... (ECU_SW_Requirements#7808)
     */
    if (0 > rtb_Add1_j) {
      rtb_Add1_j = 0;
    }

    /* End of MinMax: '<S31>/Min1' */

    /* Assignment: '<S21>/Assignment1' incorporates:
     *  DataTypeConversion: '<S31>/Data Type Conversion2'
     *  Inport: '<Root>/CylPlaMOS_idx'
     */
    VtTimAcqCION[(CylPlaMOS_idx)] = (uint16_T)rtb_Add1_j;

    /* Sum: '<S31>/Add2' incorporates:
     *  Constant: '<S31>/THRGAINCION'
     *  Constant: '<S31>/THROFFCION'
     *  Product: '<S31>/Product1'
     *  Switch: '<S31>/Switch'
     */
    rtb_ThrCrgCION_o = ((((uint32_T)THRGAINCION) * ((uint32_T)rtb_Merge3)) >>
                        ((uint32_T)10)) + ((uint32_T)THROFFCION);

    /* DataTypeConversion: '<S28>/Data Type Conversion2' */
    rtb_Merge10 = (uint32_T)rtb_ErrIntCION;

    /* Product: '<S37>/Divide' incorporates:
     *  Constant: '<S37>/DIVERRINTCION_FACT_DEF'
     */
    rtb_Switch2_a /= (uint32_T)((uint8_T)DIVERRINTCION_FACT_DEF);

    /* Logic: '<S37>/Logical Operator2' incorporates:
     *  Constant: '<S37>/ENCLPSELOL'
     *  Logic: '<S37>/Logical Operator3'
     */
    rtb_RelationalOperator1_p = (rtb_RelationalOperator1_p ||
      ((SelCIONOL_latched) && (ENCLPSELOL)));

    /* End of Outputs for SubSystem: '<S22>/PI_CTRL' */

    /* Switch: '<S23>/Switch3' incorporates:
     *  Constant: '<S31>/MAXTHRIONCRG'
     *  DataTypeConversion: '<S31>/Data Type Conversion1'
     *  DataTypeConversion: '<S31>/Data Type Conversion3'
     *  MinMax: '<S31>/Min'
     *
     * Block requirements for '<S31>/Min':
     *  1. EISB_FCA6CYL_SW_REQ_1931: The software shall calculate the P-MOS activation voltage ( signal... (ECU_SW_Requirements#7807)
     */
    if (SelCIONOL_latched) {
      /* S-Function (Look2D_IR_U16): '<S25>/Look2D_IR_U16' incorporates:
       *  Constant: '<S23>/BKLOADVCRGION_dim'
       *  Constant: '<S23>/BKRPMSATCION_dim'
       *  Constant: '<S23>/TBTHRCRGCIONOL'
       *
       * Block requirements for '<S23>/TBTHRCRGCIONOL':
       *  1. EISB_FCA6CYL_SW_REQ_284: The software shall estimate the P-MOS activation voltage ( signal ... (ECU_SW_Requirements#351)
       */
      Look2D_IR_U16( &rtb_Look2D_IR_U16, &TBTHRCRGCIONOL[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKRPMSATCION_dim), rtb_PreLookUpIdSearch_U16_o1_l,
                    rtb_PreLookUpIdSearch_U16_o2_f, ((uint8_T)BKLOADVCRGION_dim));
      rtb_Merge3 = rtb_Look2D_IR_U16;
    } else {
      /* Outputs for Atomic SubSystem: '<S22>/PI_CTRL'
       *
       * Block description for '<S22>/PI_CTRL':
       *  Closed loop control implementation.
       */
      if (((uint32_T)MAXTHRIONCRG) < rtb_ThrCrgCION_o) {
        /* MinMax: '<S31>/Min' incorporates:
         *  Constant: '<S31>/MAXTHRIONCRG'
         *  DataTypeConversion: '<S31>/Data Type Conversion3'
         *
         * Block requirements for '<S31>/Min':
         *  1. EISB_FCA6CYL_SW_REQ_1931: The software shall calculate the P-MOS activation voltage ( signal... (ECU_SW_Requirements#7807)
         */
        rtb_Merge3 = MAXTHRIONCRG;
      } else {
        rtb_Merge3 = (uint16_T)rtb_ThrCrgCION_o;
      }

      /* End of Outputs for SubSystem: '<S22>/PI_CTRL' */
    }

    /* End of Switch: '<S23>/Switch3' */

    /* Assignment: '<S21>/Assignment5' incorporates:
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  SignalConversion generated from: '<S22>/ErrCION'
     */
    VtErrCION[(CylPlaMOS_idx)] = rtb_Add_i;

    /* Assignment: '<S21>/Assignment3' incorporates:
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  SignalConversion generated from: '<S22>/ErrPropCION'
     */
    VtErrPropCION[(CylPlaMOS_idx)] = rtb_Divide;

    /* Assignment: '<S21>/Assignment4' incorporates:
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  SignalConversion generated from: '<S22>/FlgCIonDisc'
     */
    VtFlgCIonDisc[(CylPlaMOS_idx)] = rtb_FlgCIonDisc;

    /* Assignment: '<S21>/Assignment9' incorporates:
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  SignalConversion generated from: '<S22>/PIErrCION'
     */
    VtPIErrCION[(CylPlaMOS_idx)] = rtb_DataTypeConversion1;

    /* Assignment: '<S21>/Assignment8' incorporates:
     *  Inport: '<Root>/CylPlaMOS_idx'
     *  SignalConversion generated from: '<S22>/PIErrDisCION'
     */
    VtPIErrDisCION[(CylPlaMOS_idx)] = rtb_DataTypeConversion3_k;

    /* SignalConversion generated from: '<S22>/ThrErrCIonDisc' */
    ThrErrCIonDisc = rtb_LookUp_IR_S16;

    /* End of Outputs for SubSystem: '<S16>/PI_Assign' */
  }

  /* End of If: '<S16>/If' */

  /* Assignment: '<S21>/Assignment10' incorporates:
   *  Inport: '<Root>/CylPlaMOS_idx'
   */
  VtErrIntCION[(CylPlaMOS_idx)] = rtb_Merge10;

  /* Assignment: '<S21>/Assignment11' incorporates:
   *  Inport: '<Root>/CylPlaMOS_idx'
   */
  VtPIErrCionAWupDir[(CylPlaMOS_idx)] = pierrcion_dirsat;

  /* Selector: '<S21>/Selector3' incorporates:
   *  Constant: '<S21>/VTFOTHRCRGCION'
   *  Inport: '<Root>/CylPlaMOS_idx'
   *
   * Block requirements for '<S21>/VTFOTHRCRGCION':
   *  1. EISB_FCA6CYL_SW_REQ_284: The software shall estimate the P-MOS activation voltage ( signal ... (ECU_SW_Requirements#351)
   */
  rtb_Selector3_f = VTFOTHRCRGCION[(CylPlaMOS_idx)];

  /* Switch: '<S21>/Switch4' incorporates:
   *  RelationalOperator: '<S21>/Relational Operator'
   */
  if (rtb_Selector3_f >= 0) {
    rtb_Merge3 = (uint16_T)rtb_Selector3_f;
  }

  /* End of Switch: '<S21>/Switch4' */

  /* Assignment: '<S21>/Assignment2' incorporates:
   *  Inport: '<Root>/CylPlaMOS_idx'
   */
  VtThrCrgCION[(CylPlaMOS_idx)] = rtb_Merge3;

  /* Assignment: '<S21>/Assignment6' incorporates:
   *  Inport: '<Root>/CylPlaMOS_idx'
   */
  VtMemClamp[(CylPlaMOS_idx)] = rtb_RelationalOperator1_p;

  /* Assignment: '<S21>/Assignment7' incorporates:
   *  Inport: '<Root>/CylPlaMOS_idx'
   */
  VtDivErrIntCION[(CylPlaMOS_idx)] = rtb_Switch2_a;

  /* Selector: '<S21>/Selector' incorporates:
   *  Constant: '<S21>/SELERCION0'
   *
   * Block requirements for '<S21>/SELERCION0':
   *  1. EISB_FCA6CYL_SW_REQ_1913: The software shall publish two test points:
     1. VCharge0, which is ... (ECU_SW_Requirements#7798)
   */
  ErrCION0 = VtErrCION[(SELERCION0)];

  /* Selector: '<S18>/Selector1' incorporates:
   *  Constant: '<S18>/SELERCION0'
   *  Inport: '<Root>/VCharge'
   *
   * Block requirements for '<S18>/SELERCION0':
   *  1. EISB_FCA6CYL_SW_REQ_1913: The software shall publish two test points:
     1. VCharge0, which is ... (ECU_SW_Requirements#7798)
   */
  VCharge0 = VCharge[(SELERCION0)];

  /* Assignment: '<S19>/Assignment1' incorporates:
   *  Inport: '<Root>/CylPlaMOS_idx'
   */
  TmpCntIGNInOffCIon[(CylPlaMOS_idx)] = rtb_Switch2;

  /* Assignment: '<S19>/Assignment2' incorporates:
   *  Constant: '<S19>/ZERO'
   *  Inport: '<Root>/CylPlaMOS_idx'
   */
  VtTimCmdStallCIon[(CylPlaMOS_idx)] = 0U;

  /* Assignment: '<S19>/Assignment4' incorporates:
   *  Inport: '<Root>/CylPlaMOS_idx'
   *
   * Block requirements for '<S19>/Assignment4':
   *  1. EISB_FCA6CYL_SW_REQ_441: The spark threshold calculation (i.e. signal VtThrCrgCION), after ... (ECU_SW_Requirements#348)
   */
  VtSelCIONOL[(CylPlaMOS_idx)] = rtb_LogicalOperator1;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonChargeCtrl_Tdc' */
}

/* Model initialize function */
void IonChargeCtrl_initialize(void)
{
  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/IonChargeCtrl_Init' incorporates:
   *  SubSystem: '<Root>/IonChargeCtrl_setup'
   *
   * Block description for '<Root>/IonChargeCtrl_setup':
   *  Variables initialization
   *
   * Block requirements for '<Root>/IonChargeCtrl_setup':
   *  1. EISB_FCA6CYL_SW_REQ_1911: At the PowerOn event the software shall reset the variables as fol... (ECU_SW_Requirements#7785)
   */
  /* Start for Constant: '<S3>/ID_VER_IONCHARGECTRL_DEF' */
  IdVer_IonChargeCtrl = ID_VER_IONCHARGECTRL_DEF;

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/IonChargeCtrl_Init' */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

void IonChargeCtrl_Init(void);
void IonChargeCtrl_T5ms(void);
void IonChargeCtrl_Tdc(void);
void IonChargeCtrl_initialize(void);
uint16_T VChargeObjNom;
int16_T VInfVCharge;
int16_T VSupVCharge;
uint16_T VChargeObj[4];
int16_T VCrgObjOffMinMax[4];
int16_T VtErrCION[4];
uint8_T VtFlgCIonDisc[4];
boolean_T VtFlgVCrgObjSatMax[4];
boolean_T VtFlgVCrgObjSatMin[4];
boolean_T VtSelCIONOL[4];
uint16_T VtThrCrgCION[4];
void IonChargeCtrl_Init(void)
{
  uint8_T i;
  VInfVCharge = 0;
  VSupVCharge = 0;
  VChargeObjNom = 0;
  for (i = 0; i < 4; i++) {
    VChargeObj[(i)] = 0;
    VtThrCrgCION[(i)] = 0;
    VCrgObjOffMinMax[(i)] = 0;
    VtErrCION[(i)] = 0;
    VtFlgCIonDisc[(i)] = 0U;
    VtFlgVCrgObjSatMax[(i)] = false;
    VtFlgVCrgObjSatMin[(i)] = false;
    VtSelCIONOL[(i)] = false;
  }
}

void IonChargeCtrl_initialize(void)
{
  IonChargeCtrl_Init();
}

void IonChargeCtrl_T5ms(void)
{
}

void IonChargeCtrl_Tdc(void)
{
}

#endif                                 /* _BUILD_IONCHARGECTRL_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/