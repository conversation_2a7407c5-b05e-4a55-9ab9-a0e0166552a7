/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonAcqBufRec.c
 **  File Creation Date: 03-Dec-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonAcqBufRec
 **  Model Description:  This model records vectorial signals needed for post-processing, like ion signal, fft output, an so on, according to several strategies, selectable through calibration.
   These signals will be ciclycally rendered to acquisition system though a periodic task (5ms).


 **  Model Version:      1.1522
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Fri Dec 03 10:52:07 2021
 **
 **  Last Saved Modification:  RoccaG - Fri Dec 03 10:49:25 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonAcqBufRec_out.h"
#include "IonAcqBufRec_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/
#define IonAcqBufRec_MAX_SIGN_KNOCK_INT (1073737728)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_IONACQBUFREC_DEF        11516U                    /* Referenced by: '<S2>/Constant5' */

/* Model Version. */
#define IONABSTDC_DT_DIS               -128                      /* Referenced by: '<S4>/Buffer_MarkOut' */

/* Enabling value for the use of VTIONABSTDC selection for acquisition channel 0. */
#define LAST_SAMPLE_ID                 799U                      /* Referenced by:
                                                                  * '<S2>/Constant26'
                                                                  * '<S2>/Constant34'
                                                                  */

/* Maximum sample number -1 (Last id in sample buffer according to zero based notation). */
#define MAX_INT16                      32767U                    /* Referenced by:
                                                                  * '<S1>/Buffer_Record_Mgm'
                                                                  * '<S2>/Constant33'
                                                                  */

/* Max value for int16 type. */
#define MAX_ION_BUFFER                 5000U                     /* Referenced by: '<S6>/Constant' */

/* Max value for IonBufferV */
#define MAX_UINT16                     65535U                    /* Referenced by:
                                                                  * '<S1>/Buffer_Record_Mgm'
                                                                  * '<S2>/Constant20'
                                                                  * '<S2>/Constant22'
                                                                  * '<S2>/Constant32'
                                                                  */

/* Max value for uint16 type. */
#define MAX_UINT8                      255U                      /* Referenced by:
                                                                  * '<S1>/Buffer_Record_Mgm'
                                                                  * '<S2>/Constant29'
                                                                  * '<S2>/Constant4'
                                                                  */

/* Max value for uint8 type. */
#define SHOW_ALL_CYL                   -1                        /* Referenced by:
                                                                  * '<S1>/Buffer_Record_Mgm'
                                                                  * '<S4>/Buffer_MarkOut'
                                                                  */

/* Record each cylinder alternatively */
#define SHOW_ALL_DWELLINT_MORE_KNOCK   -14                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of knock or high dwell integral allowing overwrite. */
#define SHOW_DIAG_KNOCK_COH            -6                        /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cyinder in case of knock coherence diagnosis. */
#define SHOW_DWELLINT                  -11                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of high dwell integral. */
#define SHOW_DWELLINT_MORE_KNOCK       -13                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of high dwell integral and stable spark advance correction or knock detection allowing overwrite. */
#define SHOW_FLGOLSPARK_EN             -20                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case that integral on first samples of IonBuffer is lower than OLSECINTTHR */
#define SHOW_INTION_NOCOMB             -16                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of no combustion detected. */
#define SHOW_ION_KNOCK                 -8                        /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of knock detection. */
#define SHOW_ION_MISF                  -5                        /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of misfire. */
#define SHOW_ION_SPIKE                 -4                        /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of spike detection for F3 algorithm. */
#define SHOW_ION_SPIKE_KNOCK           -7                        /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of spike detected from F3 algorithm and knock detection. */
#define SHOW_KNOCK_DIST                -12                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of spike detection from F7 algorithm. */
#define SHOW_MEGA_KNOCK                -10                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of megaknock detection. */
#define SHOW_MEGA_KNOCK_MORE_KNOCK     -19                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of megaknock detection allowing overwrite. */
#define SHOW_MORE_KNOCK                -9                        /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of knock detection allowing overwrite. */
#define SHOW_MORE_KNOCK_RPM            -15                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case of knocking detection and high engine speed, allowing overwrite.  */
#define SHOW_NONE                      -3                        /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Don't record any cylinder. */
#define SHOW_STPHASE_NOK               -2                        /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case that phase recognition algorithm fails. */
#define SHOW_THPEAK_GREATER_THR        -17                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case that the value of thermal peak is greater than a threshold. */
#define SHOW_TSPARK_UNDER_THR          -18                       /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Record cylinder in case that spark time is smaller than a threshold. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONACQBUFREC_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinders!
#endif

#if (FFT_SAMPLE != 128)
#error This code was generated with a different number of samples for FFT!
#endif

#if (DIAG_NUMBER != 96)
#error This code was generated with a different number of diagnosis!
#endif

#if (MAX_ION_SIGNALS != 2)
#error This code was generated with a different number of acquisition channels!
#endif

#if (MAX_SAMPLE != 800)
#error This code was generated with a different number of ion sample!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T IONSIGNALTSPARKTHR = 200U;
                                   /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Threshold used for IONABSTDC == 18 --> SHOW_TSPARK_UNDER_THR */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T RPMENIONPLOT = 6000U;
                                   /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Rpm threshold to plot ion when IONABSTDC=-15 */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T SHOWDWELLINTTHR = 100;
                                   /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* DwellInt threshold for IonSignal plot */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SHOWTHPEAKTHR = 32000U;
                                   /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* ThPeak threshold for Ionsignal Ploting */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THPEAKPERC = 512U;/* Referenced by:
                                                             * '<S1>/Buffer_Record_Mgm'
                                                             * '<S6>/Constant1'
                                                             */

/* Threshold in Percentual of ThPeak Max for Ionsignal Ploting */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T THRDIFFKNOCKINT = 4096;
                                   /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Min difference between KnockInt and ShownKnockInt to force IonSignal overwrite */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTENCOPYIONBUFFV[2] = { 0U, 0U } ;
                                   /* Referenced by: '<S1>/Buffer_Record_Mgm' */

/* Plot IonSignal in mV */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T VTIONABSTDC[2] = { -13, -2 } ;/* Referenced by: '<S4>/Constant1' */

/* Cylinder for ion acquisition selection: * 0..(N_CYL-1) cylinder; * N_CYL..(2*N_CYL-1) cyl=(IONABSTDC-8) with knock; * -1 for cyl rotation; * -2 for cyl with StPhase <> 4; * -3 OFF; * -4 for cyl with spike detector; * -5 for cyl with misfire; * -6 for cyl with knock coherence diag; * -7 for cyl with spike detector and knock; *  -8 for cyl with knock; * -9 for cyl with knock and overwrite if greater knock; *-10 cyl with mega-knock; *-11 cyl with DwellInt > thr; *-12 cyl with knockdist; *-13 as -9 (over if DwellInt > thr); *-14 as -13 (also if FlgDSAoutDis != 0) ; *-15 as -9 with Rpm>thr; *-16 IntIon<NocombThr;*-17 if ThPeak>=SHOWTHPEAKTHR;*-18 if TSpark<=IONSIGNALTSPARKTHR;*-19 as (-9 OR -10)  */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
int8_T IonAbsTdcInUse[2];              /* '<S3>/Merge14' */

/* IonSignal sel in use:*0..(N_CYL-1);*N_CYL..(2*N_CYL-1) with knock;*-1 cyl rotation;*-2 StPhase !=4;*-3 OFF;*-4 spike detected;*-5 misfire;*-6 knock coherence diag;*-7 spike detected and knock;*-8 knock;*-9 knock and overwrite if greater knock;*-10 megaknock;*-11 DwellInt>thr;*-12 knockdist;*-13 as -9 (over if DwellInt>thr);*-14 as -13 (also if FlgDSAoutDis!=0);*-15 as -9 with Rpm>thr;*-16 IntIon<NocombThr;*-17 if ThPeak>=SHOWTHPEAKTHR;*-18 if TSpark<=IONSIGNALTSPARKTHR */
uint32_T VtIonSignal[2];               /* '<S3>/Merge8' */

/* Last Ion sample (development tool) */
uint16_T VtNSample[2];                 /* '<S3>/Merge12' */

/* Sample counter (development tool) */
uint8_T VtShownAbsTdc[2];              /* '<S3>/Merge13' */

/* TDC index of IonSample */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_IonAcqBufRec;/* '<S2>/Constant5' */
#include "out_map_sec_init.h"
/* Model Version */
STATIC_TEST_POINT uint16_T IonBuff[800];/* '<S3>/Merge17' */

/* Ion current Buffer (development tool) */
STATIC_TEST_POINT uint16_T IonBuff2[800];/* '<S3>/Merge18' */

/* Ion current Buffer for IonSignal2 (development tool) */
STATIC_TEST_POINT uint16_T IonBuff2FFT[128];/* '<S3>/Merge16' */

/* Ion current FFT buffer for IonSignal2FFT(development tool) */
STATIC_TEST_POINT uint32_T IonBuff2FftPeak[64];/* '<S3>/Merge28' */

/* Ion FFT buffer2 (development tool) */
STATIC_TEST_POINT uint16_T IonBuffFFT[128];/* '<S3>/Merge15' */

/* Ion current FFT buffer (development tool) */
STATIC_TEST_POINT uint32_T IonBuffFftPeak[64];/* '<S3>/Merge27' */
#include "out_map_sec_end.h"

/* Ion FFT buffer (development tool) */
STATIC_TEST_POINT uint32_T IonSignal;  /* '<S3>/Merge2' */

/* Last Ion sample (development tool) */
STATIC_TEST_POINT uint16_T IonSignalFFT;/* '<S3>/Merge1' */

/* Last Ion sample - FFT buffer (development tool) */
STATIC_TEST_POINT uint32_T MaxPossibleThPeak[2];/* '<S3>/Merge24' */

/* ThPeak threshold used to test the signal (IONABSTDC=-17) */
STATIC_TEST_POINT uint16_T NSample;    /* '<S3>/Merge3' */

/* Sample counter (development tool) */
STATIC_TEST_POINT uint16_T NSampleM[2];/* '<S3>/Merge11' */

/* NSampleM */
STATIC_TEST_POINT uint8_T ShownAbsTdc; /* '<S3>/Merge5' */

/* TDC index of IonSample */
STATIC_TEST_POINT uint8_T ShownCylUpdatedFlag[2];/* '<S3>/Merge10' */

/* ShownCylUpdatedFlag */
STATIC_TEST_POINT uint16_T ShownIdStartDist[2];/* '<S3>/Merge20' */

/* Shown ID IdStartDist */
STATIC_TEST_POINT uint16_T ShownIdStopDist[2];/* '<S3>/Merge21' */

/* Shown ID IdStopDist */
STATIC_TEST_POINT uint32_T ShownKnockInt[2];/* '<S3>/Merge19' */

/* KnockInt for IonSignal (IONABSTDC = -9) */
STATIC_TEST_POINT uint16_T ShownStartFftKnockId[2];/* '<S3>/Merge22' */

/* Shown ID StartFftKnockId */
STATIC_TEST_POINT uint16_T ShownThPeakId[2];/* '<S3>/Merge23' */

/* Shown ID ThPeak */
STATIC_TEST_POINT uint32_T ShownThPeakRelThr[2];/* '<S3>/Merge25' */

/* ThPeak threshold used to plot signal (IONABSTDC=-17) */
STATIC_TEST_POINT uint32_T VtFFTSignal[2];/* '<S3>/Merge7' */

/* FFT test signal */
STATIC_TEST_POINT uint16_T VtIonSignalFFT[2];/* '<S3>/Merge6' */

/* Last Ion sample - FFT buffer (development tool) */
STATIC_TEST_POINT uint16_T VtShownBaseFreq[2];/* '<S3>/Merge26' */

/* Shown FFT Base frequency */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void IonAcqBufRec_5ms(void)
{
  uint16_T rtb_VtNSample_p[2];
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufRec_5ms' incorporates:
   *  SubSystem: '<Root>/T5ms'
   *
   * Block description for '<Root>/T5ms':
   *  This block, that is triggered at 5 ms task, redirects recorded ion buffers towards acquisition system.
   *  At each step, one sample for each vectorial buffer is cyclically redirected on output signals.
   *  After N tasks the full content of the buffers will have been supplied to acquisition system.
   */
  /* Chart: '<S4>/Buffer_MarkOut' incorporates:
   *  Constant: '<S4>/Constant1'
   *  Inport: '<Root>/IonAbsTdcDT'
   *  SignalConversion generated from: '<S4>/IonAbsTdcInUse'
   *  SignalConversion generated from: '<S4>/ShownCylUpdatedFlag'
   *  SignalConversion generated from: '<S4>/VtFFTSignal'
   *  SignalConversion generated from: '<S4>/Old_value'
   */
  /* Gateway: T5ms/Buffer_MarkOut */
  /* During: T5ms/Buffer_MarkOut */
  /* This stateflow produces output towards acquisition system,redirecting one sample at time for each buffered array. */
  /* Entry Internal: T5ms/Buffer_MarkOut */
  /* Transition: '<S8>:37' */
  /*  Assign a default value to each output to improve code generation  */
  for (i = 0; i < 2; i++) {
    rtb_VtNSample_p[i] = VtNSample[(i)];
  }

  /*  initialize channel counter  */
  /*  Mark out samples collected on first channel  */
  if (VtNSample[0] < NSampleM[0]) {
    /* Transition: '<S8>:43' */
    /* Transition: '<S8>:48' */
    if (VtNSample[0] < FFT_SAMPLE) {
      /* Transition: '<S8>:50' */
      /* Transition: '<S8>:53':
       *  1. EISB_FCA6CYL_SW_REQ_1701: At each 5ms task, if recorded buffers are ready (i.e. VtShownAbsTd... (ECU_SW_Requirements#3971)
       */
      VtIonSignalFFT[0] = IonBuffFFT[(VtNSample[0])];

      /* Transition: '<S8>:56' */
    } else {
      /* Transition: '<S8>:55' */
    }

    /* Transition: '<S8>:58' */
    if (VtNSample[0] < FFT_SAMPLE_DIV2) {
      /* Transition: '<S8>:60' */
      /* Transition: '<S8>:63':
       *  1. EISB_FCA6CYL_SW_REQ_1700: At each 5ms task, if recorded buffers are ready (i.e. VtShownAbsTd... (ECU_SW_Requirements#3972)
       */
      VtFFTSignal[0] = IonBuffFftPeak[(VtNSample[0])];

      /* Transition: '<S8>:66' */
    } else {
      /* Transition: '<S8>:65' */
    }

    /* Transition: '<S8>:68':
     *  1. EISB_FCA6CYL_SW_REQ_1703: At each 5ms task, if recorded buffers are ready (i.e. VtShownAbsTd... (ECU_SW_Requirements#3973)
     */
    VtIonSignal[0] = IonBuff[(VtNSample[0])];

    /* SignalConversion generated from: '<S4>/NSample' incorporates:
     *  SignalConversion generated from: '<S4>/VtFFTSignal'
     */
    NSample = (uint16_T)((int32_T)(((int32_T)VtNSample[0]) + 1));
    rtb_VtNSample_p[0] = NSample;

    /* SignalConversion generated from: '<S4>/IonSignalFFT' */
    /*  Introduced copy of signals on CCP specific signals for retro compatibility of INCA experiments  */
    IonSignalFFT = VtIonSignalFFT[0];

    /* SignalConversion generated from: '<S4>/IonSignal' */
    IonSignal = VtIonSignal[0];

    /* SignalConversion generated from: '<S4>/ShownAbsTdc' */
    ShownAbsTdc = VtShownAbsTdc[0];

    /* Transition: '<S8>:74' */
    /* Transition: '<S8>:76' */
  } else {
    /* Transition: '<S8>:46' */
    if (IonAbsTdcInUse[0] != ((int8_T)SHOW_ALL_CYL)) {
      /* Transition: '<S8>:70' */
      /* Transition: '<S8>:73' */
      ShownCylUpdatedFlag[0] = 0U;

      /* Transition: '<S8>:76' */
    } else {
      /* Transition: '<S8>:77' */
    }
  }

  /* Transition: '<S8>:79' */
  /*  Mark out samples collected on second channel  */
  if (VtNSample[1] < NSampleM[1]) {
    /* Transition: '<S8>:94' */
    /* Transition: '<S8>:88' */
    if (VtNSample[1] < FFT_SAMPLE) {
      /* Transition: '<S8>:91' */
      /* Transition: '<S8>:81':
       *  1. EISB_FCA6CYL_SW_REQ_1701: At each 5ms task, if recorded buffers are ready (i.e. VtShownAbsTd... (ECU_SW_Requirements#3971)
       */
      VtIonSignalFFT[1] = IonBuff2FFT[(VtNSample[1])];

      /* Transition: '<S8>:101' */
    } else {
      /* Transition: '<S8>:96' */
    }

    /* Transition: '<S8>:106' */
    if (VtNSample[1] < FFT_SAMPLE_DIV2) {
      /* Transition: '<S8>:85' */
      /* Transition: '<S8>:83':
       *  1. EISB_FCA6CYL_SW_REQ_1700: At each 5ms task, if recorded buffers are ready (i.e. VtShownAbsTd... (ECU_SW_Requirements#3972)
       */
      VtFFTSignal[1] = IonBuff2FftPeak[(VtNSample[1])];

      /* Transition: '<S8>:98' */
    } else {
      /* Transition: '<S8>:107' */
    }

    /* Transition: '<S8>:99':
     *  1. EISB_FCA6CYL_SW_REQ_1703: At each 5ms task, if recorded buffers are ready (i.e. VtShownAbsTd... (ECU_SW_Requirements#3973)
     */
    VtIonSignal[1] = IonBuff2[(VtNSample[1])];
    rtb_VtNSample_p[1] = (uint16_T)((int32_T)(((int32_T)VtNSample[1]) + 1));

    /* Transition: '<S8>:102' */
    /* Transition: '<S8>:86' */
  } else {
    /* Transition: '<S8>:109' */
    if (IonAbsTdcInUse[1] != ((int8_T)SHOW_ALL_CYL)) {
      /* Transition: '<S8>:95' */
      /* Transition: '<S8>:82' */
      ShownCylUpdatedFlag[1] = 0U;

      /* Transition: '<S8>:86' */
    } else {
      /* Transition: '<S8>:111' */
    }
  }

  /* Transition: '<S8>:113' */
  /*  Update IonAbsTdcInUse  */
  if (IonAbsTdcDT == ((int8_T)IONABSTDC_DT_DIS)) {
    /* Transition: '<S8>:115' */
    /* Transition: '<S8>:118' */
    IonAbsTdcInUse[0] = VTIONABSTDC[0];

    /* Transition: '<S8>:121' */
  } else {
    /* Transition: '<S8>:120' */
    IonAbsTdcInUse[0] = IonAbsTdcDT;
  }

  /* Transition: '<S8>:123' */
  IonAbsTdcInUse[1] = VTIONABSTDC[1];

  /* End of Chart: '<S4>/Buffer_MarkOut' */

  /* SignalConversion generated from: '<S4>/VtNSample' */
  for (i = 0; i < 2; i++) {
    VtNSample[(i)] = rtb_VtNSample_p[i];
  }

  /* End of SignalConversion generated from: '<S4>/VtNSample' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufRec_5ms' */
}

/* Model step function */
void IonAcqBufRec_EOA(void)
{
  int32_T d_diff_delta_knock;
  uint16_T i;
  uint8_T ionIdx;
  boolean_T flgoverwriteion;
  int8_T tmpIonAbsTdcInUse;
  uint32_T maxThPeak;
  uint32_T scalingfactor;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufRec_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   *
   * Block description for '<Root>/EOA':
   *  The aim of this subsystem is to record vectorial signals needed for post-processing like ion signal, fft output, an so on.
   *  These signals will be ciclycally rendered to acquisition system though a periodic task (5ms).
   *

   */
  /* Chart: '<S1>/Buffer_Record_Mgm' incorporates:
   *  Constant: '<S6>/Constant1'
   *  DataTypeConversion: '<S6>/scaling_factor'
   *  Inport: '<Root>/BaseFreq'
   *  Inport: '<Root>/DeltaKnockNPow'
   *  Inport: '<Root>/DwellInt'
   *  Inport: '<Root>/FftPeak'
   *  Inport: '<Root>/FlgDSAoutDis'
   *  Inport: '<Root>/FlgOLSpark'
   *  Inport: '<Root>/IdStartDist'
   *  Inport: '<Root>/IdStopDist'
   *  Inport: '<Root>/IntIon'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/IonBuffer'
   *  Inport: '<Root>/IonBufferMod'
   *  Inport: '<Root>/IonBufferV'
   *  Inport: '<Root>/KnockInt'
   *  Inport: '<Root>/MegaKnock'
   *  Inport: '<Root>/NSampIonSpike'
   *  Inport: '<Root>/NSampleMaxEOA'
   *  Inport: '<Root>/NoCombThr'
   *  Inport: '<Root>/PtFault'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/StMisf'
   *  Inport: '<Root>/StPhase'
   *  Inport: '<Root>/StartFftKnockId'
   *  Inport: '<Root>/TSpark'
   *  Inport: '<Root>/ThPeakHR'
   *  Inport: '<Root>/ThPeakId'
   *  Inport: '<Root>/TypeKnockDist'
   *  Product: '<S6>/Product1'
   *  SignalConversion generated from: '<S1>/IonBuff'
   *  SignalConversion generated from: '<S1>/IonBuff2'
   *  SignalConversion generated from: '<S1>/IonBuff2FFT'
   *  SignalConversion generated from: '<S1>/IonBuff2FftPeak'
   *  SignalConversion generated from: '<S1>/IonBuffFFT'
   *  SignalConversion generated from: '<S1>/IonBuffFftPeak'
   *  SignalConversion generated from: '<S1>/MaxPossibleThPeak'
   *  SignalConversion generated from: '<S1>/ShownIdStartDist'
   *  SignalConversion generated from: '<S1>/ShownIdStopDist'
   *  SignalConversion generated from: '<S1>/ShownStartFftKnockId'
   *  SignalConversion generated from: '<S1>/ShownThPeakId'
   *  SignalConversion generated from: '<S1>/ShownThPeakRelThr'
   *  SignalConversion generated from: '<S1>/VtShownBaseFreq'
   *  SignalConversion generated from: '<S1>/Old_Value'
   *
   * Block description for '<S1>/Buffer_Record_Mgm':
   *  Requirements and description associated to this chart are directly
   *  linked to transitions and boxes.
   */
  /* Gateway: EOA/Buffer_Record_Mgm */
  /* During: EOA/Buffer_Record_Mgm */
  /* Requirements and description associated to this chart are directly linked to transitions and boxes. */
  /* Entry Internal: EOA/Buffer_Record_Mgm */
  /* Transition: '<S5>:8' */
  /* Transition: '<S5>:32' */
  /*  Assign a default value to each output to improve code generation  */
  /* Transition: '<S5>:33' */
  for (ionIdx = 0U; ((int32_T)ionIdx) < 2; ionIdx = (uint8_T)((int32_T)
        (((int32_T)ionIdx) + 1))) {
    /* Transition: '<S5>:10' */
    /* Transition: '<S5>:11' */
    if (((IonAbsTdcInUse[(ionIdx)] == ((int8_T)SHOW_DWELLINT_MORE_KNOCK)) &&
         (VtNSample[(ionIdx)] < NSampleM[(ionIdx)])) && (ShownKnockInt[(ionIdx)]
         <= 1073737728U)) {
      /* Transition: '<S5>:79':
       *  1. EISB_FCA6CYL_SW_REQ_1723: Record overwrite for mode m
         Software shall activate a new data rec... (ECU_SW_Requirements#3956)
       */
      /* Transition: '<S5>:91' */
      /*  Overwrite allowed if ion integral during coil charge is greater than a threshold
         and spark advance correction is stable  */
      if ((((int32_T)DwellInt[(IonAbsTdcEOA)]) >= ((int32_T)SHOWDWELLINTTHR)) &&
          (((int32_T)FlgDSAoutDis) == 0)) {
        /* Transition: '<S5>:95' */
        /* Transition: '<S5>:100' */
        flgoverwriteion = true;

        /* Transition: '<S5>:103' */
      } else {
        /* Transition: '<S5>:102' */
        /* Otherwise overwrite if knock intensity is greater than current recording one  */
        /* Graphical Function 'EvalKnockIntensity': '<S5>:710' */
        /* This function evaluates if is necessary to overwrite current buffers recording according to the intensity of new detected knock. */
        /* Transition: '<S5>:720' */
        if (KnockInt[(IonAbsTdcEOA)] > 1073737728U) {
          /* Transition: '<S5>:721' */
          /* Transition: '<S5>:723' */
          d_diff_delta_knock = IonAcqBufRec_MAX_SIGN_KNOCK_INT;

          /* Transition: '<S5>:724' */
        } else {
          /* Transition: '<S5>:722' */
          d_diff_delta_knock = (int32_T)KnockInt[(IonAbsTdcEOA)];
        }

        /* Transition: '<S5>:725' */
        d_diff_delta_knock -= (int32_T)ShownKnockInt[(ionIdx)];
        if (d_diff_delta_knock > ((int32_T)THRDIFFKNOCKINT)) {
          /* Transition: '<S5>:726' */
          /* Transition: '<S5>:728' */
          flgoverwriteion = true;

          /* Transition: '<S5>:729' */
        } else {
          /* Transition: '<S5>:727' */
          flgoverwriteion = false;
        }

        /* Transition: '<S5>:730' */
      }

      /* Transition: '<S5>:104' */
      /* Transition: '<S5>:215' */
      /* Transition: '<S5>:216' */
      /* Transition: '<S5>:217' */
      /* Transition: '<S5>:218' */
    } else {
      /* Transition: '<S5>:110' */
      if (((IonAbsTdcInUse[(ionIdx)] == ((int8_T)SHOW_ALL_DWELLINT_MORE_KNOCK)) &&
           (VtNSample[(ionIdx)] < NSampleM[(ionIdx)])) && (ShownKnockInt[(ionIdx)]
           <= 1073737728U)) {
        /* Transition: '<S5>:108':
         *  1. EISB_FCA6CYL_SW_REQ_1725: Record overwrite for mode n
           Software shall activate a new data rec... (ECU_SW_Requirements#3958)
         */
        /* Transition: '<S5>:137' */
        /*  Overwrite allowed if ion integral during coil charge is greater than a threshold  */
        if (((int32_T)DwellInt[(IonAbsTdcEOA)]) >= ((int32_T)SHOWDWELLINTTHR)) {
          /* Transition: '<S5>:145' */
          /* Transition: '<S5>:147' */
          flgoverwriteion = true;

          /* Transition: '<S5>:148' */
        } else {
          /* Transition: '<S5>:146' */
          /* Otherwise overwrite if knock intensity is greater than current recording one  */
          /* Graphical Function 'EvalKnockIntensity': '<S5>:710' */
          /* This function evaluates if is necessary to overwrite current buffers recording according to the intensity of new detected knock. */
          /* Transition: '<S5>:720' */
          if (KnockInt[(IonAbsTdcEOA)] > 1073737728U) {
            /* Transition: '<S5>:721' */
            /* Transition: '<S5>:723' */
            d_diff_delta_knock = IonAcqBufRec_MAX_SIGN_KNOCK_INT;

            /* Transition: '<S5>:724' */
          } else {
            /* Transition: '<S5>:722' */
            d_diff_delta_knock = (int32_T)KnockInt[(IonAbsTdcEOA)];
          }

          /* Transition: '<S5>:725' */
          d_diff_delta_knock -= (int32_T)ShownKnockInt[(ionIdx)];
          if (d_diff_delta_knock > ((int32_T)THRDIFFKNOCKINT)) {
            /* Transition: '<S5>:726' */
            /* Transition: '<S5>:728' */
            flgoverwriteion = true;

            /* Transition: '<S5>:729' */
          } else {
            /* Transition: '<S5>:727' */
            flgoverwriteion = false;
          }

          /* Transition: '<S5>:730' */
        }

        /* Transition: '<S5>:150' */
        /* Transition: '<S5>:216' */
        /* Transition: '<S5>:217' */
        /* Transition: '<S5>:218' */
      } else {
        /* Transition: '<S5>:164' */
        if (((((IonAbsTdcInUse[(ionIdx)] == ((int8_T)SHOW_MORE_KNOCK)) &&
               (VtNSample[(ionIdx)] < NSampleM[(ionIdx)])) && (DeltaKnockNPow
               [(IonAbsTdcEOA)] > 0)) && (ShownKnockInt[(ionIdx)] <= 1073737728U))
            || (((((IonAbsTdcInUse[(ionIdx)] == ((int8_T)SHOW_MORE_KNOCK_RPM)) &&
                   (VtNSample[(ionIdx)] < NSampleM[(ionIdx)])) &&
                  (DeltaKnockNPow[(IonAbsTdcEOA)] > 0)) && (Rpm >= RPMENIONPLOT))
                && (ShownKnockInt[(ionIdx)] <= 1073737728U))) {
          /* Transition: '<S5>:166':
           *  1. EISB_FCA6CYL_SW_REQ_1716: Record overwrite for mode h
             Software shall activate a new data rec... (ECU_SW_Requirements#3950)
           */
          /* Transition: '<S5>:167' */
          /* Transition: '<S5>:178':
           *  1. EISB_FCA6CYL_SW_REQ_1732: Record overwrite for mode s
             Software shall activate a new data rec... (ECU_SW_Requirements#3965)
           */
          /* Transition: '<S5>:174' */
          /* Transition: '<S5>:158' */
          /* Overwrite only  if knock intensity is greater than current recording one  */
          /* Graphical Function 'EvalKnockIntensity': '<S5>:710' */
          /* This function evaluates if is necessary to overwrite current buffers recording according to the intensity of new detected knock. */
          /* Transition: '<S5>:720' */
          if (KnockInt[(IonAbsTdcEOA)] > 1073737728U) {
            /* Transition: '<S5>:721' */
            /* Transition: '<S5>:723' */
            d_diff_delta_knock = IonAcqBufRec_MAX_SIGN_KNOCK_INT;

            /* Transition: '<S5>:724' */
          } else {
            /* Transition: '<S5>:722' */
            d_diff_delta_knock = (int32_T)KnockInt[(IonAbsTdcEOA)];
          }

          /* Transition: '<S5>:725' */
          d_diff_delta_knock -= (int32_T)ShownKnockInt[(ionIdx)];
          if (d_diff_delta_knock > ((int32_T)THRDIFFKNOCKINT)) {
            /* Transition: '<S5>:726' */
            /* Transition: '<S5>:728' */
            flgoverwriteion = true;

            /* Transition: '<S5>:729' */
          } else {
            /* Transition: '<S5>:727' */
            flgoverwriteion = false;
          }

          /* Transition: '<S5>:730' */
          /* Transition: '<S5>:162' */
          /* Transition: '<S5>:217' */
          /* Transition: '<S5>:218' */
        } else {
          /* Transition: '<S5>:177' */
          /* Transition: '<S5>:185' */
          if (((IonAbsTdcInUse[(ionIdx)] == ((int8_T)SHOW_MEGA_KNOCK_MORE_KNOCK))
               && (VtNSample[(ionIdx)] < NSampleM[(ionIdx)])) && (ShownKnockInt
               [(ionIdx)] <= 1073737728U)) {
            /* Transition: '<S5>:187':
             *  1. EISB_FCA6CYL_SW_REQ_1730: Record overwrite for mode r
               Software shall activate a new data rec... (ECU_SW_Requirements#3963)
             */
            /* Transition: '<S5>:200' */
            /*  Overwrite allowed if a mega knock has been detected  */
            if (((1 << ((uint32_T)IonAbsTdcEOA)) & ((int32_T)MegaKnock)) != 0) {
              /* Transition: '<S5>:194' */
              /* Transition: '<S5>:196' */
              flgoverwriteion = true;

              /* Transition: '<S5>:197' */
              /* Transition: '<S5>:211' */
            } else {
              /* Transition: '<S5>:206' */
              if (DeltaKnockNPow[(IonAbsTdcEOA)] > 0) {
                /* Transition: '<S5>:209' */
                /* Transition: '<S5>:195' */
                /* Otherwise overwrite if knock intensity is greater
                   than current recording one and knock has been
                   detected  */
                /* Graphical Function 'EvalKnockIntensity': '<S5>:710' */
                /* This function evaluates if is necessary to overwrite current buffers recording according to the intensity of new detected knock. */
                /* Transition: '<S5>:720' */
                if (KnockInt[(IonAbsTdcEOA)] > 1073737728U) {
                  /* Transition: '<S5>:721' */
                  /* Transition: '<S5>:723' */
                  d_diff_delta_knock = IonAcqBufRec_MAX_SIGN_KNOCK_INT;

                  /* Transition: '<S5>:724' */
                } else {
                  /* Transition: '<S5>:722' */
                  d_diff_delta_knock = (int32_T)KnockInt[(IonAbsTdcEOA)];
                }

                /* Transition: '<S5>:725' */
                d_diff_delta_knock -= (int32_T)ShownKnockInt[(ionIdx)];
                if (d_diff_delta_knock > ((int32_T)THRDIFFKNOCKINT)) {
                  /* Transition: '<S5>:726' */
                  /* Transition: '<S5>:728' */
                  flgoverwriteion = true;

                  /* Transition: '<S5>:729' */
                } else {
                  /* Transition: '<S5>:727' */
                  flgoverwriteion = false;
                }

                /* Transition: '<S5>:730' */
                /* Transition: '<S5>:211' */
              } else {
                /* Transition: '<S5>:212' */
                flgoverwriteion = false;
              }
            }

            /* Transition: '<S5>:199' */
            /* Transition: '<S5>:218' */
          } else {
            /* Transition: '<S5>:214' */
            flgoverwriteion = false;
          }
        }
      }
    }

    /* Transition: '<S5>:16' */
    if ((VtNSample[(ionIdx)] >= NSampleM[(ionIdx)]) || flgoverwriteion) {
      /* Transition: '<S5>:221' */
      /* Transition: '<S5>:224' */
      if (((int32_T)ShownCylUpdatedFlag[(ionIdx)]) == 0) {
        /* Transition: '<S5>:226' */
        /* Transition: '<S5>:230' */
        /*  Switch case along IonAbsTdcInUse value  */
        if ((IonAbsTdcInUse[(ionIdx)] >= 0) && (IonAbsTdcInUse[(ionIdx)] <
             ((int8_T)N_CYLINDER))) {
          /* Transition: '<S5>:232':
           *  1. EISB_FCA6CYL_SW_REQ_1706: Mode a
             Every time that VTIONABSTDC_x calibration is greater or equ... (ECU_SW_Requirements#3942)
           */
          /* Transition: '<S5>:238' */
          /* Transition: '<S5>:243' */
          /*  Record ion signals for selected cylinder  */
          VtShownAbsTdc[(ionIdx)] = (uint8_T)IonAbsTdcInUse[(ionIdx)];
          ShownCylUpdatedFlag[(ionIdx)] = 1U;

          /* Transition: '<S5>:244' */
          /* Transition: '<S5>:251' */
          /* Transition: '<S5>:278' */
          /* Transition: '<S5>:282' */
          /* Transition: '<S5>:312' */
          /* Transition: '<S5>:333' */
          /* Transition: '<S5>:335' */
          /* Transition: '<S5>:367' */
          /* Transition: '<S5>:374' */
          /* Transition: '<S5>:436' */
          /* Transition: '<S5>:442' */
          /* Transition: '<S5>:463' */
          /* Transition: '<S5>:478' */
          /* Transition: '<S5>:488' */
          /* Transition: '<S5>:518' */
          /* Transition: '<S5>:537' */
          /* Transition: '<S5>:547' */
          /* Transition: '<S5>:565' */
          /* Transition: '<S5>:573' */
          /* Transition: '<S5>:635' */
          /* Transition: '<S5>:1200' */
          /* Transition: '<S5>:1206' */
          /* Transition: '<S5>:1248' */
          /* Transition: '<S5>:1182' */
          /* Transition: '<S5>:611' */
        } else {
          /* Transition: '<S5>:253' */
          switch (IonAbsTdcInUse[(ionIdx)]) {
           case ((int8_T)SHOW_ALL_CYL):
            /* Transition: '<S5>:255':
             *  1. EISB_FCA6CYL_SW_REQ_1708: Mode b
               Every time that VTIONABSTDC_x calibration is equal to -1, t... (ECU_SW_Requirements#3943)
             */
            /* Transition: '<S5>:276' */
            /* Transition: '<S5>:269' */
            /*  Record ion signals for each cylinder alternately */
            if (VtShownAbsTdc[(ionIdx)] >= ((uint8_T)((int32_T)(((int32_T)
                    N_CYLINDER) - 1)))) {
              /* Transition: '<S5>:650' */
              /* Transition: '<S5>:652' */
              VtShownAbsTdc[(ionIdx)] = 0U;

              /* Transition: '<S5>:655' */
            } else {
              /* Transition: '<S5>:654' */
              VtShownAbsTdc[(ionIdx)] = (uint8_T)((int32_T)(((int32_T)
                VtShownAbsTdc[(ionIdx)]) + 1));
            }

            /* Transition: '<S5>:656' */
            /*  If no spark jump to following cylinder  */
            if (((uint32_T)StMisf[(VtShownAbsTdc[(ionIdx)])]) == MIS_FIRE) {
              /* Transition: '<S5>:659' */
              /* Transition: '<S5>:663' */
              VtShownAbsTdc[(ionIdx)] = (uint8_T)((int32_T)(((int32_T)
                VtShownAbsTdc[(ionIdx)]) + 1));
              if (VtShownAbsTdc[(ionIdx)] >= N_CYLINDER) {
                /* Transition: '<S5>:679' */
                /* Transition: '<S5>:676' */
                VtShownAbsTdc[(ionIdx)] = 0U;

                /* Transition: '<S5>:680' */
              } else {
                /* Transition: '<S5>:675' */
              }

              /* Transition: '<S5>:684' */
            } else {
              /* Transition: '<S5>:683' */
            }

            /* Transition: '<S5>:685' */
            ShownCylUpdatedFlag[(ionIdx)] = 1U;

            /* Transition: '<S5>:271' */
            /* Transition: '<S5>:273' */
            /* Transition: '<S5>:282' */
            /* Transition: '<S5>:312' */
            /* Transition: '<S5>:333' */
            /* Transition: '<S5>:335' */
            /* Transition: '<S5>:367' */
            /* Transition: '<S5>:374' */
            /* Transition: '<S5>:436' */
            /* Transition: '<S5>:442' */
            /* Transition: '<S5>:463' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_STPHASE_NOK):
            /* Transition: '<S5>:296' */
            /* Transition: '<S5>:298':
             *  1. EISB_FCA6CYL_SW_REQ_1710: Mode c
               Every time that VTIONABSTDC_x calibration is equal to -2, t... (ECU_SW_Requirements#3944)
             */
            /* Transition: '<S5>:299' */
            /*  Record ion signals only if phase search ended not correctly  */
            if (((uint32_T)StPhase[(IonAbsTdcEOA)]) != TH_START_FOUND) {
              /* Transition: '<S5>:688' */
              /* Transition: '<S5>:690' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:693' */
            } else {
              /* Transition: '<S5>:692' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
            }

            /* Transition: '<S5>:694' */
            /* Transition: '<S5>:289' */
            /* Transition: '<S5>:279' */
            /* Transition: '<S5>:312' */
            /* Transition: '<S5>:333' */
            /* Transition: '<S5>:335' */
            /* Transition: '<S5>:367' */
            /* Transition: '<S5>:374' */
            /* Transition: '<S5>:436' */
            /* Transition: '<S5>:442' */
            /* Transition: '<S5>:463' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_NONE):
            /* Transition: '<S5>:590' */
            /* Transition: '<S5>:313':
             *  1. EISB_FCA6CYL_SW_REQ_1711: Mode d
               Every time that VTIONABSTDC_x calibration is equal to -3, t... (ECU_SW_Requirements#3945)
             */
            /* Transition: '<S5>:311' */
            /* Transition: '<S5>:305' */
            /*  Don't record ion signals to decrease CPU load  */
            VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);

            /* Transition: '<S5>:307' */
            /* Transition: '<S5>:317' */
            /* Transition: '<S5>:333' */
            /* Transition: '<S5>:335' */
            /* Transition: '<S5>:367' */
            /* Transition: '<S5>:374' */
            /* Transition: '<S5>:436' */
            /* Transition: '<S5>:442' */
            /* Transition: '<S5>:463' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_ION_SPIKE):
            /* Transition: '<S5>:591' */
            /* Transition: '<S5>:331':
             *  1. EISB_FCA6CYL_SW_REQ_1712: Mode e
               Every time that VTIONABSTDC_x calibration is equal to -4, t... (ECU_SW_Requirements#3946)
             */
            /* Transition: '<S5>:329' */
            /*  Record ion signals only if is present a spike  */
            if (((int32_T)NSampIonSpike) != 0) {
              /* Transition: '<S5>:699' */
              /* Transition: '<S5>:700' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:702' */
            } else {
              /* Transition: '<S5>:698' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
            }

            /* Transition: '<S5>:701' */
            /* Transition: '<S5>:327' */
            /* Transition: '<S5>:319' */
            /* Transition: '<S5>:335' */
            /* Transition: '<S5>:367' */
            /* Transition: '<S5>:374' */
            /* Transition: '<S5>:436' */
            /* Transition: '<S5>:442' */
            /* Transition: '<S5>:463' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_ION_SPIKE_KNOCK):
            /* Transition: '<S5>:592' */
            /* Transition: '<S5>:339':
             *  1. EISB_FCA6CYL_SW_REQ_1713: Mode f
               Every time that VTIONABSTDC_x calibration is equal to -7, t... (ECU_SW_Requirements#3947)
             */
            /* Transition: '<S5>:341' */
            /*  Record ion signals only if are present spike and knock */
            if ((((int32_T)NSampIonSpike) != 0) && (DeltaKnockNPow[(IonAbsTdcEOA)]
                 > 0)) {
              /* Transition: '<S5>:733' */
              /* Transition: '<S5>:735' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:736' */
            } else {
              /* Transition: '<S5>:734' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
            }

            /* Transition: '<S5>:737' */
            /* Transition: '<S5>:350' */
            /* Transition: '<S5>:351' */
            /* Transition: '<S5>:367' */
            /* Transition: '<S5>:374' */
            /* Transition: '<S5>:436' */
            /* Transition: '<S5>:442' */
            /* Transition: '<S5>:463' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_ION_KNOCK):
            /* Transition: '<S5>:593' */
            /* Transition: '<S5>:366':
             *  1. EISB_FCA6CYL_SW_REQ_1714: Mode g
               Every time that VTIONABSTDC_x calibration is equal to -8, t... (ECU_SW_Requirements#3948)
             */
            /* Transition: '<S5>:355' */
            /*  Record ion signals only if knock is present  */
            if (DeltaKnockNPow[(IonAbsTdcEOA)] > 0) {
              /* Transition: '<S5>:742' */
              /* Transition: '<S5>:747' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:744' */
            } else {
              /* Transition: '<S5>:746' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
            }

            /* Transition: '<S5>:750' */
            /* Transition: '<S5>:365' */
            /* Transition: '<S5>:352' */
            /* Transition: '<S5>:374' */
            /* Transition: '<S5>:436' */
            /* Transition: '<S5>:442' */
            /* Transition: '<S5>:463' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_MORE_KNOCK):
            /* Transition: '<S5>:594' */
            /* Transition: '<S5>:375':
             *  1. EISB_FCA6CYL_SW_REQ_1715: Mode h
               Every time that VTIONABSTDC_x calibration is equal to -9, t... (ECU_SW_Requirements#3949)
             */
            /* Transition: '<S5>:373' */
            /*  Record ion signals only if knock is present  */
            if (DeltaKnockNPow[(IonAbsTdcEOA)] > 0) {
              /* Transition: '<S5>:753' */
              /* Transition: '<S5>:759' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;
              if (KnockInt[(IonAbsTdcEOA)] < 1073737728U) {
                ShownKnockInt[(ionIdx)] = KnockInt[(IonAbsTdcEOA)];
              } else {
                ShownKnockInt[(ionIdx)] = 1073737728U;
              }

              /* Transition: '<S5>:766' */
            } else {
              /* Transition: '<S5>:755' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
            }

            /* Transition: '<S5>:760' */
            /* Transition: '<S5>:383' */
            /* Transition: '<S5>:371' */
            /* Transition: '<S5>:436' */
            /* Transition: '<S5>:442' */
            /* Transition: '<S5>:463' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_ION_MISF):
            /* Transition: '<S5>:596' */
            /* Transition: '<S5>:431' */
            /* Transition: '<S5>:434' */
            /* Transition: '<S5>:424' */
            /*  Don't record ion signals, functionality not yet implemented  */
            VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);

            /* Transition: '<S5>:426' */
            /* Transition: '<S5>:430' */
            /* Transition: '<S5>:442' */
            /* Transition: '<S5>:463' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_DIAG_KNOCK_COH):
            /* Transition: '<S5>:598' */
            /* Transition: '<S5>:441':
             *  1. EISB_FCA6CYL_SW_REQ_1717: Mode i
               Every time that VTIONABSTDC_y calibration is equal to -6, t... (ECU_SW_Requirements#3951)
             */
            /* Transition: '<S5>:440' */
            /* Transition: '<S5>:812' */
            /*  Record ion signals only if knock coherence diagnosis is valid and
               if knock is present  */
            if ((PtFault[IonAbsTdcEOA + DIAG_KNOCK_COH_0] != NO_PT_FAULT) &&
                (DeltaKnockNPow[(IonAbsTdcEOA)] > 0)) {
              /* Transition: '<S5>:805' */
              /* Transition: '<S5>:808' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:804' */
            } else {
              /* Transition: '<S5>:811' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
            }

            /* Transition: '<S5>:813' */
            /* Transition: '<S5>:453' */
            /* Transition: '<S5>:444' */
            /* Transition: '<S5>:463' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_MEGA_KNOCK):
            /* Transition: '<S5>:599' */
            /* Transition: '<S5>:462':
             *  1. EISB_FCA6CYL_SW_REQ_1719: Mode j
               Every time that VTIONABSTDC_x calibration is equal to -10, ... (ECU_SW_Requirements#3952)
             */
            /* Transition: '<S5>:456' */
            /*  Record ion signals only if mega knock is present  */
            if (((1 << ((uint32_T)IonAbsTdcEOA)) & ((int32_T)MegaKnock)) != 0) {
              /* Transition: '<S5>:817' */
              /* Transition: '<S5>:816' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:814' */
            } else {
              /* Transition: '<S5>:820' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
            }

            /* Transition: '<S5>:822' */
            /* Transition: '<S5>:470' */
            /* Transition: '<S5>:460' */
            /* Transition: '<S5>:478' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_DWELLINT):
            /* Transition: '<S5>:600' */
            /* Transition: '<S5>:480':
             *  1. EISB_FCA6CYL_SW_REQ_1720: Mode k
               Every time that VTIONABSTDC_x calibration is equal to -11, ... (ECU_SW_Requirements#3953)
             */
            /* Transition: '<S5>:483' */
            /*  Record ion signals only if ion integral during coil charge is greater than
               a threshold and spark advance correction is stable  */
            if ((((int32_T)DwellInt[(IonAbsTdcEOA)]) >= ((int32_T)
                  SHOWDWELLINTTHR)) && (((int32_T)FlgDSAoutDis) == 0)) {
              /* Transition: '<S5>:826' */
              /* Transition: '<S5>:825' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:823' */
            } else {
              /* Transition: '<S5>:829' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
            }

            /* Transition: '<S5>:831' */
            /* Transition: '<S5>:477' */
            /* Transition: '<S5>:485' */
            /* Transition: '<S5>:488' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_KNOCK_DIST):
            /* Transition: '<S5>:601' */
            /* Transition: '<S5>:499':
             *  1. EISB_FCA6CYL_SW_REQ_1721: Mode l
               Every time that VTIONABSTDC_x calibration is equal to -12, ... (ECU_SW_Requirements#3954)
             */
            /* Transition: '<S5>:497' */
            /*  Record ion signals only if a noise is present  */
            if (((int32_T)TypeKnockDist) != 0) {
              /* Transition: '<S5>:838' */
              /* Transition: '<S5>:837' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownKnockInt[(ionIdx)] = KnockInt[(IonAbsTdcEOA)];
              ShownThPeakId[(ionIdx)] = ThPeakId;
              ShownIdStartDist[(ionIdx)] = IdStartDist;
              ShownIdStopDist[(ionIdx)] = IdStopDist;
              ShownStartFftKnockId[(ionIdx)] = StartFftKnockId;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:835' */
            } else {
              /* Transition: '<S5>:834' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
              ShownKnockInt[(ionIdx)] = MAX_uint32_T;
              ShownThPeakId[(ionIdx)] = ((uint16_T)MAX_UINT16);
              ShownIdStartDist[(ionIdx)] = ((uint16_T)MAX_UINT16);
              ShownIdStopDist[(ionIdx)] = ((uint16_T)MAX_UINT16);
              ShownStartFftKnockId[(ionIdx)] = ((uint16_T)MAX_INT16);
            }

            /* Transition: '<S5>:840' */
            /* Transition: '<S5>:495' */
            /* Transition: '<S5>:500' */
            /* Transition: '<S5>:518' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_DWELLINT_MORE_KNOCK):
            /* Transition: '<S5>:602' */
            /* Transition: '<S5>:513':
             *  1. EISB_FCA6CYL_SW_REQ_1722: Mode m
               Every time that VTIONABSTDC_x calibration is equal to -13, ... (ECU_SW_Requirements#3955)
             */
            /* Transition: '<S5>:517' */
            /*  Record ion signals only if ion integral during coil charge is greater than
               a threshold and spark advance correction is stable  */
            if ((((int32_T)DwellInt[(IonAbsTdcEOA)]) >= ((int32_T)
                  SHOWDWELLINTTHR)) && (((int32_T)FlgDSAoutDis) == 0)) {
              /* Transition: '<S5>:860' */
              /* Transition: '<S5>:859' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;
              ShownKnockInt[(ionIdx)] = MAX_uint32_T;

              /* Transition: '<S5>:857' */
              /* Transition: '<S5>:869' */
            } else {
              /* Transition: '<S5>:864' */
              /*  Otherwise record ion signals if knock is present  */
              if (DeltaKnockNPow[(IonAbsTdcEOA)] > 0) {
                /* Transition: '<S5>:866' */
                /* Transition: '<S5>:868' */
                VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
                ShownCylUpdatedFlag[(ionIdx)] = 1U;
                if (KnockInt[(IonAbsTdcEOA)] < 1073737728U) {
                  ShownKnockInt[(ionIdx)] = KnockInt[(IonAbsTdcEOA)];
                } else {
                  ShownKnockInt[(ionIdx)] = 1073737728U;
                }

                /* Transition: '<S5>:869' */
              } else {
                /* Transition: '<S5>:870' */
                VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
              }
            }

            /* Transition: '<S5>:862' */
            /* Transition: '<S5>:512' */
            /* Transition: '<S5>:519' */
            /* Transition: '<S5>:537' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_ALL_DWELLINT_MORE_KNOCK):
            /* Transition: '<S5>:603' */
            /* Transition: '<S5>:536':
             *  1. EISB_FCA6CYL_SW_REQ_1724: Mode n
               Every time that VTIONABSTDC_x calibration is equal to -14, ... (ECU_SW_Requirements#3957)
             */
            /* Transition: '<S5>:527' */
            /*  Record ion signals only if ion integral during coil charge is greater than a threshold  */
            if (((int32_T)DwellInt[(IonAbsTdcEOA)]) >= ((int32_T)SHOWDWELLINTTHR))
            {
              /* Transition: '<S5>:883' */
              /* Transition: '<S5>:882' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;
              ShownKnockInt[(ionIdx)] = MAX_uint32_T;

              /* Transition: '<S5>:880' */
              /* Transition: '<S5>:876' */
            } else {
              /* Transition: '<S5>:875' */
              /*  Otherwise record ion signals if knock is present  */
              if (DeltaKnockNPow[(IonAbsTdcEOA)] > 0) {
                /* Transition: '<S5>:877' */
                /* Transition: '<S5>:885' */
                VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
                ShownCylUpdatedFlag[(ionIdx)] = 1U;
                if (KnockInt[(IonAbsTdcEOA)] < 1073737728U) {
                  ShownKnockInt[(ionIdx)] = KnockInt[(IonAbsTdcEOA)];
                } else {
                  ShownKnockInt[(ionIdx)] = 1073737728U;
                }

                /* Transition: '<S5>:876' */
              } else {
                /* Transition: '<S5>:874' */
                VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
              }
            }

            /* Transition: '<S5>:886' */
            /* Transition: '<S5>:534' */
            /* Transition: '<S5>:524' */
            /* Transition: '<S5>:547' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_INTION_NOCOMB):
            /* Transition: '<S5>:604' */
            /* Transition: '<S5>:549':
             *  1. EISB_FCA6CYL_SW_REQ_1726: Mode o
               Every time that VTIONABSTDC_x calibration is equal to -16, ... (ECU_SW_Requirements#3959)
             */
            /* Transition: '<S5>:554' */
            /*  Record ion signals only if no combustion is detected */
            if (IntIon[(IonAbsTdcEOA)] < NoCombThr) {
              /* Transition: '<S5>:909' */
              /* Transition: '<S5>:908' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:906' */
            } else {
              /* Transition: '<S5>:912' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
            }

            /* Transition: '<S5>:905' */
            /* Transition: '<S5>:545' */
            /* Transition: '<S5>:555' */
            /* Transition: '<S5>:565' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_THPEAK_GREATER_THR):
            /* Transition: '<S5>:605' */
            /* Transition: '<S5>:563':
             *  1. EISB_FCA6CYL_SW_REQ_1727: Mode p
               Every time that VTIONABSTDC_x calibration is equal to -17, ... (ECU_SW_Requirements#3960)
             */
            /* Transition: '<S5>:559' */
            if ((((uint32_T)THPEAKPERC) * 25U) == 0U) {
              /* Transition: '<S5>:955' */
              /* Transition: '<S5>:957' */
              maxThPeak = (uint32_T)SHOWTHPEAKTHR;

              /* Transition: '<S5>:960' */
            } else {
              /* Outputs for Function Call SubSystem: '<S5>/Buffer_Record.SHOW_THPEAK_GREATER_THR.evalThThr' */
              /* DataTypeConversion: '<S6>/scalingfactor' incorporates:
               *  Constant: '<S6>/Constant'
               *  Inport: '<Root>/IonGainEOA'
               *  Product: '<S6>/Product'
               */
              /* Transition: '<S5>:959' */
              /* Simulink Function 'evalThThr': '<S5>:917' */
              scalingfactor = ((((uint32_T)IonGainEOA) * ((uint32_T)((uint16_T)
                MAX_ION_BUFFER))) >> ((uint32_T)8));
              maxThPeak = ((scalingfactor * ((uint32_T)THPEAKPERC)) >>
                           ((uint32_T)10));

              /* End of Outputs for SubSystem: '<S5>/Buffer_Record.SHOW_THPEAK_GREATER_THR.evalThThr' */
              MaxPossibleThPeak[(ionIdx)] = scalingfactor;
            }

            /* Transition: '<S5>:966' */
            if (ThPeakHR[(IonAbsTdcEOA)] >= maxThPeak) {
              /* Transition: '<S5>:968' */
              /* Transition: '<S5>:970' */
              ShownThPeakRelThr[(ionIdx)] = maxThPeak;
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownKnockInt[(ionIdx)] = KnockInt[(IonAbsTdcEOA)];
              ShownThPeakId[(ionIdx)] = ThPeakId;
              ShownIdStartDist[(ionIdx)] = IdStartDist;
              ShownIdStopDist[(ionIdx)] = IdStopDist;
              ShownStartFftKnockId[(ionIdx)] = StartFftKnockId;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:974' */
            } else {
              /* Transition: '<S5>:971' */
              ShownThPeakRelThr[(ionIdx)] = 0U;
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
              ShownKnockInt[(ionIdx)] = MAX_uint32_T;
              ShownThPeakId[(ionIdx)] = ((uint16_T)MAX_UINT16);
              ShownIdStartDist[(ionIdx)] = ((uint16_T)MAX_UINT16);
              ShownIdStopDist[(ionIdx)] = ((uint16_T)MAX_UINT16);
              ShownStartFftKnockId[(ionIdx)] = ((uint16_T)MAX_INT16);
            }

            /* Transition: '<S5>:975' */
            /* Transition: '<S5>:572' */
            /* Transition: '<S5>:561' */
            /* Transition: '<S5>:573' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_TSPARK_UNDER_THR):
            /* Transition: '<S5>:606' */
            /* Transition: '<S5>:577':
             *  1. EISB_FCA6CYL_SW_REQ_1728: Mode q
               Every time that VTIONABSTDC_x calibration is equal to -18, ... (ECU_SW_Requirements#3961)
             */
            /* Transition: '<S5>:580' */
            /*  Record ion signals only if spark duration is lower than a threshold  */
            if (TSpark[(IonAbsTdcEOA)] <= IONSIGNALTSPARKTHR) {
              /* Transition: '<S5>:929' */
              /* Transition: '<S5>:930' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownKnockInt[(ionIdx)] = KnockInt[(IonAbsTdcEOA)];
              ShownThPeakId[(ionIdx)] = ThPeakId;
              ShownIdStartDist[(ionIdx)] = IdStartDist;
              ShownIdStopDist[(ionIdx)] = IdStopDist;
              ShownStartFftKnockId[(ionIdx)] = StartFftKnockId;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;

              /* Transition: '<S5>:931' */
            } else {
              /* Transition: '<S5>:928' */
              VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
              ShownKnockInt[(ionIdx)] = MAX_uint32_T;
              ShownThPeakId[(ionIdx)] = ((uint16_T)MAX_UINT16);
              ShownIdStartDist[(ionIdx)] = ((uint16_T)MAX_UINT16);
              ShownIdStopDist[(ionIdx)] = ((uint16_T)MAX_UINT16);
              ShownStartFftKnockId[(ionIdx)] = ((uint16_T)MAX_INT16);
            }

            /* Transition: '<S5>:932' */
            /* Transition: '<S5>:589' */
            /* Transition: '<S5>:578' */
            /* Transition: '<S5>:635' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           case ((int8_T)SHOW_MEGA_KNOCK_MORE_KNOCK):
            /* Transition: '<S5>:643' */
            /* Transition: '<S5>:637':
             *  1. EISB_FCA6CYL_SW_REQ_1729: Mode r
               Every time that VTIONABSTDC_x calibration is equal to -19, ... (ECU_SW_Requirements#3962)
             */
            /* Transition: '<S5>:640' */
            /*  Record ion signals only if mega knock is present  */
            if (((1 << ((uint32_T)IonAbsTdcEOA)) & ((int32_T)MegaKnock)) != 0) {
              /* Transition: '<S5>:950' */
              /* Transition: '<S5>:942' */
              VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
              ShownCylUpdatedFlag[(ionIdx)] = 1U;
              ShownKnockInt[(ionIdx)] = MAX_uint32_T;

              /* Transition: '<S5>:939' */
              /* Transition: '<S5>:938' */
            } else {
              /* Transition: '<S5>:945' */
              /*  Otherwise record ion signals if knock is present  */
              if (DeltaKnockNPow[(IonAbsTdcEOA)] > 0) {
                /* Transition: '<S5>:940' */
                /* Transition: '<S5>:937' */
                VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
                ShownCylUpdatedFlag[(ionIdx)] = 1U;
                if (KnockInt[(IonAbsTdcEOA)] < 1073737728U) {
                  ShownKnockInt[(ionIdx)] = KnockInt[(IonAbsTdcEOA)];
                } else {
                  ShownKnockInt[(ionIdx)] = 1073737728U;
                }

                /* Transition: '<S5>:938' */
              } else {
                /* Transition: '<S5>:936' */
                VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
              }
            }

            /* Transition: '<S5>:944' */
            /* Transition: '<S5>:630' */
            /* Transition: '<S5>:634' */
            /* Transition: '<S5>:1200' */
            /* Transition: '<S5>:1206' */
            /* Transition: '<S5>:1248' */
            /* Transition: '<S5>:1182' */
            /* Transition: '<S5>:611' */
            break;

           default:
            /* Transition: '<S5>:1174' */
            if ((IonAbsTdcInUse[(ionIdx)] == ((int8_T)SHOW_MORE_KNOCK_RPM)) &&
                (Rpm >= RPMENIONPLOT)) {
              /* Transition: '<S5>:1201':
               *  1. EISB_FCA6CYL_SW_REQ_1731: Mode s
                 Every time that VTIONABSTDC_x calibration is equal to -15, ... (ECU_SW_Requirements#3964)
               */
              /* Transition: '<S5>:1203' */
              /*  Record ion signals only if knock is present  */
              if (DeltaKnockNPow[(IonAbsTdcEOA)] > 0) {
                /* Transition: '<S5>:1190' */
                /* Transition: '<S5>:1191' */
                VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
                ShownCylUpdatedFlag[(ionIdx)] = 1U;
                if (KnockInt[(IonAbsTdcEOA)] < 1073737728U) {
                  ShownKnockInt[(ionIdx)] = KnockInt[(IonAbsTdcEOA)];
                } else {
                  ShownKnockInt[(ionIdx)] = 1073737728U;
                }

                /* Transition: '<S5>:1193' */
              } else {
                /* Transition: '<S5>:1192' */
                VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
              }

              /* Transition: '<S5>:1194' */
              /* Transition: '<S5>:1196' */
              /* Transition: '<S5>:1205' */
              /* Transition: '<S5>:1206' */
              /* Transition: '<S5>:1248' */
              /* Transition: '<S5>:1182' */
              /* Transition: '<S5>:611' */
            } else {
              /* Transition: '<S5>:1209' */
              if (IonAbsTdcInUse[(ionIdx)] == ((int8_T)SHOW_FLGOLSPARK_EN)) {
                /* Transition: '<S5>:1226' */
                /* Transition: '<S5>:1242' */
                /*  Record ion signals only if diagnosys on first samples of IonBuffer integral is active  */
                if (((int32_T)FlgOLSpark[(IonAbsTdcEOA)]) == 1) {
                  /* Transition: '<S5>:1235' */
                  /* Transition: '<S5>:1236' */
                  VtShownAbsTdc[(ionIdx)] = IonAbsTdcEOA;
                  ShownCylUpdatedFlag[(ionIdx)] = 1U;

                  /* Transition: '<S5>:1238' */
                } else {
                  /* Transition: '<S5>:1237' */
                  VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
                }

                /* Transition: '<S5>:1239' */
                /* Transition: '<S5>:1241' */
                /* Transition: '<S5>:1247' */
                /* Transition: '<S5>:1248' */
                /* Transition: '<S5>:1182' */
                /* Transition: '<S5>:611' */
              } else {
                /* Transition: '<S5>:1249' */
                if ((IonAbsTdcInUse[(ionIdx)] >= ((int8_T)N_CYLINDER)) &&
                    (IonAbsTdcInUse[(ionIdx)] < ((int8_T)((uint32_T)(((uint32_T)
                         N_CYLINDER) << ((uint32_T)1)))))) {
                  /* Transition: '<S5>:1172':
                   *  1. EISB_FCA6CYL_SW_REQ_1733: Mode t
                     Every time that VTIONABSTDC_x calibration is greater or equ... (ECU_SW_Requirements#3966)
                   */
                  /* Transition: '<S5>:1176' */
                  /* Transition: '<S5>:1157' */
                  tmpIonAbsTdcInUse = (int8_T)(((int32_T)IonAbsTdcInUse[(ionIdx)])
                    - ((int32_T)N_CYLINDER));

                  /*  Record ion signals only if knock is present  */
                  if (DeltaKnockNPow[(tmpIonAbsTdcInUse)] > 0) {
                    /* Transition: '<S5>:1158' */
                    /* Transition: '<S5>:1160' */
                    VtShownAbsTdc[(ionIdx)] = (uint8_T)tmpIonAbsTdcInUse;
                    ShownCylUpdatedFlag[(ionIdx)] = 1U;

                    /* Transition: '<S5>:1161' */
                  } else {
                    /* Transition: '<S5>:1159' */
                    VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
                  }

                  /* Transition: '<S5>:1162' */
                  /* Transition: '<S5>:1164' */
                  /* Transition: '<S5>:1181' */
                  /* Transition: '<S5>:1182' */
                  /* Transition: '<S5>:611' */
                } else {
                  /* Transition: '<S5>:644' */
                  VtShownAbsTdc[(ionIdx)] = ((uint8_T)MAX_UINT8);
                }
              }
            }
            break;
          }
        }

        /* Transition: '<S5>:612' */
      } else {
        /* Transition: '<S5>:228' */
      }

      /* Transition: '<S5>:614' */
      if (IonAbsTdcEOA == VtShownAbsTdc[(ionIdx)]) {
        /* Transition: '<S5>:616' */
        /* Transition: '<S5>:620':
         *  1. EISB_FCA6CYL_SW_REQ_1707: Every time that record functionality has been activated for the ac... (ECU_SW_Requirements#3968)
         */
        /* Graphical Function 'CopyIonBuf': '<S5>:980' */
        /* This function does the copy of ion signal on the buffer redirected to acquisition system. */
        /* Transition: '<S5>:982' */
        if (((int32_T)VTENCOPYIONBUFFV[(ionIdx)]) != 0) {
          /* Transition: '<S5>:994' */
          /* Transition: '<S5>:997' */
          for (i = 0U; i < NSampleMaxEOA; i = (uint16_T)((int32_T)(((int32_T)i)
                 + 1))) {
            /* Transition: '<S5>:1000' */
            /* Transition: '<S5>:1002' */
            if (((int32_T)ionIdx) == 0) {
              /* Transition: '<S5>:1004' */
              /* Transition: '<S5>:1008' */
              /* Simulink Function 'copyRawNumber': '<S5>:1217' */
              IonBuff[(i)] = IonBufferV[(i)];

              /* Transition: '<S5>:1009' */
            } else {
              /* Transition: '<S5>:1006' */
              /* Simulink Function 'copyRawNumber': '<S5>:1217' */
              IonBuff2[(i)] = IonBufferV[(i)];
            }

            /* Transition: '<S5>:1016' */
            /* Transition: '<S5>:1017' */
          }

          /* Transition: '<S5>:1036' */
          /* Transition: '<S5>:1040' */
        } else {
          /* Transition: '<S5>:1023' */
          for (i = 0U; i < NSampleMaxEOA; i = (uint16_T)((int32_T)(((int32_T)i)
                 + 1))) {
            /* Transition: '<S5>:1029' */
            /* Transition: '<S5>:1019' */
            if (((int32_T)ionIdx) == 0) {
              /* Transition: '<S5>:1030' */
              /* Transition: '<S5>:1033' */
              IonBuff[(i)] = (uint16_T)(IonBuffer[(i)] >> ((uint32_T)2));

              /* Transition: '<S5>:1028' */
            } else {
              /* Transition: '<S5>:1026' */
              IonBuff2[(i)] = (uint16_T)(IonBuffer[(i)] >> ((uint32_T)2));
            }

            /* Transition: '<S5>:1024' */
            /* Transition: '<S5>:1021' */
          }

          /* Transition: '<S5>:1039' */
        }

        /* Transition: '<S5>:1044' */
        while (i < MAX_SAMPLE) {
          /* Transition: '<S5>:1046' */
          /* Transition: '<S5>:1051' */
          if (((int32_T)ionIdx) == 0) {
            /* Transition: '<S5>:1056' */
            /* Transition: '<S5>:1052' */
            IonBuff[(i)] = 0U;

            /* Transition: '<S5>:1049' */
          } else {
            /* Transition: '<S5>:1053' */
            IonBuff2[(i)] = 0U;
          }

          /* Transition: '<S5>:1050' */
          i = (uint16_T)((int32_T)(((int32_T)i) + 1));
        }

        /* Transition: '<S5>:1058' */
        NSampleM[(ionIdx)] = NSampleMaxEOA;

        /* Graphical Function 'CopyIonBufMod': '<S5>:985' */
        /* This function performs the copy of ion signal, modified by spike detection algorithm, on the buffer redirected to acquisition system. */
        /* Transition: '<S5>:1064' */
        for (i = 0U; i < FFT_SAMPLE; i = (uint16_T)((int32_T)(((int32_T)i) + 1)))
        {
          /* Transition: '<S5>:1065' */
          /* Transition: '<S5>:1068' */
          if (((int32_T)ionIdx) == 0) {
            /* Transition: '<S5>:1073' */
            /* Transition: '<S5>:1077' */
            IonBuffFFT[(i)] = IonBufferMod[(i)];

            /* Transition: '<S5>:1079' */
          } else {
            /* Transition: '<S5>:1078' */
            IonBuff2FFT[(i)] = IonBufferMod[(i)];
          }

          /* Transition: '<S5>:1074' */
          /* Transition: '<S5>:1066' */
        }

        /* Transition: '<S5>:1067' */
        /* Graphical Function 'CopyIonFFTBuf': '<S5>:989' */
        /* This function does the copy of amplitude of FFT, performed on ion signal, on the buffer redirected to acquisition system. */
        /* Transition: '<S5>:1096' */
        for (i = 0U; i < FFT_SAMPLE_DIV2; i = (uint16_T)((int32_T)(((int32_T)i)
               + 1))) {
          /* Transition: '<S5>:1097' */
          /* Transition: '<S5>:1103' */
          if (((int32_T)ionIdx) == 0) {
            /* Transition: '<S5>:1099' */
            /* Transition: '<S5>:1102' */
            IonBuffFftPeak[(i)] = FftPeak[(i)];

            /* Transition: '<S5>:1100' */
          } else {
            /* Transition: '<S5>:1087' */
            IonBuff2FftPeak[(i)] = FftPeak[(i)];
          }

          /* Transition: '<S5>:1095' */
          /* Transition: '<S5>:1092' */
        }

        /* Transition: '<S5>:1094' */
        VtShownBaseFreq[(ionIdx)] = BaseFreq;
        VtNSample[(ionIdx)] = 0U;
        ShownCylUpdatedFlag[(ionIdx)] = 0U;

        /* Transition: '<S5>:622' */
      } else {
        /* Transition: '<S5>:621' */
      }

      /* Transition: '<S5>:623' */
    } else {
      /* Transition: '<S5>:222' */
    }

    /* Transition: '<S5>:20' */
    /* Transition: '<S5>:25' */
  }

  /* End of Chart: '<S1>/Buffer_Record_Mgm' */
  /* Transition: '<S5>:27' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufRec_EOA' */
}

/* Model step function */
void IonAcqBufRec_PowerOn(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufRec_PowerOn' incorporates:
   *  SubSystem: '<Root>/PowerOn'
   *
   * Block description for '<Root>/PowerOn':
   *  This block performs initialization for each model output.
   *
   * Block requirements for '<Root>/PowerOn':
   *  1. EISB_FCA6CYL_SW_REQ_1734: Software shall initialize each output produced for the "buffer record" strategy at ECU power on. (ECU_SW_Requirements#3940)
   */
  /* SignalConversion generated from: '<S2>/IonBuff' */
  memset((&(IonBuff[0])), 0, 800U * (sizeof(uint16_T)));

  /* SignalConversion generated from: '<S2>/IonBuff2' */
  memset((&(IonBuff2[0])), 0, 800U * (sizeof(uint16_T)));

  /* SignalConversion generated from: '<S2>/IonBuffFFT' */
  memset((&(IonBuffFFT[0])), 0, (sizeof(uint16_T)) << 7U);

  /* SignalConversion generated from: '<S2>/IonBuff2FFT' */
  memset((&(IonBuff2FFT[0])), 0, (sizeof(uint16_T)) << 7U);

  /* SignalConversion generated from: '<S2>/IonBuffFftPeak' */
  memset((&(IonBuffFftPeak[0])), 0, (sizeof(uint32_T)) << 6U);

  /* SignalConversion generated from: '<S2>/IonBuff2FftPeak' */
  memset((&(IonBuff2FftPeak[0])), 0, (sizeof(uint32_T)) << 6U);
  for (i = 0; i < 2; i++) {
    /* SignalConversion generated from: '<S2>/NSampleM' */
    NSampleM[(i)] = 0U;

    /* Product: '<S2>/Product' incorporates:
     *  Constant: '<S2>/Constant26'
     */
    VtNSample[(i)] = ((uint16_T)LAST_SAMPLE_ID);

    /* SignalConversion generated from: '<S2>/VtIonSignalFFT' */
    VtIonSignalFFT[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/VtFFTSignal' */
    VtFFTSignal[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/VtIonSignal' incorporates:
     *  Constant: '<S2>/Constant9'
     */
    VtIonSignal[(i)] = 0U;

    /* Product: '<S2>/Product2' incorporates:
     *  Constant: '<S2>/Constant29'
     */
    VtShownAbsTdc[(i)] = ((uint8_T)MAX_UINT8);

    /* SignalConversion generated from: '<S2>/IonAbsTdcInUse' */
    IonAbsTdcInUse[(i)] = 0;

    /* SignalConversion generated from: '<S2>/ShownCylUpdatedFlag' */
    ShownCylUpdatedFlag[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/ShownKnockInt' incorporates:
     *  Constant: '<S2>/Constant11'
     */
    ShownKnockInt[(i)] = 0U;

    /* Product: '<S2>/Product5' incorporates:
     *  Constant: '<S2>/Constant20'
     */
    ShownIdStartDist[(i)] = ((uint16_T)MAX_UINT16);

    /* Product: '<S2>/Product6' incorporates:
     *  Constant: '<S2>/Constant32'
     */
    ShownIdStopDist[(i)] = ((uint16_T)MAX_UINT16);

    /* Product: '<S2>/Product7' incorporates:
     *  Constant: '<S2>/Constant33'
     */
    ShownStartFftKnockId[(i)] = ((uint16_T)MAX_INT16);

    /* Product: '<S2>/Product4' incorporates:
     *  Constant: '<S2>/Constant22'
     */
    ShownThPeakId[(i)] = ((uint16_T)MAX_UINT16);

    /* SignalConversion generated from: '<S2>/MaxPossibleThPeak' incorporates:
     *  Constant: '<S2>/Constant23'
     */
    MaxPossibleThPeak[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/ShownThPeakRelThr' incorporates:
     *  Constant: '<S2>/Constant24'
     */
    ShownThPeakRelThr[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/VtShownBaseFreq' */
    VtShownBaseFreq[(i)] = 0U;
  }

  /* SignalConversion generated from: '<S2>/IonSignalFFT' incorporates:
   *  Constant: '<S2>/Constant2'
   */
  IonSignalFFT = 0U;

  /* SignalConversion generated from: '<S2>/IonSignal' incorporates:
   *  Constant: '<S2>/Constant3'
   */
  IonSignal = 0U;

  /* Chart: '<S4>/Buffer_MarkOut' incorporates:
   *  Constant: '<S2>/Constant34'
   *  SignalConversion generated from: '<S2>/NSample'
   */
  NSample = ((uint16_T)LAST_SAMPLE_ID);

  /* SignalConversion generated from: '<S2>/ShownAbsTdc' incorporates:
   *  Constant: '<S2>/Constant4'
   */
  ShownAbsTdc = ((uint8_T)MAX_UINT8);

  /* Constant: '<S2>/Constant5' */
  IdVer_IonAcqBufRec = ID_VER_IONACQBUFREC_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqBufRec_PowerOn' */
}

/* Model initialize function */
void IonAcqBufRec_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint16_T NSampleM[MAX_ION_SIGNALS];
uint16_T VtNSample[MAX_ION_SIGNALS];
void IonAcqBufRec_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<MAX_ION_SIGNALS;idx++) {
    NSampleM[idx] = 0u;
    VtNSample[idx] = 0u;
  }
}

void IonAcqBufRec_PowerOn(void)
{
  IonAcqBufRec_Stub();
}

void IonAcqBufRec_EOA(void)
{
  IonAcqBufRec_Stub();
}

void IonAcqBufRec_5ms(void)
{
  IonAcqBufRec_Stub();
}

#endif                                 /* _BUILD_IONACQBUFREC_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/