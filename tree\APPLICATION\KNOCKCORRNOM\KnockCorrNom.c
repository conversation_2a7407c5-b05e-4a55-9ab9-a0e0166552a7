/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Spec/Application/KnockCorrNom/trunk/KnockCorrNom_codegen/K#$  */
/* $Revision:: 210102                                                                                         $  */
/* $Date:: 2022-02-25 14:09:30 +0100 (ven, 25 feb 2022)                                                       $  */
/* $Author:: MarottaR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           KnockCorrNom.c
 **  File Creation Date: 25-Feb-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         KnockCorrNom
 **  Model Description:  The aim of this model is to calculate the spark advance nominal knock correction for each cylinder
 **  Model Version:      1.1096
 **  Model Author:       MarottaR - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: MarottaR - Fri Feb 25 14:04:54 2022
 **
 **  Last Saved Modification:  MarottaR - Fri Feb 25 12:37:11 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "KnockCorrNom_out.h"
#include "KnockCorrNom_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/KnockCorrNom_Scheduler' */
#define Knoc_event_KnockCorrNom_PowerOn (0)
#define KnockCo_event_KnockCorrNom_10ms (3)
#define KnockCor_event_KnockCorrNom_EOA (2)
#define Knock_event_KnockCorrNom_NoSync (1)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKDELTAKNOCK_dim               3U                        /* Referenced by:
                                                                  * '<S13>/BKDELTAKNOCK_dim'
                                                                  * '<S17>/BKDELTAKNOCK_dim'
                                                                  */

/* Length of breakpoint BKDELTAKNOCK */
#define BKRPMKNOCK5_dim                4U                        /* Referenced by:
                                                                  * '<S9>/BKRPMKNOCK5_dim'
                                                                  * '<S10>/BKRPMKNOCK5_dim'
                                                                  * '<S17>/BKRPMKNOCK5_dim'
                                                                  */

/* Length of breakpoint BKRPMKNOCK5 */
#define ID_VER_KNOCKCORRNOM_DEF        11096U                    /* Referenced by: '<Root>/KnockCorrNom_Scheduler' */

/* Model Version. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_KNOCKCORRNOM_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported block signals */
boolean_T FlgSakPos;                   /* '<S7>/Logical Operator' */

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static enum_KnockState KnockState_old[8];/* '<S1>/Memory' */

/* Knock state old */

/*Exported calibration memory section */
/*Init of exported calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_EXPORT_CALIBRATION */
CALQUAL CALQUAL_POST uint8_T THRCNTKNOCKCOH = 10U;/* Referenced by:
                                                   * '<S6>/THRCNTKNOCKCOH'
                                                   * '<S14>/THRCNTKNOCKCOH'
                                                   */

/* CntKnockCohEE threshold */

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKDELTAKNOCK[4] = { 0, 340, 819, 3072 }
;                                      /* Referenced by: '<S13>/BKDELTAKNOCK' */

/* Breakpoints of DeltaKnockNPow */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMKNOCK5[5] = { 1000U, 2000U, 3000U,
  4000U, 6500U } ;                     /* Referenced by: '<S9>/BKRPMKNOCK5' */

/* Breakpoints of engine speed for knock control */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T CNTKNOCKCOHDEC = 1U;
                                     /* Referenced by: '<S25>/CNTKNOCKCOHDEC' */

/* CntKnockCohEE decrement step */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T CNTKNOCKCOHINC = 3U;/* Referenced by:
                                                              * '<S6>/CNTKNOCKCOHINC'
                                                              * '<S14>/CNTKNOCKCOHINC'
                                                              */

/* CntKnockCohEE increment step */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENKNOCKCOHNORON = 0U;
                                    /* Referenced by: '<S14>/ENKNOCKCOHNORON' */

/* Enable knocking coherence during RON detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T HYSTSAKKNOCKCOH = 24;
                                    /* Referenced by: '<S14>/HYSTSAKKNOCKCOH' */

/* Hyst on the threshold on the knock offset for the coherence diagnosis */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T KCOHDIAGSTEP = 10U;/* Referenced by: '<S14>/KCOHDIAGSTEP' */

/* KCohDiagCnt increment step */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T KCORRINCDELAYI = 10U;
                                      /* Referenced by: '<S7>/KCORRINCDELAYI' */

/* Delay before further incrementing the knock correction (positive corr case) */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T KCORRINCI = 12;/* Referenced by: '<S7>/KCORRINCI' */

/* Step to increase the knock correction in torque control */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T KCORRINCN = 8;/* Referenced by: '<S7>/KCORRINCN' */

/* Step to increase the knock correction (normal case) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T NCYLKNOCKCOHMIN = 4U;
                                    /* Referenced by: '<S14>/NCYLKNOCKCOHMIN' */

/* NCylKnockCoh threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TAIRMAXKCOH = 1600;/* Referenced by: '<S14>/TAIRMAXKCOH' */

/* Threshold on the air temperature to disable knock coherence */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T TBKCORRDEC[20] = { -63, -63, -63, -63,
  -63, -63, -63, -63, -63, -63, -63, -63, -63, -63, -63, -63, -63, -63, -63, -63
} ;                                    /* Referenced by: '<S13>/TBKCORRDEC' */

/* Table of steps to reduce the spark advance in case of knocking */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T THRKCOHDIAGCNT = 100U;
                                     /* Referenced by: '<S14>/THRKCOHDIAGCNT' */

/* KCohDiagCnt threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T THSAKKNOCKCOH = 12;
                                      /* Referenced by: '<S14>/THSAKKNOCKCOH' */

/* Threshold on the SAKnockMin for the coherence diagnosis */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TWATMAXKCOH = 1760;/* Referenced by: '<S14>/TWATMAXKCOH' */

/* Threshold on the coolant temperature to disable knock coherence */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTKCORRINCDELAYN[5] = { 30U, 30U, 30U,
  30U, 30U } ;                      /* Referenced by: '<S7>/VTKCORRINCDELAYN' */

/* Delay before further incrementing the knock correction (normal case) */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
int16_T DeltaKCorrCyl;                 /* '<S4>/Merge' */

/* Delta knock correction for protection */
uint8_T FlgCntKnockCohInc[8];          /* '<S4>/Merge4' */

/* Flag to indicate CntKnockCohEE increment during trip */
uint8_T FlgKCohInc;                    /* '<S4>/Merge1' */

/* Flag to indicate NCylKnockCoh above threshold */
uint8_T FlgKCohIncLev1[8];             /* '<S4>/Merge3' */

/* Flag to indicate knocking coherence active and below threshold */
uint8_T KCohDiagCnt[8];                /* '<S4>/Merge2' */

/* Internal counter for knocking coherence */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint8_T CntKnockCohPOn[8];/* '<S4>/Merge10' */

/* Knocking coherence diagnosis pre-filter - Power-on value */
STATIC_TEST_POINT uint8_T CountCylOut[8];/* '<S4>/Merge12' */

/* Counter for increment of knock correction */
STATIC_TEST_POINT uint8_T EnIncCylOut[8];/* '<S4>/Merge11' */

/* Enable increment for knock correction */
STATIC_TEST_POINT uint32_T IdVer_KnockCorrNom;/* '<Root>/KnockCorrNom_Scheduler' */

/* Model Version */
STATIC_TEST_POINT int16_T KCorrDec;    /* '<S4>/Merge6' */

/* Negative Delta knock correction for protection */
STATIC_TEST_POINT int16_T KCorrInc;    /* '<S4>/Merge7' */

/* Positive Delta knock correction for protection */
STATIC_TEST_POINT uint8_T KCorrIncDelay;/* '<S4>/Merge8' */

/* Delay to increase the protective knock corr  */
STATIC_TEST_POINT uint8_T NCylKnockCoh;/* '<S4>/Merge9' */

/* Number of cylinders with knocking coherence active */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void Knock_chartstep_c3_KnockCorrNom(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/KnockCorrNom_Scheduler' */
static void Knock_chartstep_c3_KnockCorrNom(const int32_T *sfEvent)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_IR_U8;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_S16_o1;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  int16_T rtb_Look2D_IR_S8;
  int32_T s25_iter;
  boolean_T rtb_RelationalOperator2;
  uint8_T rtb_EnIncCylIn;
  boolean_T rtb_KnockAct;
  uint8_T IndCylRec;
  int16_T rtb_DeltaKnockNPowCyl;
  enum_KnockState tmp;
  boolean_T guard1 = false;
  boolean_T guard2 = false;

  /* Chart: '<Root>/KnockCorrNom_Scheduler'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  /* Chart: '<Root>/KnockCorrNom_Scheduler' incorporates:
   *  Inport: '<Root>/FlgSyncPhased'
   *  Inport: '<Root>/KnockCorrMode'
   *  Inport: '<Root>/VtRec'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  /* During: KnockCorrNom_Scheduler */
  /* Entry Internal: KnockCorrNom_Scheduler */
  /* Transition: '<S3>:5' */
  switch (*sfEvent) {
   case Knoc_event_KnockCorrNom_PowerOn:
    /* Outputs for Function Call SubSystem: '<Root>/Init_fcn'
     *
     * Block description for '<Root>/Init_fcn':
     *  This block performs outputs initialization.
     */
    /* SignalConversion generated from: '<S2>/DeltaKCorrCyl' incorporates:
     *  Constant: '<S2>/constant1'
     */
    /* Transition: '<S3>:7' */
    /* Transition: '<S3>:11':
     *  1. EISB_FCA6CYL_SW_REQ_1877: Software shall set to 0 each software variable implemented in indi... (ECU_SW_Requirements#6968)
     */
    /* Event: '<S3>:12' */
    DeltaKCorrCyl = 0;

    /* SignalConversion generated from: '<S2>/FlgKCohInc' incorporates:
     *  Constant: '<S2>/Constant3'
     */
    FlgKCohInc = 0U;

    /* SignalConversion generated from: '<S2>/KCorrDec' incorporates:
     *  Constant: '<S2>/constant2'
     */
    KCorrDec = 0;

    /* SignalConversion generated from: '<S2>/KCorrInc' incorporates:
     *  Constant: '<S2>/constant3'
     */
    KCorrInc = 0;

    /* SignalConversion generated from: '<S2>/KCorrIncDelay' incorporates:
     *  Constant: '<S2>/Constant2'
     */
    KCorrIncDelay = 0U;

    /* SignalConversion generated from: '<S2>/NCylKnockCoh' incorporates:
     *  Constant: '<S2>/Constant1'
     */
    NCylKnockCoh = 0U;

    /* End of Outputs for SubSystem: '<Root>/Init_fcn' */
    IdVer_KnockCorrNom = ID_VER_KNOCKCORRNOM_DEF;

    /* Outputs for Function Call SubSystem: '<Root>/NoSync_fcn'
     *
     * Block description for '<Root>/NoSync_fcn':
     *  This block performs internal counters reset in case of no sync.
     */
    /* Outputs for Iterator SubSystem: '<S5>/Reset_Counters' incorporates:
     *  ForIterator: '<S26>/For Iterator'
     *
     * Block description for '<S5>/Reset_Counters':
     *  This block performs internal counters reset in case of no sync.
     */
    /* Outputs for Function Call SubSystem: '<Root>/Init_fcn'
     *
     * Block description for '<Root>/Init_fcn':
     *  This block performs outputs initialization.
     */
    /* Outputs for Iterator SubSystem: '<S2>/Reset_CntKnockCoh' incorporates:
     *  ForIterator: '<S25>/For Iterator'
     *
     * Block description for '<S2>/Reset_CntKnockCoh':
     *  This block performs the reset of diagnosis coherence variables at
     *  initialization event.
     */
    /* Transition: '<S3>:75' */
    /* Transition: '<S3>:77' */
    /* Event: '<S3>:71' */
    for (s25_iter = 0; s25_iter < 8; s25_iter++) {
      /* Switch: '<S25>/Switch' incorporates:
       *  Constant: '<S25>/CNTKNOCKCOHDEC'
       *  Constant: '<S25>/Constant'
       *  MultiPortSwitch: '<S25>/Index Vector'
       *  RelationalOperator: '<S25>/Relational Operator'
       *  SignalConversion generated from: '<S2>/CntKnockCohEE_in'
       *  Sum: '<S25>/Sum'
       */
      if (CntKnockCohEE[(s25_iter)] > CNTKNOCKCOHDEC) {
        IndCylRec = (uint8_T)(CntKnockCohEE[(s25_iter)] - CNTKNOCKCOHDEC);
      } else {
        IndCylRec = 0U;
      }

      /* End of Switch: '<S25>/Switch' */

      /* SignalConversion generated from: '<S2>/CntKnockCohEE' incorporates:
       *  Assignment: '<S25>/Assignment'
       */
      CntKnockCohEE[(s25_iter)] = IndCylRec;

      /* SignalConversion generated from: '<S2>/CntKnockCohPOn' incorporates:
       *  Assignment: '<S25>/Assignment1'
       */
      CntKnockCohPOn[(s25_iter)] = IndCylRec;

      /* SignalConversion generated from: '<S2>/FlgCntKnockCohInc' */
      FlgCntKnockCohInc[(s25_iter)] = 0U;

      /* SignalConversion generated from: '<S2>/FlgKCohIncLev1' */
      FlgKCohIncLev1[(s25_iter)] = 0U;

      /* SignalConversion generated from: '<S2>/KCohDiagCnt' */
      KCohDiagCnt[(s25_iter)] = 0U;

      /* Switch: '<S26>/Switch1' incorporates:
       *  Constant: '<S26>/Constant3'
       *  MultiPortSwitch: '<S26>/Index Vector1'
       *  RelationalOperator: '<S26>/Relational Operator1'
       *  SignalConversion generated from: '<S5>/CountCylOut_In'
       */
      if (((int32_T)CountCylOut[(s25_iter)]) != 0) {
        /* SignalConversion generated from: '<S5>/CountCylOut' incorporates:
         *  Constant: '<S26>/Constant4'
         */
        CountCylOut[(s25_iter)] = 0U;
      }

      /* Switch: '<S26>/Switch' incorporates:
       *  Constant: '<S26>/Constant1'
       *  MultiPortSwitch: '<S26>/Index Vector'
       *  RelationalOperator: '<S26>/Relational Operator'
       *  SignalConversion generated from: '<S5>/EnIncCylOut_In'
       */
      if (((int32_T)EnIncCylOut[(s25_iter)]) != 0) {
        /* SignalConversion generated from: '<S5>/EnIncCylOut' incorporates:
         *  Constant: '<S26>/Constant2'
         */
        EnIncCylOut[(s25_iter)] = 0U;
      }
    }

    /* End of Outputs for SubSystem: '<S2>/Reset_CntKnockCoh' */
    /* End of Outputs for SubSystem: '<Root>/Init_fcn' */
    /* End of Outputs for SubSystem: '<S5>/Reset_Counters' */
    /* End of Outputs for SubSystem: '<Root>/NoSync_fcn' */
    /* Transition: '<S3>:78' */
    /* Transition: '<S3>:87' */
    break;

   case Knock_event_KnockCorrNom_NoSync:
    /* Outputs for Function Call SubSystem: '<Root>/NoSync_fcn'
     *
     * Block description for '<Root>/NoSync_fcn':
     *  This block performs internal counters reset in case of no sync.
     */
    /* Outputs for Iterator SubSystem: '<S5>/Reset_Counters' incorporates:
     *  ForIterator: '<S26>/For Iterator'
     *
     * Block description for '<S5>/Reset_Counters':
     *  This block performs internal counters reset in case of no sync.
     */
    /* Transition: '<S3>:20' */
    /* Transition: '<S3>:22' */
    /* Transition: '<S3>:52' */
    /* EnInc & Count reset */
    /* Transition: '<S3>:77' */
    /* Event: '<S3>:71' */
    for (s25_iter = 0; s25_iter < 8; s25_iter++) {
      /* Switch: '<S26>/Switch1' incorporates:
       *  Constant: '<S26>/Constant3'
       *  MultiPortSwitch: '<S26>/Index Vector1'
       *  RelationalOperator: '<S26>/Relational Operator1'
       *  SignalConversion generated from: '<S5>/CountCylOut_In'
       */
      if (((int32_T)CountCylOut[(s25_iter)]) != 0) {
        /* SignalConversion generated from: '<S5>/CountCylOut' incorporates:
         *  Constant: '<S26>/Constant4'
         */
        CountCylOut[(s25_iter)] = 0U;
      }

      /* Switch: '<S26>/Switch' incorporates:
       *  Constant: '<S26>/Constant1'
       *  MultiPortSwitch: '<S26>/Index Vector'
       *  RelationalOperator: '<S26>/Relational Operator'
       *  SignalConversion generated from: '<S5>/EnIncCylOut_In'
       */
      if (((int32_T)EnIncCylOut[(s25_iter)]) != 0) {
        /* SignalConversion generated from: '<S5>/EnIncCylOut' incorporates:
         *  Constant: '<S26>/Constant2'
         */
        EnIncCylOut[(s25_iter)] = 0U;
      }
    }

    /* End of Outputs for SubSystem: '<S5>/Reset_Counters' */
    /* End of Outputs for SubSystem: '<Root>/NoSync_fcn' */
    /* Transition: '<S3>:78' */
    /* Transition: '<S3>:87' */
    break;

   case KnockCor_event_KnockCorrNom_EOA:
    /* Transition: '<S3>:9' */
    /* Transition: '<S3>:86' */
    /* ev_EOA &
       Recovery modality -> effects only on CntKnockCohEE according to requirements
       Slew modality -> CntKnockCohEE = CntKnockCohPOn (init value), no effects on other outputs
       Normal modality -> all outputs managed according to requirements */
    /* Transition: '<S3>:93' */
    if (((uint32_T)KnockCorrMode) == KNOCK_CORR_REC) {
      /* Outputs for Function Call SubSystem: '<Root>/Recovery_fcn'
       *
       * Block description for '<Root>/Recovery_fcn':
       *  In this block are implemented the recovery actions.
       */
      /* Chart: '<S6>/KnockCorrNom_Scheduler' incorporates:
       *  Constant: '<S6>/CNTKNOCKCOHINC'
       *  Constant: '<S6>/DIAG_KNOCK_COH_0'
       *  Constant: '<S6>/THRCNTKNOCKCOH'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Inport: '<Root>/StDiag'
       *  SignalConversion generated from: '<S6>/CountCylOut'
       *  SignalConversion generated from: '<S6>/EnIncCylOut'
       *  SignalConversion generated from: '<S6>/CntKnockCohPOn_old'
       *  SignalConversion generated from: '<S6>/FlgCntKnockCohInc_old'
       *
       * Block description for '<S6>/KnockCorrNom_Scheduler':
       *  In this block are implemented the recovery actions.
       */
      /* Transition: '<S3>:95' */
      /* Transition: '<S3>:97' */
      /* Event: '<S3>:98' */
      /* Gateway: Recovery_fcn/KnockCorrNom_Scheduler */
      /* During: Recovery_fcn/KnockCorrNom_Scheduler */
      /* Entry Internal: Recovery_fcn/KnockCorrNom_Scheduler */
      /* Transition: '<S27>:128' */
      /* Transition: '<S27>:174' */
      EnIncCylOut[(IonAbsTdcEOA)] = 0U;
      CountCylOut[(IonAbsTdcEOA)] = 0U;
      if (((uint32_T)StDiag[DIAG_KNOCK_COH_0 + IonAbsTdcEOA]) == FAULT) {
        /* Transition: '<S27>:127'
         * Requirements for Transition: '<S27>:127':
         *  1. EISB_FCA6CYL_SW_REQ_1846: The sotware shall sets the signal CntKnockCohEE equal to the calib... (ECU_SW_Requirements#6990)
         */
        CntKnockCohEE[(IonAbsTdcEOA)] = THRCNTKNOCKCOH;

        /* Transition: '<S27>:129' */
        /* Transition: '<S27>:131' */
        /* Transition: '<S27>:136' */
        /* Transition: '<S27>:133' */
      } else {
        /* Transition: '<S27>:137' */
        if (((int32_T)FlgCntKnockCohInc[(IonAbsTdcEOA)]) != 0) {
          /* Transition: '<S27>:134'
           * Requirements for Transition: '<S27>:134':
           *  1. EISB_FCA6CYL_SW_REQ_1847: In case knock control functionality is ENABLED, the recovery REC_K... (ECU_SW_Requirements#6991)
           */
          CntKnockCohEE[(IonAbsTdcEOA)] = (uint8_T)(CntKnockCohPOn[(IonAbsTdcEOA)]
            + CNTKNOCKCOHINC);
          if (CntKnockCohEE[(IonAbsTdcEOA)] >= THRCNTKNOCKCOH) {
            /* Transition: '<S27>:130'
             * Requirements for Transition: '<S27>:130':
             *  1. EISB_FCA6CYL_SW_REQ_1848: In case knock control functionality is ENABLED, the recovery REC_K... (ECU_SW_Requirements#6992)
             */
            CntKnockCohEE[(IonAbsTdcEOA)] = THRCNTKNOCKCOH;

            /* Transition: '<S27>:131' */
            /* Transition: '<S27>:136' */
            /* Transition: '<S27>:133' */
          } else {
            /* Transition: '<S27>:132' */
            /* Transition: '<S27>:133' */
          }
        } else {
          /* Transition: '<S27>:135' */
        }
      }

      /* End of Outputs for SubSystem: '<Root>/Recovery_fcn' */
      /* Transition: '<S27>:138' */
      /* Transition: '<S3>:101' */
    } else {
      /* Outputs for Function Call SubSystem: '<Root>/EOA_fcn'
       *
       * Block description for '<Root>/EOA_fcn':
       *  This block performs EOA functionalities.
       */
      /* S-Function (PreLookUpIdSearch_U16): '<S24>/PreLookUpIdSearch_U16' incorporates:
       *  Constant: '<S9>/BKRPMKNOCK5'
       *  Constant: '<S9>/BKRPMKNOCK5_dim'
       */
      /* Transition: '<S3>:46' */
      /* Event: '<S3>:13' */
      PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                            &rtb_PreLookUpIdSearch_U16_o2, RpmF, &BKRPMKNOCK5[0],
                            ((uint8_T)BKRPMKNOCK5_dim));

      /* Logic: '<S7>/Logical Operator' incorporates:
       *  DataTypeConversion: '<S7>/Data Type Conversion'
       *  DataTypeConversion: '<S7>/Data Type Conversion1'
       *  Inport: '<Root>/FlgSAKIndInc'
       *  Inport: '<Root>/FlgSAKnockInc'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Selector: '<S7>/Selector1'
       *  Selector: '<S7>/Selector5'
       */
      FlgSakPos = ((((int32_T)FlgSAKnockInc[(IonAbsTdcEOA)]) != 0) || (((int32_T)
        FlgSAKIndInc[(IonAbsTdcEOA)]) != 0));

      /* S-Function (LookUp_IR_U8): '<S12>/LookUp_IR_U8' incorporates:
       *  Constant: '<S10>/BKRPMKNOCK5_dim'
       *  Constant: '<S7>/VTKCORRINCDELAYN'
       */
      LookUp_IR_U8( &rtb_LookUp_IR_U8, &VTKCORRINCDELAYN[0],
                   rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                   ((uint8_T)BKRPMKNOCK5_dim));

      /* Chart: '<S7>/Parameters_Choice' incorporates:
       *  Constant: '<S7>/KCORRINCDELAYI'
       *  Constant: '<S7>/KCORRINCI'
       *  Constant: '<S7>/KCORRINCN'
       *  Inport: '<Root>/FlgNoTrqCtrSA'
       *
       * Block description for '<S7>/Parameters_Choice':
       *  In this stateflow according to the value of the flag of no torque control on spark advance, SAKnock and SAKCorrInd are set:
       *  - KcorrInc equal to the tunable calibration KCORRINCI or KCORRINCN
       *  - KCorrIncDelay to the tunable calibration KCORRINCDELAYI or to a 1-D look-up table (parameter VTKCORRINCDELAYN ), function of the engine speed filtered
       */
      /* Gateway: EOA_fcn/Correction_Inc_Calculation/Parameters_Choice */
      /* During: EOA_fcn/Correction_Inc_Calculation/Parameters_Choice */
      /* Entry Internal: EOA_fcn/Correction_Inc_Calculation/Parameters_Choice */
      /* Transition: '<S11>:20' */
      /* Case ev_EOA and Recovery disabled */
      /* Case ev_EOA and Slew modality */
      if ((((uint32_T)KnockCorrMode) != KNOCK_CORR_SLEW) && (((uint32_T)
            KnockCorrMode) != KNOCK_CORR_OFF)) {
        /* Transition: '<S11>:27' */
        /* Case ev_EOA and strategy disabled */
        /* Transition: '<S11>:25' */
        /* Case ev_EOA and Normal modality */
        if (FlgSakPos && (((int32_T)FlgNoTrqCtrSA) != 0)) {
          /* Transition: '<S11>:12' */
          /* Transition: '<S11>:16'
           * Requirements for Transition: '<S11>:16':
           *  1. EISB_FCA6CYL_SW_REQ_1225: The software shall estimate the positive delta knock correction (i... (ECU_SW_Requirements#2092)
           *  2. EISB_FCA6CYL_SW_REQ_1226: The software shall estimate the delta time of increment of knock c... (ECU_SW_Requirements#2094)
           */
          KCorrInc = KCORRINCI;
          KCorrIncDelay = KCORRINCDELAYI;

          /* Transition: '<S11>:17' */
        } else {
          /* Transition: '<S11>:14'
           * Requirements for Transition: '<S11>:14':
           *  1. EISB_FCA6CYL_SW_REQ_1228: The software shall estimate the increment of knock correction (ie.... (ECU_SW_Requirements#2093)
           *  2. EISB_FCA6CYL_SW_REQ_1229: The software shall estimate the delta time of increment of knock c... (ECU_SW_Requirements#2095)
           */
          KCorrInc = KCORRINCN;
          KCorrIncDelay = (uint8_T)(((uint32_T)rtb_LookUp_IR_U8) >> ((uint32_T)8));
        }
      } else {
        /* Transition: '<S11>:28' */
        /* Transition: '<S11>:34' */
        /* Transition: '<S11>:26' */
        /* Transition: '<S11>:36' */
        /* Transition: '<S11>:37' */
        /* Transition: '<S11>:17' */
      }

      /* End of Chart: '<S7>/Parameters_Choice' */

      /* Selector: '<S13>/Selector1' incorporates:
       *  Inport: '<Root>/DeltaKnockNPow'
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      /* Transition: '<S11>:19' */
      rtb_DeltaKnockNPowCyl = DeltaKnockNPow[(IonAbsTdcEOA)];

      /* S-Function (PreLookUpIdSearch_S16): '<S18>/PreLookUpIdSearch_S16' incorporates:
       *  Constant: '<S13>/BKDELTAKNOCK'
       *  Constant: '<S13>/BKDELTAKNOCK_dim'
       */
      PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1,
                            &rtb_PreLookUpIdSearch_S16_o2, rtb_DeltaKnockNPowCyl,
                            &BKDELTAKNOCK[0], ((uint8_T)BKDELTAKNOCK_dim));

      /* S-Function (Look2D_IR_S8): '<S20>/Look2D_IR_S8' incorporates:
       *  Constant: '<S13>/TBKCORRDEC'
       *  Constant: '<S17>/BKDELTAKNOCK_dim'
       *  Constant: '<S17>/BKRPMKNOCK5_dim'
       */
      Look2D_IR_S8( &rtb_Look2D_IR_S8, &TBKCORRDEC[0],
                   rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                   ((uint8_T)BKDELTAKNOCK_dim), rtb_PreLookUpIdSearch_U16_o1,
                   rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKRPMKNOCK5_dim));

      /* Chart: '<S13>/KCorrDec_Mgm' incorporates:
       *  Product: '<S19>/Divide'
       *
       * Block description for '<S13>/KCorrDec_Mgm':
       *  In this block is assigned to the correspond output the signal
       *  previously calculated
       */
      /* Gateway: EOA_fcn/Delta_Correction_Calculation/KCorrDec_Calculation/KCorrDec_Mgm */
      /* During: EOA_fcn/Delta_Correction_Calculation/KCorrDec_Calculation/KCorrDec_Mgm */
      /* Entry Internal: EOA_fcn/Delta_Correction_Calculation/KCorrDec_Calculation/KCorrDec_Mgm */
      /* Transition: '<S16>:87' */
      /* Case ev_EOA and Recovery disabled */
      /* Case ev_EOA and strategy disabled */
      if ((((uint32_T)KnockCorrMode) != KNOCK_CORR_OFF) && (((uint32_T)
            KnockCorrMode) != KNOCK_CORR_SLEW)) {
        /* Transition: '<S16>:84' */
        /* Case ev_EOA and Slew modality */
        /* Transition: '<S16>:88' */
        /* Case ev_EOA and Normal modality */
        /* Transition: '<S16>:96'
         * Requirements for Transition: '<S16>:96':
         *  1. EISB_FCA6CYL_SW_REQ_1231: The software shall estimate the negative delta knock correction (i... (ECU_SW_Requirements#2096)
         */
        KCorrDec = (int16_T)((rtb_Look2D_IR_S8 + 15360) / 32);
      } else {
        /* Transition: '<S16>:90' */
        /* Transition: '<S16>:106' */
        /* Transition: '<S16>:86' */
        /* Transition: '<S16>:103' */
        /* Transition: '<S16>:108' */
      }

      /* End of Chart: '<S13>/KCorrDec_Mgm' */

      /* Chart: '<S14>/Knock_Coh_PTfault' incorporates:
       *  Constant: '<S14>/CNTKNOCKCOHINC'
       *  Constant: '<S14>/KCOHDIAGSTEP'
       *  Constant: '<S14>/NCYLKNOCKCOHMIN'
       *  Constant: '<S14>/THRCNTKNOCKCOH'
       *  Constant: '<S14>/THRKCOHDIAGCNT'
       *  Constant: '<S14>/THSAKKNOCKCOH'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Inport: '<Root>/KnockState'
       *  Inport: '<Root>/SAKnockMin'
       *  SignalConversion generated from: '<S1>/FlgKCohIndLev1'
       *  SignalConversion generated from: '<S1>/CntKnockCohPOn_old'
       *  SignalConversion generated from: '<S1>/FlgCntKnockCohInc_old'
       *  SignalConversion generated from: '<S1>/KCohDiagCnt_old'
       *  SignalConversion generated from: '<S1>/NCylKnockCoh_old'
       *
       * Block description for '<S14>/Knock_Coh_PTfault':
       *  In this block are managed the conditions for knock coherence diagnasis
       *  and the recovery actions.
       */
      /* Transition: '<S16>:95' */
      /* Gateway: EOA_fcn/Delta_Correction_Calculation/Knock_Coh_PTfault_And_Recovery_Mgm/Knock_Coh_PTfault */
      /* During: EOA_fcn/Delta_Correction_Calculation/Knock_Coh_PTfault_And_Recovery_Mgm/Knock_Coh_PTfault */
      /* Entry Internal: EOA_fcn/Delta_Correction_Calculation/Knock_Coh_PTfault_And_Recovery_Mgm/Knock_Coh_PTfault */
      /* Transition: '<S21>:175' */
      rtb_KnockAct = false;

      /* Transition: '<S21>:179' */
      /* Case ev_EOA and Recovery disabled */
      CntKnockCohEE[(IonAbsTdcEOA)] = CntKnockCohPOn[(IonAbsTdcEOA)];

      /* Case ev_EOA and Slew modality */
      if ((((uint32_T)KnockCorrMode) != KNOCK_CORR_SLEW) && (((uint32_T)
            KnockCorrMode) != KNOCK_CORR_OFF)) {
        /* Transition: '<S21>:225' */
        /* Case ev_EOA and strategy disabled */
        /* Transition: '<S21>:22' */
        /* Case ev_EOA and Normal modality */
        tmp = KnockState[(IonAbsTdcEOA)];
        guard1 = false;
        guard2 = false;
        if ((((uint32_T)tmp) == ACTIVE_KNOCK) || (((uint32_T)tmp) == HEAVY_KNOCK))
        {
          /* Transition: '<S21>:24' */
          rtb_KnockAct = true;
          if (((int32_T)VtP2NoiseDetFlg[(IonAbsTdcEOA)]) != 0) {
            /* Transition: '<S21>:30' */
            guard2 = true;
          } else {
            /* Transition: '<S21>:32' */
            if ((((TAir < TAIRMAXKCOH) && (TWater < TWATMAXKCOH)) &&
                 (((((int32_T)FlgRonStoredEE) != 0) || (((int32_T)
                     FlgRonInheritEE) != 0)) || (((int32_T)ENKNOCKCOHNORON) != 0)))
                && (((int32_T)FlgCntKnockCohInc[(IonAbsTdcEOA)]) == 0)) {
              /* Transition: '<S21>:35' */
              s25_iter = ((int32_T)SAKnockMin) + ((int32_T)THSAKKNOCKCOH);
              if (((int32_T)SAKCorrInd[(IonAbsTdcEOA)]) < s25_iter) {
                /* Transition: '<S21>:40'
                 * Requirements for Transition: '<S21>:40':
                 *  1. EISB_FCA6CYL_SW_REQ_1826: If the following conditions are met knock:

                   (knock control functio... (ECU_SW_Requirements#6981)
                 */
                KCohDiagCnt[(IonAbsTdcEOA)] = (uint8_T)(KCohDiagCnt
                  [(IonAbsTdcEOA)] + KCOHDIAGSTEP);
                FlgKCohIncLev1[(IonAbsTdcEOA)] = 1U;
                if (KCohDiagCnt[(IonAbsTdcEOA)] >= THRKCOHDIAGCNT) {
                  /* Transition: '<S21>:52'
                   * Requirements for Transition: '<S21>:52':
                   *  1. EISB_FCA6CYL_SW_REQ_1849: If the following conditions are met:

                     (knock control functionality... (ECU_SW_Requirements#6982)
                   */
                  FlgCntKnockCohInc[(IonAbsTdcEOA)] = 1U;
                  CntKnockCohEE[(IonAbsTdcEOA)] = (uint8_T)(CntKnockCohEE
                    [(IonAbsTdcEOA)] + CNTKNOCKCOHINC);
                  NCylKnockCoh = (uint8_T)((int32_T)(((int32_T)NCylKnockCoh) + 1));
                } else {
                  /* Transition: '<S21>:54' */
                }

                if (CntKnockCohEE[(IonAbsTdcEOA)] >= THRCNTKNOCKCOH) {
                  /* Transition: '<S21>:63'
                   * Requirements for Transition: '<S21>:63':
                   *  1. EISB_FCA6CYL_SW_REQ_1850: If the following conditions are met:

                     (knock control functionality... (ECU_SW_Requirements#6983)
                   */
                  CntKnockCohEE[(IonAbsTdcEOA)] = THRCNTKNOCKCOH;
                } else {
                  /* Transition: '<S21>:65' */
                }

                if (NCylKnockCoh >= NCYLKNOCKCOHMIN) {
                  /* SignalConversion generated from: '<S1>/FlgKCohInc' */
                  /* Transition: '<S21>:67'
                   * Requirements for Transition: '<S21>:67':
                   *  1. EISB_FCA6CYL_SW_REQ_1851: If the following conditions are met:

                     (knock control functionality... (ECU_SW_Requirements#6984)
                   */
                  FlgKCohInc = 1U;
                } else {
                  /* Transition: '<S21>:68' */
                }
              } else {
                /* Transition: '<S21>:42' */
                if (((int32_T)SAKCorrInd[(IonAbsTdcEOA)]) > (s25_iter +
                     ((int32_T)HYSTSAKKNOCKCOH))) {
                  /* Transition: '<S21>:44' */
                  guard1 = true;
                } else {
                  /* Transition: '<S21>:46' */
                  /* Transition: '<S21>:90' */
                }
              }
            } else {
              /* Transition: '<S21>:37' */
              /* Transition: '<S21>:71' */
              guard1 = true;
            }
          }
        } else {
          /* Transition: '<S21>:26' */
          /* Transition: '<S21>:33' */
          guard2 = true;
        }

        if (guard2) {
          /* Transition: '<S21>:76' */
          if ((((uint32_T)tmp) == NO_KNOCK) && (((((int32_T)FlgRonStoredEE) != 0)
                || (((int32_T)FlgRonInheritEE) != 0)) || (((int32_T)
                 ENKNOCKCOHNORON) != 0))) {
            /* Transition: '<S21>:79' */
            guard1 = true;
          } else {
            /* Transition: '<S21>:78' */
            /* Transition: '<S21>:86' */
            /* Transition: '<S21>:80' */
            /* Transition: '<S21>:90' */
          }
        }

        if (guard1) {
          /* Transition: '<S21>:89' */
          if ((((int32_T)KCohDiagCnt[(IonAbsTdcEOA)]) > 0) && (((int32_T)
                FlgCntKnockCohInc[(IonAbsTdcEOA)]) == 0)) {
            /* Transition: '<S21>:74'
             * Requirements for Transition: '<S21>:74':
             *  1. EISB_FCA6CYL_SW_REQ_1852: If the following conditions are met:

               (knock control functionality... (ECU_SW_Requirements#6985)
             *  2. EISB_FCA6CYL_SW_REQ_1854: If the following conditions are met:

               (knock control functionality... (ECU_SW_Requirements#6987)
             */
            KCohDiagCnt[(IonAbsTdcEOA)] = (uint8_T)((int32_T)(((int32_T)
              KCohDiagCnt[(IonAbsTdcEOA)]) - 1));
          } else {
            /* Transition: '<S21>:81' */
          }

          if ((((int32_T)KCohDiagCnt[(IonAbsTdcEOA)]) == 0) && (((int32_T)
                FlgCntKnockCohInc[(IonAbsTdcEOA)]) == 0)) {
            /* Transition: '<S21>:83'
             * Requirements for Transition: '<S21>:83':
             *  1. EISB_FCA6CYL_SW_REQ_1853: If the following conditions are met:

               (knock control functionality... (ECU_SW_Requirements#6986)
             *  2. EISB_FCA6CYL_SW_REQ_1855: If the following conditions are met:

               (knock control functionality... (ECU_SW_Requirements#6988)
             */
            FlgKCohIncLev1[(IonAbsTdcEOA)] = 0U;
          } else {
            /* Transition: '<S21>:84' */
          }

          /* Transition: '<S21>:87' */
          /* Transition: '<S21>:80' */
          /* Transition: '<S21>:90' */
        }
      } else {
        /* Transition: '<S21>:227' */
        /* Transition: '<S21>:228' */
        /* Transition: '<S21>:192' */
        /* Transition: '<S21>:181' */
        /* Transition: '<S21>:186' */
        /* Transition: '<S21>:86' */
        /* Transition: '<S21>:80' */
        /* Transition: '<S21>:90' */
      }

      /* End of Chart: '<S14>/Knock_Coh_PTfault' */

      /* RelationalOperator: '<S15>/Relational Operator2' incorporates:
       *  Constant: '<S15>/NO_KNOCK'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Inport: '<Root>/KnockState'
       *  Selector: '<S15>/Selector2'
       */
      /* Transition: '<S21>:70' */
      rtb_RelationalOperator2 = (((uint32_T)KnockState[(IonAbsTdcEOA)]) ==
        NO_KNOCK);

      /* Switch: '<S15>/Switch2' incorporates:
       *  Constant: '<S15>/Constant2'
       *  DataTypeConversion: '<S15>/Data Type Conversion1'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Logic: '<S15>/Logical Operator2'
       *  Logic: '<S15>/Logical Operator3'
       *  Selector: '<S15>/Selector1'
       *  Selector: '<S15>/Selector5'
       *  SignalConversion generated from: '<S1>/CountCylOut_old'
       *  SignalConversion generated from: '<S1>/EnIncCylOut_old'
       *  Sum: '<S23>/FixPt Sum1'
       */
      if ((!rtb_RelationalOperator2) || (((int32_T)EnIncCylOut[(IonAbsTdcEOA)])
           != 0)) {
        IndCylRec = 1U;
      } else {
        IndCylRec = (uint8_T)(((uint32_T)CountCylOut[(IonAbsTdcEOA)]) + 1U);
      }

      /* End of Switch: '<S15>/Switch2' */

      /* Switch: '<S15>/Switch' incorporates:
       *  Constant: '<S15>/Constant3'
       *  Constant: '<S15>/NO_KNOCK'
       *  DataTypeConversion: '<S15>/Data Type Conversion2'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Logic: '<S15>/Logical Operator1'
       *  Memory: '<S1>/Memory'
       *  RelationalOperator: '<S15>/Relational Operator'
       *  RelationalOperator: '<S15>/Relational Operator3'
       *  Selector: '<S15>/Selector4'
       */
      if (rtb_RelationalOperator2 && (((uint32_T)KnockState_old[(IonAbsTdcEOA)])
           != NO_KNOCK)) {
        rtb_EnIncCylIn = 1U;
      } else {
        rtb_EnIncCylIn = (uint8_T)((IndCylRec >= KCorrIncDelay) ? 1 : 0);
      }

      /* End of Switch: '<S15>/Switch' */

      /* Chart: '<S15>/EnInc_Count_Management' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  SignalConversion generated from: '<S1>/CountCylOut'
       *  SignalConversion generated from: '<S1>/EnIncCylOut'
       *
       * Block description for '<S15>/EnInc_Count_Management':
       *  In this block are assigned to the correspond outputs the signals
       *  previously calculated
       */
      /* Gateway: EOA_fcn/Delta_Correction_Calculation/Timer_Enable_Inc/EnInc_Count_Management */
      /* During: EOA_fcn/Delta_Correction_Calculation/Timer_Enable_Inc/EnInc_Count_Management */
      /* Entry Internal: EOA_fcn/Delta_Correction_Calculation/Timer_Enable_Inc/EnInc_Count_Management */
      /* Transition: '<S22>:87' */
      /* Case ev_EOA and Recovery disabled */
      /* Case ev_EOA and strategy disabled */
      switch (KnockCorrMode) {
       case KNOCK_CORR_OFF:
        /* Transition: '<S22>:90' */
        /* Transition: '<S22>:103' */
        EnIncCylOut[(IonAbsTdcEOA)] = 0U;
        CountCylOut[(IonAbsTdcEOA)] = 0U;

        /* Transition: '<S22>:104' */
        /* Transition: '<S22>:100' */
        break;

       case KNOCK_CORR_SLEW:
        /* Transition: '<S22>:84' */
        /* Case ev_EOA and Slew modality */
        /* Transition: '<S22>:86' */
        /* Transition: '<S22>:79' */
        /* Transition: '<S22>:100' */
        break;

       default:
        /* Transition: '<S22>:88' */
        /* Case ev_EOA and Normal modality */
        /* Transition: '<S22>:96' */
        EnIncCylOut[(IonAbsTdcEOA)] = rtb_EnIncCylIn;
        CountCylOut[(IonAbsTdcEOA)] = IndCylRec;
        break;
      }

      /* End of Chart: '<S15>/EnInc_Count_Management' */

      /* Switch: '<S8>/Switch1' incorporates:
       *  Constant: '<S15>/constant1'
       *  DataTypeConversion: '<S15>/Data Type Conversion'
       *  Switch: '<S15>/Switch1'
       *
       * Block requirements for '<S8>/Switch1':
       *  1. EISB_FCA6CYL_SW_REQ_1233: The software shall sets the signal DeltaKCorrCyl (estimated delta ... (ECU_SW_Requirements#2098)
       *  2. EISB_FCA6CYL_SW_REQ_1234: The software shall sets the signal DeltaKCorrCyl (estimated delta ... (ECU_SW_Requirements#2100)
       */
      /* Transition: '<S22>:95' */
      if (rtb_KnockAct) {
        DeltaKCorrCyl = KCorrDec;
      } else if (((int32_T)rtb_EnIncCylIn) != 0) {
        /* Switch: '<S15>/Switch1' */
        DeltaKCorrCyl = KCorrInc;
      } else {
        DeltaKCorrCyl = 0;
      }

      /* End of Switch: '<S8>/Switch1' */

      /* Update for Memory: '<S1>/Memory' incorporates:
       *  Inport: '<Root>/KnockState'
       */
      memcpy((&(KnockState_old[0])), (&(KnockState[0])), (sizeof(enum_KnockState))
             << 3U);

      /* End of Outputs for SubSystem: '<Root>/EOA_fcn' */
    }

    /* Transition: '<S3>:100' */
    /* Transition: '<S3>:87' */
    break;

   default:
    /* Transition: '<S3>:83' */
    /* ev_10ms
       Recovery modality -> effects only on CntKnockCohEE according to requirements */
    IndCylRec = 0U;
    while ((IndCylRec < N_CYLINDER) && (((int32_T)FlgSyncPhased) == 0)) {
      /* Transition: '<S3>:91' */
      /* Transition: '<S3>:105' */
      if (((int32_T)VtRec[REC_KNOCKCORR_OFF_0 + IndCylRec]) == 1) {
        /* Outputs for Function Call SubSystem: '<Root>/Recovery_fcn'
         *
         * Block description for '<Root>/Recovery_fcn':
         *  In this block are implemented the recovery actions.
         */
        /* Chart: '<S6>/KnockCorrNom_Scheduler' incorporates:
         *  Constant: '<S6>/CNTKNOCKCOHINC'
         *  Constant: '<S6>/DIAG_KNOCK_COH_0'
         *  Constant: '<S6>/THRCNTKNOCKCOH'
         *  Inport: '<Root>/StDiag'
         *  SignalConversion generated from: '<S6>/CountCylOut'
         *  SignalConversion generated from: '<S6>/EnIncCylOut'
         *  SignalConversion generated from: '<S6>/CntKnockCohPOn_old'
         *  SignalConversion generated from: '<S6>/FlgCntKnockCohInc_old'
         *
         * Block description for '<S6>/KnockCorrNom_Scheduler':
         *  In this block are implemented the recovery actions.
         */
        /* Transition: '<S3>:107' */
        /* Transition: '<S3>:111' */
        /* Event: '<S3>:98' */
        /* Gateway: Recovery_fcn/KnockCorrNom_Scheduler */
        /* During: Recovery_fcn/KnockCorrNom_Scheduler */
        /* Entry Internal: Recovery_fcn/KnockCorrNom_Scheduler */
        /* Transition: '<S27>:128' */
        /* Transition: '<S27>:174' */
        EnIncCylOut[(IndCylRec)] = 0U;
        CountCylOut[(IndCylRec)] = 0U;
        if (((uint32_T)StDiag[DIAG_KNOCK_COH_0 + IndCylRec]) == FAULT) {
          /* Transition: '<S27>:127'
           * Requirements for Transition: '<S27>:127':
           *  1. EISB_FCA6CYL_SW_REQ_1846: The sotware shall sets the signal CntKnockCohEE equal to the calib... (ECU_SW_Requirements#6990)
           */
          CntKnockCohEE[(IndCylRec)] = THRCNTKNOCKCOH;

          /* Transition: '<S27>:129' */
          /* Transition: '<S27>:131' */
          /* Transition: '<S27>:136' */
          /* Transition: '<S27>:133' */
        } else {
          /* Transition: '<S27>:137' */
          if (((int32_T)FlgCntKnockCohInc[(IndCylRec)]) != 0) {
            /* Transition: '<S27>:134'
             * Requirements for Transition: '<S27>:134':
             *  1. EISB_FCA6CYL_SW_REQ_1847: In case knock control functionality is ENABLED, the recovery REC_K... (ECU_SW_Requirements#6991)
             */
            CntKnockCohEE[(IndCylRec)] = (uint8_T)(CntKnockCohPOn[(IndCylRec)] +
              CNTKNOCKCOHINC);
            if (CntKnockCohEE[(IndCylRec)] >= THRCNTKNOCKCOH) {
              /* Transition: '<S27>:130'
               * Requirements for Transition: '<S27>:130':
               *  1. EISB_FCA6CYL_SW_REQ_1848: In case knock control functionality is ENABLED, the recovery REC_K... (ECU_SW_Requirements#6992)
               */
              CntKnockCohEE[(IndCylRec)] = THRCNTKNOCKCOH;

              /* Transition: '<S27>:131' */
              /* Transition: '<S27>:136' */
              /* Transition: '<S27>:133' */
            } else {
              /* Transition: '<S27>:132' */
              /* Transition: '<S27>:133' */
            }
          } else {
            /* Transition: '<S27>:135' */
          }
        }

        /* End of Outputs for SubSystem: '<Root>/Recovery_fcn' */
        /* Transition: '<S27>:138' */
        /* Transition: '<S3>:112' */
      } else {
        /* Transition: '<S3>:109' */
      }

      /* Transition: '<S3>:114' */
      IndCylRec = (uint8_T)((int32_T)(((int32_T)IndCylRec) + 1));

      /* Transition: '<S3>:115' */
    }

    /* Transition: '<S3>:89' */
    break;
  }

  /* End of Chart: '<Root>/KnockCorrNom_Scheduler' */
  /* Transition: '<S3>:80' */
}

/*
 * Output and update for function-call system: '<Root>/KnockCorrNom_Scheduler'
 * Block description for: '<Root>/KnockCorrNom_Scheduler'
 *   Model scheduler. This stateflow manage events of PowerOn NoSync, EOA and
 *   10ms.
 */
void KnockCor_KnockCorrNom_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[4];
  int32_T i;

  /* Chart: '<Root>/KnockCorrNom_Scheduler' incorporates:
   *  TriggerPort: '<S3>/input events'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  for (i = 0; i < 4; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: KnockCorrNom_Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S3>:1' */
    i = (int32_T)Knoc_event_KnockCorrNom_PowerOn;
    Knock_chartstep_c3_KnockCorrNom(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S3>:24' */
    i = (int32_T)Knock_event_KnockCorrNom_NoSync;
    Knock_chartstep_c3_KnockCorrNom(&i);
  }

  if (rtb_inputevents[2U] == 2) {
    /* Event: '<S3>:3' */
    i = (int32_T)KnockCor_event_KnockCorrNom_EOA;
    Knock_chartstep_c3_KnockCorrNom(&i);
  }

  if (rtb_inputevents[3U] == 2) {
    /* Event: '<S3>:81' */
    i = (int32_T)KnockCo_event_KnockCorrNom_10ms;
    Knock_chartstep_c3_KnockCorrNom(&i);
  }
}

/* Model step function */
void KnockCorrNom_10ms(void)
{
  /* Chart: '<Root>/KnockCorrNom_Scheduler'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrNom_10ms' */

  /* Chart: '<Root>/KnockCorrNom_Scheduler'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  KnockCor_KnockCorrNom_Scheduler(3);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrNom_10ms' */
}

/* Model step function */
void KnockCorrNom_EOA(void)
{
  /* Chart: '<Root>/KnockCorrNom_Scheduler'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrNom_EOA' */

  /* Chart: '<Root>/KnockCorrNom_Scheduler'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  KnockCor_KnockCorrNom_Scheduler(2);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrNom_EOA' */
}

/* Model step function */
void KnockCorrNom_NoSync(void)
{
  /* Chart: '<Root>/KnockCorrNom_Scheduler'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrNom_NoSync' */

  /* Chart: '<Root>/KnockCorrNom_Scheduler'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  KnockCor_KnockCorrNom_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrNom_NoSync' */
}

/* Model step function */
void KnockCorrNom_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrNom_PowerOn' incorporates:
   *  Chart: '<Root>/KnockCorrNom_Scheduler'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */

  /* Chart: '<Root>/KnockCorrNom_Scheduler'
   *
   * Block description for '<Root>/KnockCorrNom_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  KnockCor_KnockCorrNom_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrNom_PowerOn' */
}

/* Model initialize function */
void KnockCorrNom_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

int16_T DeltaKCorrCyl;
uint8_T FlgKCohInc;
int16_T KCorrDec;
int16_T KCorrInc;
uint8_T KCorrIncDelay;
uint8_T NCylKnockCoh;
uint8_T CntKnockCohEE[N_CYL_MAX];
uint8_T CntKnockCohPOn[N_CYL_MAX];
uint8_T FlgCntKnockCohInc[N_CYL_MAX];
uint8_T FlgKCohIncLev1[N_CYL_MAX];
uint8_T KCohDiagCnt[N_CYL_MAX];
uint8_T CountCylOut[N_CYL_MAX];
uint8_T EnIncCylOut[N_CYL_MAX];
void KnockCorrNom_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    CntKnockCohEE[idx] = 0u;
    CntKnockCohPOn[idx] = 0u;
    FlgCntKnockCohInc[idx] = 0u;
    FlgKCohIncLev1[idx] = 0u;
    KCohDiagCnt[idx] = 0u;
    CountCylOut[idx] = 0u;
    EnIncCylOut[idx] = 0u;
  }

  DeltaKCorrCyl = 0;
  FlgKCohInc = 0u;
  KCorrDec = 0;
  KCorrInc = 0;
  KCorrIncDelay = 0u;
  NCylKnockCoh = 0u;
}

void KnockCorrNom_PowerOn(void)
{
  KnockCorrNom_Stub();
}

void KnockCorrNom_NoSync(void)
{
  KnockCorrNom_Stub();
}

void KnockCorrNom_EOA(void)
{
  KnockCorrNom_Stub();
}

void KnockCorrNom_10ms(void)
{
  KnockCorrNom_Stub();
}

#endif                                 /* _BUILD_KNOCKCORRNOM_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/