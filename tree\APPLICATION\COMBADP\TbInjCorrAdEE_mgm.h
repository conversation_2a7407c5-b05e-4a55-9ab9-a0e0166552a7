/******************************************************************************/
/* $HeadURL::                                                              $  */
/* $Revision::                                                             $  */
/* $Date::                                                                 $  */
/* $Author::                                                               $  */
/******************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CombAdp
**  Filename        :  TbInjCorrAdEE_mgm.h
**  Created on      :  10-sep-2021 08:00:00
**  Original author :  GiuseppeR
******************************************************************************/
/*****************************************************************************
**
**                        TbInjCorrAdEE_mgm.h Description
**
**  Header file for management of ee-adaptive coefficients for CombAdp
 *
******************************************************************************/
#ifndef _BUILD_TBINJCORRADEE_MGM_H_
#define _BUILD_TBINJCORRADEE_MGM_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/

#include "rtwtypes.h"

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
#define MIN(a, b) (((a) < (b))? ((a)) : ((b)))

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
#define TBINJCORRADEE_COLUMN_NUMBER 5u
#define TBINJCORRADEE_ROW_NUMBER   96u
#define TBINJCORRADEE_LENGTH      480u

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/

extern uint16_T TbInjCorrAdEE[480];
/* Tables of the adaptive coefficients for all cylinders */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Get_TbInjCorrAdEE
**
**   Description:
**    return the value of a cell of TbInjCorrAdEE 
**
**   Parameters :
**    uint8_T   row index
**    uint8_T   column index
**
**   Returns:
**    int16_T    cell value
**
******************************************************************************/
uint16_T Get_TbInjCorrAdEE(uint8_T rowIndex, uint8_T columnIndex);

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Set_TbInjCorrAdEE
**
**   Description:
**    assign the value of a cell of TbInjCorrAdEE 
**
**   Parameters :
**    uint8_T   row index
**    uint8_T   column index
**    int16_T   cell value
**
**   Returns:
**    void
**
******************************************************************************/
void Set_TbInjCorrAdEE(uint8_T rowIndex, uint8_T columnIndex, uint16_T value);

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Interpolate_TbInjCorrAdEE
**
**   Description:
**    return the value of direct interpolation on TbInjCorrAdEE table
**
**   Parameters :
**    uint16_T   row index
**    uint16_T   row ratio
**    uint16_T   column index
**    uint16_T   column ratio
**
**   Returns:
**    int16_T    interpolated value
**
******************************************************************************/
uint16_T Interpolate_TbInjCorrAdEE(uint16_T IdX, uint16_T RatioX, uint16_T IdY, uint16_T RatioY);

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Reset_TbInjCorrAdEE
**
**   Description:
**    reset TbInjCorrAdEE table
**
**   Parameters :

**
**   Returns:
**    int16_T    interpolated value
**
******************************************************************************/
void Reset_TbInjCorrAdEE(void);  
  
#endif /* _BUILD_TBINJCORRADEE_MGM_H_ */

