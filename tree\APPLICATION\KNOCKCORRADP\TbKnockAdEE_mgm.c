/******************************************************************************/
/* $HeadURL::                                                              $  */
/* $Revision::                                                             $  */
/* $Date::                                                                 $  */
/* $Author::                                                               $  */
/******************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  KnockCorrAdp
**  Filename        :  TbKnockAdEE_mgm.c
**  Created on      :  17-may-2021 08:00:00
**  Original author :  GiuseppeR
******************************************************************************/
/*****************************************************************************
**
**                        TbKnockAdEE_mgm.c Description
**
**  Management of ee-adaptive coefficients for KnockCorrAdp
 *
******************************************************************************/

#ifndef _BUILD_TBKNOCKADEE_MGM_C_
#define _BUILD_TBKNOCKADEE_MGM_C_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "TbKnockAdEE_mgm.h"

static int16_T interp(int16_T yL, int16_T yR, uint16_T RatioX);

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Get_TbKnockAdEE
**
**   Description:
**    return the value of a cell of TbKnockAdEE 
**
**   Parameters :
**    uint8_T   row index
**    uint8_T   column index
**
**   Returns:
**    int16_T    cell value
**
******************************************************************************/
int16_T Get_TbKnockAdEE(uint8_T rowIndex, uint8_T columnIndex)
{
    int16_T result;
    uint16_T index = (rowIndex*TBKNOCKADEE_COLUMN_NUMBER) + columnIndex;

    if ((index < 0u) || (index >= TBKNOCKADEE_LENGTH))
    {
        result = 0;
    }
    else
    {
        result = TbKnockAdEE[index];
    }
    return result;
}

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Set_TbKnockAdEE
**
**   Description:
**    assign the value of a cell of TbKnockAdEE 
**
**   Parameters :
**    uint8_T   row index
**    uint8_T   column index
**    int16_T   cell value
**
**   Returns:
**    void
**
******************************************************************************/
void Set_TbKnockAdEE(uint8_T rowIndex, uint8_T columnIndex, int16_T value)
{
    uint16_T index = (rowIndex*TBKNOCKADEE_COLUMN_NUMBER) + columnIndex;
    if ((index >= 0u) && (index < TBKNOCKADEE_LENGTH))
    {
        if ((value > MIN_TBKNOCKADEE) && (value <= MAX_TBKNOCKADEE))
        {
            TbKnockAdEE[index] = value;
        }
    }
}

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Interpolate_TbKnockAdEE
**
**   Description:
**    return the value of direct interpolation on TbKnockAdEE table
**
**   Parameters :
**    uint16_T   row index
**    uint16_T   row ratio
**    uint16_T   column index
**    uint16_T   column ratio
**
**   Returns:
**    int16_T    interpolated value
**
******************************************************************************/
int16_T Interpolate_TbKnockAdEE(uint16_T IdX, uint16_T RatioX, uint16_T IdY, uint16_T RatioY)
{
    int16_T OutZ0;
    int16_T OutZ1;
    uint16_T row0 = IdX * TBKNOCKADEE_COLUMN_NUMBER;
    uint16_T row1 = (IdX+1) * TBKNOCKADEE_COLUMN_NUMBER;
    uint16_T col0 = IdY;
    uint16_T col1 = (IdY+1);
  
    if(IdX == (TBKNOCKADEE_ROW_NUMBER-1))
    {
        OutZ0 = TbKnockAdEE[row0+col0];
        OutZ1 = TbKnockAdEE[MIN(row0+col1,TBKNOCKADEE_LENGTH-1u)];
    }
    else
    {
        OutZ0 = interp(TbKnockAdEE[row0+col0], TbKnockAdEE[row1+col0], RatioX);
        OutZ1 = interp(TbKnockAdEE[row0+col1], TbKnockAdEE[MIN(row1+col1,TBKNOCKADEE_LENGTH-1u)], RatioX);
    }   
    
    return interp( OutZ0,   OutZ1,   RatioY);
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : interp
**
**   Description:
**    Interpolate along one axis
**
**   Parameters :
**    [yL] left value
**    [yR] right value
**    [ratioX] ratio
**    [out] interpolated value
**
**   SW Requirements:
**    NA
**
**
******************************************************************************/

static int16_T interp(int16_T yL, int16_T yR, uint16_T RatioX)
{
    int16_T outValue;
    if(RatioX == MAX_uint16_T)
    {
        outValue = yR;    
    }
    else if(RatioX == 0)
    {
        outValue = yL;    
    }
    else
    {
        int32_T tmp;
        
        tmp = (int32_T)yR - (int32_T)yL;
        tmp *= (int32_T)RatioX;
        tmp >>= 16;
        tmp += (int32_T)yL;
        
        outValue = (int16_T)tmp;  
    }
    return outValue;
}

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/


#endif /* _BUILD_TBKNOCKADEE_MGM_C_ */
