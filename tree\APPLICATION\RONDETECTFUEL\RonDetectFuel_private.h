/*****************************************************************************************************************/
/* $HeadURL:: https://85.42.57.219/svn/Rep_GeOr/Spec_ECU/Application/RonDetectFuel/ED_001/SWE3/branches/SW_00#$  */
/* $Revision:: 262624                                                                                         $  */
/* $Date:: 2025-05-28 10:33:15 +0300 (Wed, 28 May 2025)                                                       $  */
/* $Author:: simsekoglui                                                                                      $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      RonDetectFuel_private.h
 **  Date:          28-May-2025
 **
 **  Model Version: 1.1020
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_RonDetectFuel_private_h_
#define RTW_HEADER_RonDetectFuel_private_h_
#include "rtwtypes.h"
#include "RonDetectFuel_out.h"

/* Includes for objects with custom storage classes. */
#include "RonDetectMgm_out.h"
#include "DigIn_out.h"
#include "RonDetectEst_out.h"
#include "syncmgm_out.h"
#include "rondetectmgm_out.h"
/* Includes for extra files. */
#include "CanMgmIn_out.h"
#include "DiagCanMgm_out.h"
#include "ETPU_EngineDefs.h"
#include "RonDetectEn_out.h"

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/
extern void RonDete_RonDetectFuel_Scheduler(int32_T controlPortIdx);

#endif                                 /* RTW_HEADER_RonDetectFuel_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/